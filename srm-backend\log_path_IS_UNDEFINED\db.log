[INFO ] [2025-08-05 09:04:52.909] - select '1' from dual
[INFO ] [2025-08-05 09:04:53.123] - select '1' from dual
[INFO ] [2025-08-05 09:04:53.280] - select '1' from dual
[INFO ] [2025-08-05 09:04:53.424] - select '1' from dual
[INFO ] [2025-08-05 09:04:53.587] - select '1' from dual
[INFO ] [2025-08-05 09:06:49.804] - select '1' from dual
[INFO ] [2025-08-05 09:06:50.628] - SELECT id, tenant_p_id, tenant_id, enquiry_code, enquiry_name, enquiry_explain, bid_plan_id, bid_plan_code, enquiry_rule, dept_name, tenant_name, remark, enquiry_type, goods_type, enquiry_mode, enquiry_stat, enquiry_way, ask_date, complete_date, order_date, is_urgent, delete_flag, wf_id, wf_status, version_num, dep_name, pur_org_name, info_type, werks, plans, allow_bid_count, rate_code, bstae, auto_source, tenant_sees_rank_and_price, vendor_sees_rank_and_price, vendor_sees_final_rank_price, is_part_update, enquiry_round, expand_remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM im_enquiry WHERE (enquiry_mode = 3 AND enquiry_stat = 2 AND ask_date > '2025-08-05 09:06:48')
[INFO ] [2025-08-05 09:06:50.755] - SELECT id, tenant_p_id, tenant_id, enquiry_code, enquiry_name, enquiry_explain, bid_plan_id, bid_plan_code, enquiry_rule, dept_name, tenant_name, remark, enquiry_type, goods_type, enquiry_mode, enquiry_stat, enquiry_way, ask_date, complete_date, order_date, is_urgent, delete_flag, wf_id, wf_status, version_num, dep_name, pur_org_name, info_type, werks, plans, allow_bid_count, rate_code, bstae, auto_source, tenant_sees_rank_and_price, vendor_sees_rank_and_price, vendor_sees_final_rank_price, is_part_update, enquiry_round, expand_remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM im_enquiry WHERE (enquiry_mode = 3 AND (enquiry_stat = 2 OR enquiry_stat = 3) AND complete_date > '2025-08-05 09:06:50')
[INFO ] [2025-08-05 09:06:53.096] - /* ping */ SELECT 1
[INFO ] [2025-08-05 09:13:46.139] - <0><1952538484100780032> select '1' from dual
[INFO ] [2025-08-05 09:13:46.183] - <0><1952538484100780032> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-05 09:13:46.269] - <0><1952538484100780032> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-05 09:13:46.369] - <0><1952538484100780032> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-05 09:13:47.454] - <0><1952538491264651264> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-05 09:13:47.487] - <0><1952538491264651264> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-05 09:13:48.112] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-05 09:13:48.131] - <0><1952538493563129856> select '1' from dual
[INFO ] [2025-08-05 09:13:48.131] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-05 09:13:48.131] - <0><1952538493563129856> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-05 09:13:48.147] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:13:48.165] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-05 09:13:48.179] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:13:48.198] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-05 09:13:48.198] - <0><1952538494536208384> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-05 09:13:48.214] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:13:48.214] - <0><1952538494536208384> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 09:13:48.214] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-05 09:13:48.231] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:13:48.248] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-05 09:13:48.267] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:13:48.281] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-05 09:13:48.299] - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:13:49.098] - <0><1952538497715490816> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-05 09:13:49.098] - <0><1952538497690324992> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 09:13:49.114] - <0><1952538497715490816> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-05 09:13:49.130] - <0><1952538497715490816> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-05 09:13:49.176] - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-05 09:13:49.176] - <0><1952538497690324992> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 09:13:49.192] - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-05 09:13:49.208] - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-05 09:13:49.224] - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-05 09:13:49.259] - <0><1952538497715490816> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-05 09:13:49.275] - <0><1952538497715490816> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-05 09:13:49.283] - <0><1952538497715490816> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-05 09:13:49.498] - <0><1952538497715490816> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-05 09:13:49.515] - <0><1952538497715490816> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-05 09:13:49.532] - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-05 09:13:49.548] - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 09:13:49.564] - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-05 09:13:49.582] - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-05 09:13:49.598] - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-05 09:13:49.666] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.681] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.698] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.715] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.732] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.750] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.774] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.783] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.810] - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:13:49.878] - <0><1952538497715490816> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-05 09:13:49.899] - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:13:49.921] - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:13:49.940] - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:13:49.949] - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:27:24.727] - <0><1952541919260921856> select '1' from dual
[INFO ] [2025-08-05 09:27:25.242] - <0><1952541921269993472> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-05 09:27:25.306] - <0><1952541921269993472> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-05 09:27:25.941] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-05 09:27:25.973] - <0><1952541924294086658> select '1' from dual
[INFO ] [2025-08-05 09:27:25.977] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-05 09:27:25.988] - <0><1952541924294086658> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-05 09:27:25.992] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:27:26.018] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-05 09:27:26.075] - <0><1952541924822568960> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-05 09:27:26.076] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:27:26.090] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-05 09:27:26.091] - <0><1952541924822568960> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 09:27:26.103] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:27:26.152] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-05 09:27:26.181] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:27:26.195] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-05 09:27:26.211] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:27:26.284] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-05 09:27:26.299] - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 09:27:26.875] - <0><1952541928198983680> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-05 09:27:26.875] - <0><1952541928198983681> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 09:27:26.890] - <0><1952541928198983680> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-05 09:27:26.890] - <0><1952541928198983681> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 09:27:26.917] - <0><1952541928198983680> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-05 09:27:26.967] - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-05 09:27:26.978] - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-05 09:27:26.995] - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-05 09:27:27.049] - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-05 09:27:27.062] - <0><1952541928198983680> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-05 09:27:27.083] - <0><1952541928198983680> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-05 09:27:27.137] - <0><1952541928198983680> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-05 09:27:27.238] - <0><1952541928198983680> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-05 09:27:27.268] - <0><1952541928198983680> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-05 09:27:27.328] - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-05 09:27:27.347] - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 09:27:27.381] - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-05 09:27:27.411] - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-05 09:27:27.458] - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-05 09:27:27.601] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.623] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.681] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.704] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.770] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.789] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.843] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.872] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.915] - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:27:27.949] - <0><1952541928198983680> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-05 09:27:27.967] - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:27:28.024] - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:27:28.055] - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:27:28.113] - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 09:43:22.212] - <0><1952545935365722112> select '1' from dual
[INFO ] [2025-08-05 09:43:24.710] - <0><1952545945780178944> select * from sys_user where user_code = 'SU20250604641_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-05 09:43:24.724] - <0><1952545945780178944> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
[INFO ] [2025-08-05 09:43:24.740] - <0><1952545945780178944> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
[INFO ] [2025-08-05 09:43:25.685] - <0><1952545948586168320> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
[INFO ] [2025-08-05 09:43:25.718] - <0><1952545948586168320> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
[INFO ] [2025-08-05 09:43:26.154] - <0><1952545951891279876> select '1' from dual
[INFO ] [2025-08-05 09:43:26.154] - <0><1952545951891279873> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 09:43:26.166] - <0><1952545951891279876> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 09:43:26.167] - <0><1952545951891279874> select '1' from dual
[INFO ] [2025-08-05 09:43:26.197] - <0><1952545951891279874> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 09:43:26.266] - <0><1952545952356847616> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND parent_id = 0)
[INFO ] [2025-08-05 09:43:26.287] - <0><1952545952356847616> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 09:43:26.705] - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
[INFO ] [2025-08-05 09:43:26.715] - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
[INFO ] [2025-08-05 09:43:26.730] - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
[INFO ] [2025-08-05 09:43:26.736] - <0><1952545954185564160> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 09:43:26.741] - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
[INFO ] [2025-08-05 09:43:26.758] - <0><1952545954189758464> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
[INFO ] [2025-08-05 09:43:26.770] - <0><1952545954189758464> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
[INFO ] [2025-08-05 09:43:26.771] - <0><1952545954185564160> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 09:43:26.783] - <0><1952545954185564161> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
[INFO ] [2025-08-05 09:43:26.785] - <0><1952545954189758464> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
[INFO ] [2025-08-05 09:43:26.842] - <0><1952545954189758464> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
[INFO ] [2025-08-05 09:43:26.865] - <0><1952545954189758464> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
[INFO ] [2025-08-05 09:43:26.871] - <0><1952545954303004672> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25409 AND bvni.is_read = '0'
[INFO ] [2025-08-05 09:43:26.876] - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
[INFO ] [2025-08-05 09:43:26.889] - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 09:43:26.906] - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
[INFO ] [2025-08-05 09:43:26.923] - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
[INFO ] [2025-08-05 09:43:26.957] - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
[INFO ] [2025-08-05 09:43:26.989] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.007] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.024] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.043] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.062] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.077] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.094] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.112] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.130] - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 09:43:27.151] - <0><1952545954189758464> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
[INFO ] [2025-08-05 09:43:27.168] - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 09:43:27.189] - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 09:43:27.205] - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 09:43:27.222] - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 09:43:30.018] - <0><1952545967699611648> SELECT COUNT(*) FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905'
[INFO ] [2025-08-05 09:43:30.112] - <0><1952545967699611648> SELECT op.tenant_id, op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name, op.dept_name, op.order_date, op.dep_code, op.bsart, op.pur_name, op.order_flag, op.sync_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.tenant_name, op.currency_name, op.total_amount, op.order_remark, op.publish_date, op.purchasing_group, op.reserved06, op.purchasing_group, opi.seq, opi.item_stat, op.id sale_id, opi.id sale_item_id, op.pur_no sale_no, (opi.order_num - opi.make_num) dev_num, opi.is_close, opi.erp_change_type, opi.goods_id, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model, opi.delivery_stat, opi.order_num, opi.matched_plan_num, opi.make_num, opi.fix_num, opi.wait_num, opi.receive_num, opi.refund_num, opi.ref_ded_num, opi.erp_master_num, opi.erp_reject_num, opi.ret_ded_num, opi.rate_name, opi.delivery_date, opi.tax_price, opi.gst_price, opi.gst_amount, opi.source_id, opi.uom_id, opi.uom_code, opi.uom_name, op.pur_id, op.pur_Name, op.dept_id, op.dept_name, op.dept_code, opi.delivery_type, opi.rate_id, opi.rate_val, opi.currency_id, opi.currency_name, opi.big_pack_standard_num, opi.small_pack_standard_num, opi.big_pack_label_num, opi.small_pack_label_num, opi.big_pack_mantissa, opi.small_pack_mantissa FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905' ORDER BY op.id DESC, opi.seq ASC LIMIT 20
[INFO ] [2025-08-05 09:43:30.261] - <0><1952545967699611648> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1067290, 1067312, 1065924, 1066065, 1065924, 1066065, 1103821, 1103810, 244986, 244983))
[INFO ] [2025-08-05 09:43:30.660] - <0><1952545967699611648> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1065924, 1065924, 1066065, 245043, 1065924, 1066065, 1065924, 1066065, 1066070))
[INFO ] [2025-08-05 09:46:36.685] - <0><1952546751006855168> select '1' from dual
[INFO ] [2025-08-05 09:46:36.708] - <0><1952546751006855168> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 09:46:36.976] - <0><1952546752109957120> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'order_pur' AND head_id = '24377' AND line_id = '0')
[INFO ] [2025-08-05 09:46:37.196] - <0><1952546752688771072> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE (pur_id = 24377 AND delete_flag = 0) ORDER BY seq ASC
[INFO ] [2025-08-05 09:46:37.214] - <0><1952546752688771072> select * from base_config where param_key = 'orderReply' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 09:46:37.284] - <0><1952546752688771072> select '1' from dual
[INFO ] [2025-08-05 09:46:37.996] - <0><1952546752688771072> INSERT INTO base_oper_log (tenant_id, oper_bill, oper_type, soure_head_id, attr1, attr2, remark, create_id, creater, create_date, modifi_id, modifier, modify_date) VALUES (25409, 3, 10, 24377, 'PO2507310005', 'SU20250604641_admin', '查看采购订单PO2507310005', 30410, 'SU20250604641_admin', '2025-08-05 09:46:37', 30410, 'SU20250604641_admin', '2025-08-05 09:46:37')
[INFO ] [2025-08-05 10:36:04.838] - <0><1952559200359632896> select '1' from dual
[INFO ] [2025-08-05 10:36:05.120] - <0><1952559201479512064> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
[INFO ] [2025-08-05 10:36:05.168] - <0><1952559201479512064> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
[INFO ] [2025-08-05 10:36:05.708] - <0><1952559203991900160> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 10:36:05.715] - <0><1952559204017065986> select '1' from dual
[INFO ] [2025-08-05 10:36:05.723] - <0><1952559204017065985> select '1' from dual
[INFO ] [2025-08-05 10:36:05.738] - <0><1952559204017065986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 10:36:05.743] - <0><1952559204017065985> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 10:36:06.104] - <0><1952559205388603392> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND parent_id = 0)
[INFO ] [2025-08-05 10:36:06.120] - <0><1952559205388603392> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 10:36:07.283] - <0><1952559210585346048> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25409 AND bvni.is_read = '0'
[INFO ] [2025-08-05 10:36:07.299] - <0><1952559210585346049> SELECT COUNT(*) FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905'
[INFO ] [2025-08-05 10:36:07.346] - <0><1952559210585346049> SELECT op.tenant_id, op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name, op.dept_name, op.order_date, op.dep_code, op.bsart, op.pur_name, op.order_flag, op.sync_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.tenant_name, op.currency_name, op.total_amount, op.order_remark, op.publish_date, op.purchasing_group, op.reserved06, op.purchasing_group, opi.seq, opi.item_stat, op.id sale_id, opi.id sale_item_id, op.pur_no sale_no, (opi.order_num - opi.make_num) dev_num, opi.is_close, opi.erp_change_type, opi.goods_id, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model, opi.delivery_stat, opi.order_num, opi.matched_plan_num, opi.make_num, opi.fix_num, opi.wait_num, opi.receive_num, opi.refund_num, opi.ref_ded_num, opi.erp_master_num, opi.erp_reject_num, opi.ret_ded_num, opi.rate_name, opi.delivery_date, opi.tax_price, opi.gst_price, opi.gst_amount, opi.source_id, opi.uom_id, opi.uom_code, opi.uom_name, op.pur_id, op.pur_Name, op.dept_id, op.dept_name, op.dept_code, opi.delivery_type, opi.rate_id, opi.rate_val, opi.currency_id, opi.currency_name, opi.big_pack_standard_num, opi.small_pack_standard_num, opi.big_pack_label_num, opi.small_pack_label_num, opi.big_pack_mantissa, opi.small_pack_mantissa FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905' ORDER BY op.id DESC, opi.seq ASC LIMIT 20
[INFO ] [2025-08-05 10:36:07.402] - <0><1952559210585346049> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1067290, 1067312, 1065924, 1066065, 1065924, 1066065, 1103821, 1103810, 244986, 244983))
[INFO ] [2025-08-05 10:36:07.455] - <0><1952559210585346049> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1065924, 1065924, 1066065, 245043, 1065924, 1066065, 1065924, 1066065, 1066070))
[INFO ] [2025-08-05 10:36:26.211] - <0><1952559289681530880> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:36:26.270] - <0><1952559289681530880> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:36:27.271] - <0><1952559294333014016> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2434' AND line_id = '0')
[INFO ] [2025-08-05 10:36:27.583] - <0><1952559295624859648> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
[INFO ] [2025-08-05 10:36:27.598] - <0><1952559295624859648> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:36:27.625] - <0><1952559295624859648> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
[INFO ] [2025-08-05 10:36:33.924] - <0><1952559322292244480> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:36:33.961] - <0><1952559322292244480> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:37:34.459] - <0><1952559576269934592> select '1' from dual
[INFO ] [2025-08-05 10:37:37.034] - <0><1952559587003158528> select * from sys_user where user_code = 'sys' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-05 10:37:37.051] - <0><1952559587003158528> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 1)
[INFO ] [2025-08-05 10:37:37.069] - <0><1952559587003158528> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = NULL
[INFO ] [2025-08-05 10:37:37.963] - <0><1952559590916444160> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 1)
[INFO ] [2025-08-05 10:37:38.011] - <0><1952559590916444160> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = NULL
[INFO ] [2025-08-05 10:37:38.436] - <0><1952559592938098688> select '1' from dual
[INFO ] [2025-08-05 10:37:38.436] - <0><1952559592933904386> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 1 ORDER BY id ASC
[INFO ] [2025-08-05 10:37:38.437] - <0><1952559592938098689> select '1' from dual
[INFO ] [2025-08-05 10:37:38.453] - <0><1952559592938098688> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 1 ORDER BY id ASC
[INFO ] [2025-08-05 10:37:38.453] - <0><1952559592933904386> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 1 AND status = 1) AND tenant_id = 1 ORDER BY id ASC
[INFO ] [2025-08-05 10:37:38.453] - <0><1952559592938098689> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 1 AND status = 1) AND tenant_id = 1 ORDER BY id ASC
[INFO ] [2025-08-05 10:37:38.598] - <0><1952559593625964544> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 1 AND parent_id = 0)
[INFO ] [2025-08-05 10:37:38.615] - <0><1952559593625964544> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 1 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 10:37:39.035] - <0><1952559595425320960> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 1 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 10:37:39.220] - <0><1952559596234821632> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 1 AND bv.vendor_stat = 1
[INFO ] [2025-08-05 10:37:39.238] - <0><1952559596234821632> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 1 AND bv.wf_status = 0
[INFO ] [2025-08-05 10:37:39.254] - <0><1952559596234821632> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 1 AND bv.wf_status = 2
[INFO ] [2025-08-05 10:37:39.270] - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 1
[INFO ] [2025-08-05 10:37:39.284] - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 2
[INFO ] [2025-08-05 10:37:39.297] - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 3
[INFO ] [2025-08-05 10:37:39.310] - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 4
[INFO ] [2025-08-05 10:37:39.324] - <0><1952559596234821632> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 1 and bsi.item_stat = 1
[INFO ] [2025-08-05 10:37:39.340] - <0><1952559596234821632> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 1 and bsi.item_stat = 2
[INFO ] [2025-08-05 10:37:39.354] - <0><1952559596234821632> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 1 and bsi.item_stat = 3
[INFO ] [2025-08-05 10:37:39.411] - <0><1952559596234821632> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 1 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 1
[INFO ] [2025-08-05 10:37:39.435] - <0><1952559596234821632> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 1 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 1 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 1 AND op.pur_id = 1
[INFO ] [2025-08-05 10:37:39.454] - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 3
[INFO ] [2025-08-05 10:37:39.473] - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 10:37:39.490] - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 4
[INFO ] [2025-08-05 10:37:39.510] - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 5
[INFO ] [2025-08-05 10:37:39.528] - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1
[INFO ] [2025-08-05 10:37:39.573] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.594] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.614] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.634] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.651] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.670] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.689] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.712] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.733] - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:37:39.755] - <0><1952559596234821632> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 1
[INFO ] [2025-08-05 10:37:39.778] - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 1 AND dis.tenant_id = 1
[INFO ] [2025-08-05 10:37:39.796] - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 2 AND dis.tenant_id = 1
[INFO ] [2025-08-05 10:37:39.814] - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 3 AND dis.tenant_id = 1
[INFO ] [2025-08-05 10:37:39.835] - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 4 AND dis.tenant_id = 1
[INFO ] [2025-08-05 10:37:43.997] - <0><1952559615767695360> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = '0') ORDER BY order_num ASC
[INFO ] [2025-08-05 10:37:44.736] - <0><1952559617843875840> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = '0') ORDER BY order_num ASC
[INFO ] [2025-08-05 10:38:08.169] - <0><1952559717647339520> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE id = 14383
[INFO ] [2025-08-05 10:38:08.334] - <0><1952559717647339521> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = 0 AND is_valid = 1) ORDER BY parent_id, order_num ASC
[INFO ] [2025-08-05 10:38:21.646] - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE id = 14383
[INFO ] [2025-08-05 10:38:21.690] - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (menu_path_ids LIKE '/12947/13437/14383/%') ORDER BY menu_type ASC
[INFO ] [2025-08-05 10:38:21.706] - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE id = 13437
[INFO ] [2025-08-05 10:38:21.744] - <0><1952559773985230848> UPDATE sys_menu SET parent_id = 13437, menu_name = '批量确认收样', menu_perms = 'base:sample:confirmRecSample', menu_type = 2, is_system = 0, is_back = 0, is_blank = 0, order_num = 5, short_cut_url = '', is_valid = 1, is_hand = 1, ent_type = '1', menu_path_ids = '/12947/13437/14383/', menu_path_names = '/基础管理/送样通知单/批量确认收样/', create_id = 1, creater = 'sys', create_date = '2025-08-04 16:26:42', modifi_id = 1, modifier = 'sys', modify_date = '2025-08-05 10:38:21' WHERE id = 14383
[INFO ] [2025-08-05 10:38:21.777] - <0><1952559773985230848> UPDATE sys_menu SET is_back = 0, is_valid = 1, ent_type = '1', menu_path_ids = getMenuId(id), menu_path_names = getMenuName(id) WHERE id = 14383
[INFO ] [2025-08-05 10:38:21.805] - <0><1952559773985230848> UPDATE sys_menu SET is_back = 0, is_valid = 1, ent_type = '1', menu_path_ids = getMenuId(id), menu_path_names = getMenuName(id) WHERE id = 14383
[INFO ] [2025-08-05 10:38:21.836] - <0><1952559773985230848> UPDATE sys_menu_ent sme SET menu_path_ids = (SELECT menu_path_ids FROM sys_menu WHERE id = sme.menu_id), menu_path_names = (SELECT menu_path_names FROM sys_menu WHERE id = sme.menu_id) WHERE tenant_id = 0
[INFO ] [2025-08-05 10:38:21.862] - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_valid = 1 AND id = 14383)
[INFO ] [2025-08-05 10:38:21.953] - <0><1952559773985230848> DELETE FROM sys_menu_ent WHERE (menu_path_ids LIKE '/12947/13437/14383/%')
[INFO ] [2025-08-05 10:39:04.206] - <0><1952559773985230848> INSERT INTO sys_menu_ent (tenant_id, menu_id, menu_path_ids, menu_path_names, create_date) SELECT se.id, sm.id, sm.menu_path_ids, menu_path_names, now() FROM sys_menu sm, sys_ent se WHERE se.item_code != 'sys' AND INSTR(sm.ent_type, se.ent_type) > 0 AND sm.is_valid = 1 AND sm.menu_path_ids LIKE CONCAT('/12947/13437/14383/', '%') AND se.is_valid = 1
[INFO ] [2025-08-05 10:39:06.616] - <0><1952559962280120320> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = '0') ORDER BY order_num ASC
[INFO ] [2025-08-05 10:43:05.091] - <0><1952560926089236480> select '1' from dual
[INFO ] [2025-08-05 10:43:08.323] - <0><1952560944636448768> select '1' from dual
[INFO ] [2025-08-05 10:43:13.174] - <0><1952560996805201920> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-05 10:43:13.211] - <0><1952560996805201920> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-05 10:43:13.277] - <0><1952560996805201920> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-05 10:43:15.290] - <0><1952561005495799808> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-05 10:43:15.380] - <0><1952561005495799808> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
[INFO ] [2025-08-05 10:43:16.265] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-05 10:43:16.349] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
[INFO ] [2025-08-05 10:43:16.355] - <0><1952561009371336704> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
[INFO ] [2025-08-05 10:43:16.389] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 10:43:16.409] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
[INFO ] [2025-08-05 10:43:16.426] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 10:43:16.442] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
[INFO ] [2025-08-05 10:43:16.459] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 10:43:16.474] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
[INFO ] [2025-08-05 10:43:16.492] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 10:43:16.492] - <0><1952561010843537408> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
[INFO ] [2025-08-05 10:43:16.513] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
[INFO ] [2025-08-05 10:43:16.513] - <0><1952561010843537408> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 10:43:16.557] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 10:43:16.574] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
[INFO ] [2025-08-05 10:43:16.599] - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
[INFO ] [2025-08-05 10:43:17.071] - <0><1952561013251067904> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-05 10:43:17.073] - <0><1952561013251067905> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 10:43:17.093] - <0><1952561013251067905> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 10:43:17.095] - <0><1952561013251067904> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-05 10:43:17.129] - <0><1952561013251067904> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-05 10:43:17.156] - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-05 10:43:17.172] - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-05 10:43:17.197] - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-05 10:43:17.226] - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-05 10:43:17.265] - <0><1952561013251067904> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-05 10:43:17.306] - <0><1952561013251067904> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-05 10:43:17.362] - <0><1952561013251067904> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-05 10:43:17.626] - <0><1952561013251067904> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-05 10:43:17.676] - <0><1952561013251067904> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-05 10:43:17.728] - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-05 10:43:17.749] - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 10:43:17.779] - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-05 10:43:17.850] - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-05 10:43:17.933] - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-05 10:43:18.145] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.187] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.220] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.266] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.326] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.388] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.466] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.555] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.642] - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:18.706] - <0><1952561013251067904> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-05 10:43:18.742] - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:18.810] - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:18.895] - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:19.007] - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:21.446] - <0><1952561031504678912> SELECT COUNT(*) FROM base_sample bs WHERE bs.tenant_id = 24739
[INFO ] [2025-08-05 10:43:21.552] - <0><1952561031504678912> SELECT bs.id, bs.tenant_id, bs.vendor_id, bs.vendor_code, bs.vendor_erp_code, bs.vendor_name, bs.dept_id, bs.dept_code, bs.dept_name, bs.sample_no, bs.sample_date, bs.sample_stat, bs.source_id, bs.source_no, bs.source_type, bs.demand_class_type, bs.is_need_up_file, bs.is_valid, bs.delete_flag, bs.return_remark, bs.is_return, bs.return_date, bs.remark, bs.create_id, bs.creater, bs.create_date, bs.modifi_id, bs.modifier, bs.modify_date, bs.tenant_name, bs.applicant, bs.apply_dept_name, bs.apply_date, bsi.id item_id, bsi.goods_id, bsi.goods_code, bsi.goods_erp_code, bsi.goods_name, bsi.goods_model, bsi.demand_date, bsi.demand_qty, bsi.reply_quantity, bsi.reply_delivery_date, bsi.reply_state, bsi.pur_name, bsi.goods_num, bsi.item_stat, bsi.case_stat, bsi.case_date, bsi.vendor_remark, bsi.remark item_remark, (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = bs.id) if_inspection FROM base_sample bs LEFT JOIN base_sample_item bsi ON bs.id = bsi.sample_id WHERE bs.tenant_id = 24739 ORDER BY bs.id DESC LIMIT 20
[INFO ] [2025-08-05 10:43:26.404] - <0><1952561052379729921> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
[INFO ] [2025-08-05 10:43:26.409] - <0><1952561052379729920> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 10:43:26.424] - <0><1952561052379729921> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
[INFO ] [2025-08-05 10:43:26.425] - <0><1952561052379729920> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 10:43:26.457] - <0><1952561052379729921> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
[INFO ] [2025-08-05 10:43:26.494] - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
[INFO ] [2025-08-05 10:43:26.537] - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
[INFO ] [2025-08-05 10:43:26.586] - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
[INFO ] [2025-08-05 10:43:26.655] - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
[INFO ] [2025-08-05 10:43:26.724] - <0><1952561052379729921> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
[INFO ] [2025-08-05 10:43:26.801] - <0><1952561052379729921> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
[INFO ] [2025-08-05 10:43:26.851] - <0><1952561052379729921> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
[INFO ] [2025-08-05 10:43:26.938] - <0><1952561052379729921> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
[INFO ] [2025-08-05 10:43:26.969] - <0><1952561052379729921> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
[INFO ] [2025-08-05 10:43:26.990] - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
[INFO ] [2025-08-05 10:43:27.010] - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 10:43:27.031] - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
[INFO ] [2025-08-05 10:43:27.051] - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
[INFO ] [2025-08-05 10:43:27.070] - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
[INFO ] [2025-08-05 10:43:27.117] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.137] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.162] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.180] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.199] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.221] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.244] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.264] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.282] - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:43:27.302] - <0><1952561052379729921> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
[INFO ] [2025-08-05 10:43:27.321] - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:27.340] - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:27.363] - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:43:27.383] - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
[INFO ] [2025-08-05 10:44:00.470] - <0><1952561195237724160> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
[INFO ] [2025-08-05 10:44:00.521] - <0><1952561195237724160> SELECT id, user_id, tenant_id, is_main, dept_id, dept_name, dept_code, dept_path_id, dept_path_code, dept_path_name, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user_dept WHERE (user_id = 26442) ORDER BY dept_id ASC
[INFO ] [2025-08-05 10:44:05.883] - <0><1952561217555615744> SELECT * FROM (SELECT reply_date AS startDate FROM order_pur_item WHERE wait_num > 0 AND item_stat = 4 AND pur_id IN (SELECT id FROM order_pur WHERE tenant_id = 24739) UNION SELECT plan_date AS startDate FROM dm_delivery_plan_item WHERE delete_flag = 0 AND plan_date IS NOT NULL AND sale_id IN (SELECT id FROM order_pur WHERE tenant_id = 24739)) od ORDER BY od.startDate ASC LIMIT 1
[INFO ] [2025-08-05 10:44:06.072] - <0><1952561218658717696> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
[INFO ] [2025-08-05 10:44:06.109] - <0><1952561218658717696> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:44:06.547] - <0><1952561220470657024> SELECT COUNT(*) FROM (SELECT '2025-08-05' AS startDate, so.pur_id, so.pur_name, so.dept_id, so.dept_name, so.dept_code, soi.order_price_uom, soi.werks, soi.price_uom, so.tenant_name tenantName, soi.delivery_type, so.id saleId, soi.id saleItemId, so.pur_no saleNo, it.plan_no planNo, so.order_date orderDate, soi.goods_id goodsId, soi.goods_code goodsCode, soi.goods_name goodsName, soi.goods_model goodsModel, soi.goods_erp_code goodsErpCode, soi.goods_class_name goodsClassName, soi.class_code goodsClassCode, soi.seq saleSeq, ifnull(it.drawing_no, '-') AS drawingNo, it.match_num AS orderNum, (SELECT IFNULL(SUM(dev_num), 0) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum, so.vendor_id, so.vendor_code vendorCode, so.vendor_name vendorName, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) waitNum, soi.uom_id uomId, soi.uom_code, soi.uom_name uomName, soi.gst_price gstPrice, soi.tax_price taxPrice, soi.gst_price * soi.order_num gstAmount, soi.currency_name currencyName, soi.currency_id currencyId, soi.rate_id rateId, soi.rate_val rateVal, soi.rate_name rateName, soi.taxes_type taxesType, soi.invoice_type invoiceType, so.order_type orderType, soi.purchase_remark remark, soi.change_count changeCount, so.stat, soi.seq, soi.is_close, it.plan_date replyDate, barcode_type barcodeType, it.purchaser_name purEmployee, soi.sale_employee_name saleEmployee, soi.soure_no sourceNo, it.delivery_date deliveryDate, it.warehouse_code warehouseCode, soi.warehouse_name warehouseName, soi.item_stat itemStat, soi.delivery_type deliveryType, it.collect_flag collectFlag, it.receiving_control receivingControl, so.vendor_delivery_address, so.shipping_address, it.id, it.id AS list_id, it.id AS plan_srm_line_id, IFNULL(it.refund_num, 0) refund_num, IFNULL(it.un_competent_num, 0) un_competent_num, soi.tenant_id tenantId, ifnull(it.make_num, 0) makeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) canMakeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) devNum, ifnull(it.make_num, 0) tempFixNum, so.order_type, soi.aux_uom_id, soi.aux_uom_code, soi.aux_uom_name, soi.aux_uom_num, it.uom_num, 1 is_source_to_plan, 0 convert_numerator, (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num FROM order_pur so, order_pur_item soi, dm_delivery_plan_item it WHERE so.id = soi.pur_id AND it.sale_item_id = soi.id AND it.sale_id = so.id AND it.delete_Flag <> 2 AND ((so.change_count = 0 AND item_stat = 4) OR so.change_count > 0 AND item_stat IN (1, 4)) AND soi.delivery_type = 2 AND (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) > 0 AND soi.is_close = 0 AND soi.return_mark = 0 AND so.dept_id = 28905) so WHERE so.canMakeNum > 0
[INFO ] [2025-08-05 10:44:06.594] - <0><1952561220470657024> SELECT * FROM (SELECT '2025-08-05' AS startDate, so.pur_id, so.pur_name, so.dept_id, so.dept_name, so.dept_code, soi.order_price_uom, soi.werks, soi.price_uom, so.tenant_name tenantName, soi.delivery_type, so.id saleId, soi.id saleItemId, so.pur_no saleNo, it.plan_no planNo, so.order_date orderDate, soi.goods_id goodsId, soi.goods_code goodsCode, soi.goods_name goodsName, soi.goods_model goodsModel, soi.goods_erp_code goodsErpCode, soi.goods_class_name goodsClassName, soi.class_code goodsClassCode, soi.seq saleSeq, ifnull(it.drawing_no, '-') AS drawingNo, it.match_num AS orderNum, (SELECT IFNULL(SUM(dev_num), 0) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum, so.vendor_id, so.vendor_code vendorCode, so.vendor_name vendorName, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) waitNum, soi.uom_id uomId, soi.uom_code, soi.uom_name uomName, soi.gst_price gstPrice, soi.tax_price taxPrice, soi.gst_price * soi.order_num gstAmount, soi.currency_name currencyName, soi.currency_id currencyId, soi.rate_id rateId, soi.rate_val rateVal, soi.rate_name rateName, soi.taxes_type taxesType, soi.invoice_type invoiceType, so.order_type orderType, soi.purchase_remark remark, soi.change_count changeCount, so.stat, soi.seq, soi.is_close, it.plan_date replyDate, barcode_type barcodeType, it.purchaser_name purEmployee, soi.sale_employee_name saleEmployee, soi.soure_no sourceNo, it.delivery_date deliveryDate, it.warehouse_code warehouseCode, soi.warehouse_name warehouseName, soi.item_stat itemStat, soi.delivery_type deliveryType, it.collect_flag collectFlag, it.receiving_control receivingControl, so.vendor_delivery_address, so.shipping_address, it.id, it.id AS list_id, it.id AS plan_srm_line_id, IFNULL(it.refund_num, 0) refund_num, IFNULL(it.un_competent_num, 0) un_competent_num, soi.tenant_id tenantId, ifnull(it.make_num, 0) makeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) canMakeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) devNum, ifnull(it.make_num, 0) tempFixNum, so.order_type, soi.aux_uom_id, soi.aux_uom_code, soi.aux_uom_name, soi.aux_uom_num, it.uom_num, 1 is_source_to_plan, 0 convert_numerator, (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num FROM order_pur so, order_pur_item soi, dm_delivery_plan_item it WHERE so.id = soi.pur_id AND it.sale_item_id = soi.id AND it.sale_id = so.id AND it.delete_Flag <> 2 AND ((so.change_count = 0 AND item_stat = 4) OR so.change_count > 0 AND item_stat IN (1, 4)) AND soi.delivery_type = 2 AND (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) > 0 AND soi.is_close = 0 AND soi.return_mark = 0 AND so.dept_id = 28905) so WHERE so.canMakeNum > 0 ORDER BY so.deliveryDate ASC LIMIT 20
[INFO ] [2025-08-05 10:44:06.613] - <0><1952561220470657024> select * from base_config where param_key = 'deliveryHomeDays' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:44:06.653] - <0><1952561220470657024> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1066065, 1123394, 1123530, 1078086))
[INFO ] [2025-08-05 10:44:08.537] - <0><1952561229144477696> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2435' AND line_id = '0')
[INFO ] [2025-08-05 10:44:08.545] - <0><1952561229157060608> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2435
[INFO ] [2025-08-05 10:44:08.561] - <0><1952561229157060608> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:44:08.588] - <0><1952561229157060608> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2435 AND delete_flag = 0)
[INFO ] [2025-08-05 10:44:10.628] - <0><1952561237818298368> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
[INFO ] [2025-08-05 10:44:10.666] - <0><1952561237818298368> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:44:19.527] - <0><1952561275227295744> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2416' AND line_id = '0')
[INFO ] [2025-08-05 10:44:19.531] - <0><1952561275239878656> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2416
[INFO ] [2025-08-05 10:44:19.551] - <0><1952561275239878656> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:44:19.619] - <0><1952561275239878656> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2416 AND delete_flag = 0)
[INFO ] [2025-08-05 10:44:21.155] - <0><1952561281992708096> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
[INFO ] [2025-08-05 10:44:21.204] - <0><1952561281992708096> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:44:25.215] - <0><1952561299042553856> select * from sys_user where user_code = 'SU20250604641_Admin' and is_deleted=0 and is_valid=1
[INFO ] [2025-08-05 10:44:25.234] - <0><1952561299042553856> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
[INFO ] [2025-08-05 10:44:25.254] - <0><1952561299042553856> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
[INFO ] [2025-08-05 10:44:26.244] - <0><1952561303362686976> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
[INFO ] [2025-08-05 10:44:26.288] - <0><1952561303362686976> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
[INFO ] [2025-08-05 10:44:26.737] - <0><1952561305472421890> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 10:44:26.737] - <0><1952561305472421889> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 10:44:26.741] - <0><1952561305472421892> select '1' from dual
[INFO ] [2025-08-05 10:44:26.760] - <0><1952561305472421892> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
[INFO ] [2025-08-05 10:44:26.869] - <0><1952561306030264320> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND parent_id = 0)
[INFO ] [2025-08-05 10:44:26.889] - <0><1952561306030264320> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND is_valid = 1 AND dept_type = 1)
[INFO ] [2025-08-05 10:44:27.276] - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
[INFO ] [2025-08-05 10:44:27.283] - <0><1952561307754123264> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 10:44:27.291] - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
[INFO ] [2025-08-05 10:44:27.296] - <0><1952561307749928960> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
[INFO ] [2025-08-05 10:44:27.297] - <0><1952561307804454912> select '1' from dual
[INFO ] [2025-08-05 10:44:27.305] - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
[INFO ] [2025-08-05 10:44:27.316] - <0><1952561307804454912> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25409 AND bvni.is_read = '0'
[INFO ] [2025-08-05 10:44:27.318] - <0><1952561307754123264> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 10:44:27.320] - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
[INFO ] [2025-08-05 10:44:27.337] - <0><1952561307749928961> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
[INFO ] [2025-08-05 10:44:27.351] - <0><1952561307749928961> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
[INFO ] [2025-08-05 10:44:27.367] - <0><1952561307749928961> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
[INFO ] [2025-08-05 10:44:27.426] - <0><1952561307749928961> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
[INFO ] [2025-08-05 10:44:27.449] - <0><1952561307749928961> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
[INFO ] [2025-08-05 10:44:27.467] - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
[INFO ] [2025-08-05 10:44:27.488] - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 10:44:27.506] - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
[INFO ] [2025-08-05 10:44:27.523] - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
[INFO ] [2025-08-05 10:44:27.540] - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
[INFO ] [2025-08-05 10:44:27.587] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.607] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.626] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.646] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.671] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.691] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.711] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.733] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.753] - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 10:44:27.777] - <0><1952561307749928961> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
[INFO ] [2025-08-05 10:44:27.795] - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 10:44:27.814] - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 10:44:27.833] - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 10:44:27.856] - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 10:44:34.501] - <0><1952561337726619648> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:44:34.572] - <0><1952561337726619648> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:44:36.831] - <0><1952561347818115072> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2416' AND line_id = '0')
[INFO ] [2025-08-05 10:44:37.144] - <0><1952561349126737920> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2416
[INFO ] [2025-08-05 10:44:37.160] - <0><1952561349126737920> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:44:37.186] - <0><1952561349126737920> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2416 AND delete_flag = 0)
[INFO ] [2025-08-05 10:45:15.420] - <0><1952561509177184256> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:45:15.468] - <0><1952561509177184256> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:45:21.291] - <0><1952561534296870912> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2392' AND line_id = '0')
[INFO ] [2025-08-05 10:45:21.609] - <0><1952561535630659584> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
[INFO ] [2025-08-05 10:45:21.626] - <0><1952561535630659584> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:45:21.648] - <0><1952561535630659584> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
[INFO ] [2025-08-05 10:45:25.835] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
[INFO ] [2025-08-05 10:45:25.863] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
[INFO ] [2025-08-05 10:45:25.906] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE (id = 125930)
[INFO ] [2025-08-05 10:45:25.970] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE id = 125930
[INFO ] [2025-08-05 10:45:26.057] - <0><1952561553041215488> UPDATE order_pur_item SET tenant_p_id = 24739, tenant_id = 24739, source_id = 185265, source_item_id = 368469, pur_id = 24359, seq = '1', erp_change_type = '', goods_id = 1103821, goods_erp_code = '8501000000052', goods_code = '8501000000052', goods_name = '输出线', goods_model = 'R输出线(35094/CK01-03456客供线)(剥线45MM上锡5MM 无SR)B:KG S:KG/旧编码:85668077 ROHS 2.0、REACH', uom_id = 617, uom_code = 'Pcs', uom_name = 'Pcs', aux_uom_id = 617, aux_uom_code = 'Pcs', aux_uom_name = 'Pcs', rate_id = 65, rate_name = '13%增值税', rate_val = 13.000000, matched_plan_num = 0.000000, gst_amount = 31800.000000, tax_amount = 28141.590000, delivery_type = 2, taxes_type = 0, gst_price = 3.180000, tax_price = 2.814159, barcode_type = 0, soure_no = 'PO2507230003', change_count = 0, item_stat = 4, delivery_date = '2025-07-23 15:39:38', reply_date = '2025-07-23 15:40:00', make_num = 2001.000000, order_num = 10000.000000, purchase_confirm = 0, vendor_confirm = 0, fix_num = 1001.000000, refund_num = 0.000000, erp_master_num = 0.000000, erp_reject_num = 0.000000, wait_num = 8999.000000, goods_class_name = 'DC线材', delivery_stat = 1, is_busy = 0, delete_flag = 0, is_print = 0, is_close = 0, return_mark = 0, free_mark = 0, print_count = 0, is_return_po = 0, order_price_uom = 'Pcs', price_uom = 10000.00, un_competent_num = 0.000000, mrp_region = '101', create_id = 0, creater = '平台账号', create_date = '2025-07-23 15:38:37', modifi_id = 30410, modifier = 'SU20250604641_admin', modify_date = '2025-08-05 10:45:25' WHERE id = 125930
[INFO ] [2025-08-05 10:45:26.084] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE (id = 125931)
[INFO ] [2025-08-05 10:45:26.129] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE id = 125931
[INFO ] [2025-08-05 10:45:26.182] - <0><1952561553041215488> UPDATE order_pur_item SET tenant_p_id = 24739, tenant_id = 24739, source_id = 185265, source_item_id = 368470, pur_id = 24359, seq = '2', erp_change_type = '', goods_id = 1103810, goods_erp_code = '8501000000040', goods_code = '8501000000040', goods_name = '输出线', goods_model = 'R输出线(21306客供线)无SR B:KG S:KG/旧编码:85668066 ROHS 2.0、REACH', uom_id = 617, uom_code = 'Pcs', uom_name = 'Pcs', aux_uom_id = 617, aux_uom_code = 'Pcs', aux_uom_name = 'Pcs', rate_id = 65, rate_name = '13%增值税', rate_val = 13.000000, matched_plan_num = 0.000000, gst_amount = 0.000000, tax_amount = 0.000000, delivery_type = 2, taxes_type = 0, gst_price = 0.000000, tax_price = 0.000000, barcode_type = 0, soure_no = 'PO2507230003', change_count = 0, item_stat = 4, delivery_date = '2025-07-23 15:40:56', reply_date = '2025-07-23 15:40:00', make_num = 1001.000000, order_num = 10000.000000, purchase_confirm = 0, vendor_confirm = 0, fix_num = 1.000000, refund_num = 0.000000, erp_master_num = 0.000000, erp_reject_num = 0.000000, wait_num = 9999.000000, goods_class_name = 'DC线材', delivery_stat = 1, is_busy = 0, delete_flag = 0, is_print = 0, is_close = 0, return_mark = 0, free_mark = 0, print_count = 0, is_return_po = 0, order_price_uom = 'Pcs', price_uom = 10000.00, un_competent_num = 0.000000, mrp_region = '101', create_id = 0, creater = '平台账号', create_date = '2025-07-23 15:38:37', modifi_id = 30410, modifier = 'SU20250604641_admin', modify_date = '2025-08-05 10:45:26' WHERE id = 125931
[INFO ] [2025-08-05 10:45:26.240] - <0><1952561553041215488> UPDATE dm_delivery SET tenant_p_id = 0, tenant_id = 24739, de_no = 'SH202507232707', delivery_type = 2, vendor_id = 25409, vendor_code = 'SA0116', vendor_name = '测试供应商A', dept_id = 28905, dept_code = '101', dept_name = '佛山市顺德区冠宇达电源有限公司', is_print = 0, print_count = 0, is_call = 0, de_stat = 2, is_ele = 0, logistics_name = '', ele_no = '', delivery_date = '2025-08-05 10:45:25', audit_time = '2025-08-05 10:45:25', delete_flag = 0, see = 0, tenant_name = '', is_compromise = 0, is_compromise_create = 0, address = '', shipping_address = '总厂材料仓', order_type = 1, create_id = 30410, creater = 'SU20250604641_admin', create_date = '2025-07-23 19:52:05', modifi_id = 30410, modifier = 'SU20250604641_admin', modify_date = '2025-08-05 10:45:26' WHERE id = 2392
[INFO ] [2025-08-05 10:45:26.314] - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1103821, 1103810))
[INFO ] [2025-08-05 10:45:36.676] - <0><1952561553041215488> INSERT INTO sys_intfsap_log (send_msg, tenant_id, return_msg, intf_name, send_url, exe_second, create_id, creater, create_date, modifi_id, modifier, modify_date) VALUES ('{"corpId":"gyd","data":{"supplierName":"测试供应商A","creator":"SRM","code":"SH202507232707","voucherType":"标准采购","createTime":"2025-08-05 10:45:25","supplierCode":"SA0116","rows":[{"quantity":1.00,"productionBatchNo":"1","unitName":"Pcs","sourceItemId":"368469","reserve1":"2392","manufactureDate":"2025-07-23","rowCode":"5728","materialCode":"8501000000052","sourceVoucherCode":"PO2507230003","reserve2":"5728","warehouseCode":"CL01"},{"quantity":1.00,"productionBatchNo":"1","unitName":"Pcs","sourceItemId":"368470","reserve1":"2392","manufactureDate":"2025-07-23","rowCode":"5729","materialCode":"8501000000040","sourceVoucherCode":"PO2507230003","reserve2":"5729","warehouseCode":"CL01"}]},"sign":"268DFBF7FA8DB910E374475CC1E76E39","timestamp":"1754361926338"}', 24739, '{"code":1,"msg":"数据同步成功"}', '送货单同步至WMS接口', 'http://192.168.1.119:9005/sync/order/srmPurchaseDelivery/standart', 10, 30410, 'SU20250604641_admin', '2025-08-05 10:45:36', 30410, 'SU20250604641_admin', '2025-08-05 10:45:36')
[INFO ] [2025-08-05 10:45:36.966] - <0><1952561599744790528> select '1' from dual
[INFO ] [2025-08-05 10:45:36.996] - <0><1952561599744790528> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:45:37.038] - <0><1952561599744790528> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:45:38.841] - <0><1952561553041215488> INSERT INTO sys_intfsap_log (send_msg, tenant_id, return_msg, intf_name, send_url, exe_second, create_id, creater, create_date, modifi_id, modifier, modify_date) VALUES ('{"NeedUpDateFields":["F_UXTH_deliqty"],"NeedReturnFields":[],"IsDeleteEntry":false,"SubSystemId":"","IsVerifyBaseDataField":false,"IsEntryBatchFill":true,"ValidateFlag":true,"NumberSearch":true,"IsAutoAdjustField":false,"InterationFlags":"","IgnoreInterationFlag":"true","IsControlPrecision":false,"ValidateRepeatJson":false,"model":[{"FID":"185265","FPOOrderEntry":[{"FEntryID":"368469","F_UXTH_deliqty":1001.000000},{"FEntryID":"368470","F_UXTH_deliqty":1.000000}]}]}', 24739, '{"result":{"needReturnData":[],"responseStatus":{"errors":[],"isSuccess":true,"msgCode":0,"successEntitys":[{"dIndex":0,"id":"185265","number":"PO2507230003"}]}}}', '批量修改采购订单', 'PUR_PurchaseOrder', 1754361938814, 30410, 'SU20250604641_admin', '2025-08-05 10:45:38', 30410, 'SU20250604641_admin', '2025-08-05 10:45:38')
[INFO ] [2025-08-05 10:45:41.826] - <0><1952561620074582016> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
[INFO ] [2025-08-05 10:45:41.922] - <0><1952561620074582016> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:45:41.957] - <0><1952561620074582016> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
[INFO ] [2025-08-05 10:49:42.850] - <0><1952562631245778944> select '1' from dual
[INFO ] [2025-08-05 10:49:42.994] - <0><1952562631245778944> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
[INFO ] [2025-08-05 10:49:43.018] - <0><1952562631245778944> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:49:43.177] - <0><1952562631245778944> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
[INFO ] [2025-08-05 10:51:58.420] - <0><1952563199875960832> select '1' from dual
[INFO ] [2025-08-05 10:51:58.438] - <0><1952563199875960832> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:51:58.557] - <0><1952563199875960832> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:52:03.897] - <0><1952563222940438528> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2434' AND line_id = '0')
[INFO ] [2025-08-05 10:52:03.899] - <0><1952563222957215744> select '1' from dual
[INFO ] [2025-08-05 10:52:03.920] - <0><1952563222957215744> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
[INFO ] [2025-08-05 10:52:03.939] - <0><1952563222957215744> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 10:52:03.963] - <0><1952563222957215744> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
[INFO ] [2025-08-05 10:57:52.608] - <0><1952564685305499648> select '1' from dual
[INFO ] [2025-08-05 10:57:52.633] - <0><1952564685305499648> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:57:52.717] - <0><1952564685305499648> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:58:13.466] - <0><1952564772920315904> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:58:13.512] - <0><1952564772920315904> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 10:58:26.485] - <0><1952564827404324864> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 10:58:26.528] - <0><1952564827404324864> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 11:02:56.418] - <0><1952565959778324480> select '1' from dual
[INFO ] [2025-08-05 11:02:56.459] - <0><1952565959778324480> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
[INFO ] [2025-08-05 11:02:56.510] - <0><1952565959778324480> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 11:02:56.556] - <0><1952565959778324480> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
[INFO ] [2025-08-05 11:03:56.330] - <0><1952566211109408770> select '1' from dual
[INFO ] [2025-08-05 11:03:56.330] - <0><1952566211109408768> select '1' from dual
[INFO ] [2025-08-05 11:03:56.346] - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
[INFO ] [2025-08-05 11:03:56.346] - <0><1952566211109408769> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 11:03:56.363] - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
[INFO ] [2025-08-05 11:03:56.371] - <0><1952566211109408770> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
[INFO ] [2025-08-05 11:03:56.380] - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
[INFO ] [2025-08-05 11:03:56.383] - <0><1952566211109408769> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 11:03:56.396] - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
[INFO ] [2025-08-05 11:03:56.411] - <0><1952566211109408768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
[INFO ] [2025-08-05 11:03:56.426] - <0><1952566211109408768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
[INFO ] [2025-08-05 11:03:56.443] - <0><1952566211109408768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
[INFO ] [2025-08-05 11:03:56.568] - <0><1952566211109408768> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
[INFO ] [2025-08-05 11:03:56.602] - <0><1952566211109408768> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
[INFO ] [2025-08-05 11:03:56.623] - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
[INFO ] [2025-08-05 11:03:56.644] - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 11:03:56.664] - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
[INFO ] [2025-08-05 11:03:56.686] - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
[INFO ] [2025-08-05 11:03:56.709] - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
[INFO ] [2025-08-05 11:03:56.769] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.792] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.811] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.830] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.850] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.871] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.894] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.914] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.934] - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:03:56.966] - <0><1952566211109408768> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
[INFO ] [2025-08-05 11:03:56.988] - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:03:57.007] - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:03:57.028] - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:03:57.047] - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:05:43.636] - <0><1952566661120479232> select '1' from dual
[INFO ] [2025-08-05 11:05:43.655] - <0><1952566661120479232> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
[INFO ] [2025-08-05 11:05:43.748] - <0><1952566661120479232> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
[INFO ] [2025-08-05 11:07:20.747] - <0><1952567068504838145> select '1' from dual
[INFO ] [2025-08-05 11:07:20.751] - <0><1952567068509032448> select '1' from dual
[INFO ] [2025-08-05 11:07:20.757] - <0><1952567068504838144> select '1' from dual
[INFO ] [2025-08-05 11:07:20.767] - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
[INFO ] [2025-08-05 11:07:20.772] - <0><1952567068504838145> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
[INFO ] [2025-08-05 11:07:20.782] - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
[INFO ] [2025-08-05 11:07:20.793] - <0><1952567068504838144> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
[INFO ] [2025-08-05 11:07:20.797] - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
[INFO ] [2025-08-05 11:07:20.806] - <0><1952567068504838145> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
[INFO ] [2025-08-05 11:07:20.811] - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
[INFO ] [2025-08-05 11:07:20.829] - <0><1952567068509032448> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
[INFO ] [2025-08-05 11:07:20.846] - <0><1952567068509032448> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
[INFO ] [2025-08-05 11:07:20.861] - <0><1952567068509032448> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
[INFO ] [2025-08-05 11:07:20.920] - <0><1952567068509032448> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
[INFO ] [2025-08-05 11:07:20.948] - <0><1952567068509032448> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
[INFO ] [2025-08-05 11:07:20.965] - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
[INFO ] [2025-08-05 11:07:20.983] - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
[INFO ] [2025-08-05 11:07:21.002] - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
[INFO ] [2025-08-05 11:07:21.023] - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
[INFO ] [2025-08-05 11:07:21.042] - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
[INFO ] [2025-08-05 11:07:21.093] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.115] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.136] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.159] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.182] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.202] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.232] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.254] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.275] - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
[INFO ] [2025-08-05 11:07:21.296] - <0><1952567068509032448> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
[INFO ] [2025-08-05 11:07:21.314] - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:07:21.334] - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:07:21.444] - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:07:21.466] - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
[INFO ] [2025-08-05 11:22:06.647] - <0><1952570784180031488> select '1' from dual
[INFO ] [2025-08-05 11:22:06.677] - <0><1952570784180031488> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
[INFO ] [2025-08-05 11:22:06.712] - <0><1952570784180031488> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
[INFO ] [2025-08-05 11:22:06.746] - <0><1952570784180031488> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
