{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue?vue&type=template&id=0a0920c3&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue", "mtime": 1754404617956}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"DIAN-common-layout DIAN-flex-main\" }, [\n    _c(\n      \"div\",\n      { staticClass: \"DIAN-common-layout-center\" },\n      [\n        _c(\n          \"el-row\",\n          { staticClass: \"DIAN-common-search-box\", attrs: { gutter: 16 } },\n          [\n            _c(\n              \"el-form\",\n              {\n                nativeOn: {\n                  submit: function ($event) {\n                    $event.preventDefault()\n                  },\n                },\n              },\n              [\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 6 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: { placeholder: \"请输入需求单号\", clearable: \"\" },\n                      model: {\n                        value: _vm.queryParam.demandNo,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"demandNo\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.demandNo\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: { placeholder: \"请输入申请人名称\", clearable: \"\" },\n                      model: {\n                        value: _vm.queryParam.applicant,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"applicant\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.applicant\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: {\n                        placeholder: \"请输入采购员名称或编码\",\n                        clearable: \"\",\n                      },\n                      model: {\n                        value: _vm.queryParam.pur,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"pur\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.pur\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 6 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: {\n                        placeholder: \"请输入物料编码/名称/型号\",\n                        clearable: \"\",\n                      },\n                      model: {\n                        value: _vm.queryParam.goods,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"goods\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.goods\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\n                      \"el-form-item\",\n                      [\n                        _c(\n                          \"el-select\",\n                          {\n                            attrs: {\n                              placeholder: \"请选择需求状态\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.queryParam.caseStat,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.queryParam, \"caseStat\", $$v)\n                              },\n                              expression: \"queryParam.caseStat\",\n                            },\n                          },\n                          _vm._l(_vm.caseStatOptions, function (item) {\n                            return _c(\"el-option\", {\n                              key: item.key,\n                              attrs: { label: item.value, value: item.key },\n                            })\n                          }),\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n                _vm.showAll\n                  ? [\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 6 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\"el-date-picker\", {\n                                attrs: {\n                                  type: \"daterange\",\n                                  placeholder: \"请选择需求日期\",\n                                  \"range-separator\": \"至\",\n                                  \"start-placeholder\": \"（需求）开始日期\",\n                                  \"end-placeholder\": \"（需求）结束日期\",\n                                },\n                                model: {\n                                  value: _vm.demandDates,\n                                  callback: function ($$v) {\n                                    _vm.demandDates = $$v\n                                  },\n                                  expression: \"demandDates\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 6 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\"el-date-picker\", {\n                                attrs: {\n                                  type: \"daterange\",\n                                  placeholder: \"请选择申请日期\",\n                                  \"range-separator\": \"至\",\n                                  \"start-placeholder\": \"（申请）开始日期\",\n                                  \"end-placeholder\": \"（申请）结束日期\",\n                                },\n                                model: {\n                                  value: _vm.applyDates,\n                                  callback: function ($$v) {\n                                    _vm.applyDates = $$v\n                                  },\n                                  expression: \"applyDates\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ]\n                  : _vm._e(),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 6 } },\n                  [\n                    _c(\n                      \"el-form-item\",\n                      [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"primary\", icon: \"el-icon-search\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.search()\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.$t(\"common.search\")) +\n                                \"\\n            \"\n                            ),\n                          ]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { icon: \"el-icon-refresh-right\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.reset()\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.$t(\"common.reset\")) +\n                                \"\\n            \"\n                            ),\n                          ]\n                        ),\n                        !_vm.showAll\n                          ? _c(\n                              \"el-button\",\n                              {\n                                attrs: {\n                                  type: \"text\",\n                                  icon: \"el-icon-arrow-down\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    _vm.showAll = true\n                                  },\n                                },\n                              },\n                              [_vm._v(\"展开\\n            \")]\n                            )\n                          : _c(\n                              \"el-button\",\n                              {\n                                attrs: {\n                                  type: \"text\",\n                                  icon: \"el-icon-arrow-up\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    _vm.showAll = false\n                                  },\n                                },\n                              },\n                              [_vm._v(\"\\n              收起\\n            \")]\n                            ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ],\n              2\n            ),\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"DIAN-common-layout-main DIAN-flex-main\" },\n          [\n            _c(\"div\", { staticClass: \"DIAN-common-head\" }, [\n              _c(\"div\"),\n              _c(\n                \"div\",\n                { staticClass: \"DIAN-common-head-right\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"has-per\",\n                          rawName: \"v-has-per\",\n                          value: \"base:sampleDemand:save\",\n                          expression: \"'base:sampleDemand:save'\",\n                        },\n                      ],\n                      attrs: { type: \"primary\", icon: \"el-icon-plus\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.addOrUpdateHandle()\n                        },\n                      },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n            \" +\n                          _vm._s(_vm.$t(\"common.addBtn\")) +\n                          \"\\n          \"\n                      ),\n                    ]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"has-per\",\n                          rawName: \"v-has-per\",\n                          value: \"base:sampleDemand:returnSample\",\n                          expression: \"'base:sampleDemand:returnSample'\",\n                        },\n                      ],\n                      attrs: {\n                        type: \"primary\",\n                        icon: \"el-icon-back\",\n                        loading: _vm.btnLoading,\n                        disabled: _vm.selectedDatas.length === 0,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchReturnSample()\n                        },\n                      },\n                    },\n                    [_vm._v(\"退回PLM\")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"has-per\",\n                          rawName: \"v-has-per\",\n                          value: \"base:sampleDemand:issuedSample\",\n                          expression: \"'base:sampleDemand:issuedSample'\",\n                        },\n                      ],\n                      attrs: {\n                        type: \"primary\",\n                        icon: \"el-icon-plus\",\n                        disabled: _vm.selectedDatas.length === 0,\n                        loading: _vm.btnLoading,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.openVendorDialog()\n                        },\n                      },\n                    },\n                    [_vm._v(\"\\n            添加供应商\\n          \")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"has-per\",\n                          rawName: \"v-has-per\",\n                          value: \"base:sampleDemand:updateBuyer\",\n                          expression: \"'base:sampleDemand:updateBuyer'\",\n                        },\n                      ],\n                      attrs: {\n                        type: \"primary\",\n                        icon: \"el-icon-user\",\n                        disabled: _vm.selectedDatas.length === 0,\n                        loading: _vm.btnLoading,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.openBuyerDialog()\n                        },\n                      },\n                    },\n                    [_vm._v(\"\\n            更换采购员\\n          \")]\n                  ),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"has-per\",\n                          rawName: \"v-has-per\",\n                          value: \"base:sampleDemand:localClosed\",\n                          expression: \"'base:sampleDemand:localClosed'\",\n                        },\n                      ],\n                      attrs: {\n                        type: \"primary\",\n                        icon: \"el-icon-check\",\n                        loading: _vm.btnLoading,\n                        disabled: _vm.selectedDatas.length === 0,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchLocalClosed()\n                        },\n                      },\n                    },\n                    [_vm._v(\"\\n            本地结案\\n          \")]\n                  ),\n                  _c(\n                    \"el-tooltip\",\n                    {\n                      attrs: {\n                        effect: \"dark\",\n                        content: _vm.$t(\"common.refresh\"),\n                        placement: \"top\",\n                      },\n                    },\n                    [\n                      _c(\"el-link\", {\n                        attrs: {\n                          icon: \"icon-ym icon-ym-Refresh DIAN-common-head-icon\",\n                          underline: false,\n                        },\n                        on: {\n                          click: function ($event) {\n                            return _vm.search()\n                          },\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\"d-screen-full\"),\n                ],\n                1\n              ),\n            ]),\n            _c(\n              \"d-table\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.listLoading,\n                    expression: \"listLoading\",\n                  },\n                ],\n                attrs: { data: _vm.list, hasNO: false, hasC: true },\n                on: { \"selection-change\": _vm.handleSelectionChange },\n              },\n              [\n                _c(\"el-table-column\", {\n                  attrs: { type: \"expand\" },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (props) {\n                        return [\n                          _c(\n                            \"el-table\",\n                            {\n                              staticStyle: { width: \"75%\" },\n                              attrs: {\n                                data:\n                                  props.row.sampleDemandVendorEntityList || [],\n                                size: \"mini\",\n                                border: \"\",\n                              },\n                            },\n                            [\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  type: \"index\",\n                                  width: \"100\",\n                                  label: \"序号\",\n                                  align: \"center\",\n                                  sortable: \"\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"goodsErpCode\",\n                                  label: \"物料编码\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                  \"show-overflow-tooltip\": \"\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"goodsName\",\n                                  label: \"物料名称\",\n                                  align: \"center\",\n                                  width: \"150\",\n                                  \"show-overflow-tooltip\": \"\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"vendorCode\",\n                                  label: \"供应商编码\",\n                                  width: \"120\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"vendorName\",\n                                  label: \"供应商名称\",\n                                  width: \"180\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"caseStat\",\n                                  label: \"结案状态\",\n                                  width: \"120\",\n                                },\n                                scopedSlots: _vm._u(\n                                  [\n                                    {\n                                      key: \"default\",\n                                      fn: function (scope) {\n                                        return [\n                                          _c(\"span\", [\n                                            _vm._v(\n                                              _vm._s(\n                                                _vm._f(\"commonEnumsTurn\")(\n                                                  scope.row.caseStat,\n                                                  \"base.CaseStatEnum\"\n                                                )\n                                              )\n                                            ),\n                                          ]),\n                                        ]\n                                      },\n                                    },\n                                  ],\n                                  null,\n                                  true\n                                ),\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"assigner\",\n                                  label: \"分配人\",\n                                  width: \"100\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"assignDate\",\n                                  label: \"分配时间\",\n                                  width: \"150\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"modifier\",\n                                  label: \"更新人\",\n                                  width: \"100\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"modifyDate\",\n                                  label: \"更新时间\",\n                                  width: \"150\",\n                                },\n                              }),\n                              _c(\"el-table-column\", {\n                                attrs: {\n                                  prop: \"remark\",\n                                  label: \"备注\",\n                                  width: \"200\",\n                                  \"show-overflow-tooltip\": \"\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    type: \"index\",\n                    width: \"50\",\n                    label: \"序号\",\n                    align: \"center\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"orgName\",\n                    label: \"组织名称\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandNo\",\n                    label: \"需求单号\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandClassType\",\n                    label: \"单据类型\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(\n                                _vm._f(\"commonEnumsTurn\")(\n                                  scope.row.demandClassType,\n                                  \"base.DemandClassTypeEnum\"\n                                )\n                              )\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"isNeedUpFile\",\n                    label: \"是否需要上传文件\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          scope.row.isNeedUpFile === 1\n                            ? _c(\"el-tag\", [_vm._v(\"是\")])\n                            : _c(\"el-tag\", [_vm._v(\"否\")]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"applicant\",\n                    label: \"申请人\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"applyDeptName\",\n                    label: \"申请部门\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"applyDate\",\n                    label: \"申请日期\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"purName\",\n                    label: \"采购员\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"purCode\",\n                    label: \"采购员编码\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsCode\",\n                    label: \"物料编码\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsName\",\n                    label: \"物料名称\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsModel\",\n                    label: \"物料规格型号\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"vendorName\",\n                    label: \"建议供应商\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandQty\",\n                    label: \"需求数量\",\n                    align: \"center\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"uomName\",\n                    label: \"单位\",\n                    align: \"center\",\n                    width: \"80\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandDate\",\n                    label: \"需求日期\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _vm._v(\n                            \"\\n            \" +\n                              _vm._s(_vm._f(\"date\")(scope.row.demandDate)) +\n                              \"\\n          \"\n                          ),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"saleDeptName\",\n                    label: \"销售部门名称\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"model\",\n                    label: \"机型\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"purpose\",\n                    label: \"用途\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"caseStat\",\n                    label: \"需求状态\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(\n                                _vm._f(\"commonEnumsTurn\")(\n                                  scope.row.caseStat,\n                                  \"base.CaseStatEnum\"\n                                )\n                              )\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"returnCause\",\n                    label: \"退回原因\",\n                    width: \"200\",\n                    \"show-overflow-tooltip\": \"\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"caseDate\",\n                    label: \"结案日期\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _vm._v(\n                            \"\\n            \" +\n                              _vm._s(_vm._f(\"date\")(scope.row.caseDate)) +\n                              \"\\n          \"\n                          ),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"itemRemark\",\n                    label: \"行备注\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"200\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"creater\",\n                    label: \"创建人\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"createDate\",\n                    label: \"创建时间\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"modifier\",\n                    label: \"更新人\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"modifyDate\",\n                    label: \"更新时间\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    label: \"操作\",\n                    fixed: \"right\",\n                    align: \"center\",\n                    width: \"100\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\n                            \"el-button\",\n                            {\n                              directives: [\n                                {\n                                  name: \"has-per\",\n                                  rawName: \"v-has-per\",\n                                  value: \"base:sample:info\",\n                                  expression: \"'base:sample:info'\",\n                                },\n                              ],\n                              attrs: { size: \"mini\", type: \"text\" },\n                              on: {\n                                click: function ($event) {\n                                  return _vm.addOrUpdateHandle(scope.row.id)\n                                },\n                              },\n                            },\n                            [\n                              _vm._v(\n                                \"\\n              \" +\n                                  _vm._s(_vm.$t(\"common.lookBtn\")) +\n                                  \"\\n            \"\n                              ),\n                            ]\n                          ),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n              ],\n              1\n            ),\n            _c(\"d-pagination\", {\n              attrs: {\n                total: _vm.total,\n                page: _vm.queryParam.page,\n                limit: _vm.queryParam.limit,\n              },\n              on: {\n                \"update:page\": function ($event) {\n                  return _vm.$set(_vm.queryParam, \"page\", $event)\n                },\n                \"update:limit\": function ($event) {\n                  return _vm.$set(_vm.queryParam, \"limit\", $event)\n                },\n                pagination: _vm.initData,\n              },\n            }),\n          ],\n          1\n        ),\n        _c(\"Form\", {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.detailVisible,\n              expression: \"detailVisible\",\n            },\n          ],\n          ref: \"detail\",\n          on: { callRefreshList: _vm.callRefreshList },\n        }),\n        _c(\"VendorProp\", {\n          ref: \"vendor\",\n          attrs: { singleChoice: false },\n          on: { callData: _vm.vendorSelect },\n        }),\n        _c(\"UserProp\", {\n          ref: \"userProp\",\n          attrs: { singleChoice: true, title: \"采购员\" },\n          on: { callData: _vm.userSelect },\n        }),\n      ],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}