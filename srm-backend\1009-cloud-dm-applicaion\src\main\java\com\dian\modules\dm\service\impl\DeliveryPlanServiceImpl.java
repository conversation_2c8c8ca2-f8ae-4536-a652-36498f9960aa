/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.dm.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dian.client.base.BaseClient;
import com.dian.client.order.OrderClient;
import com.dian.client.sys.SysClient;
import com.dian.common.exception.RRException;
import com.dian.common.validator.Assert;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.dian.common.utils.BeanConverter;
import com.dian.common.vo.UserSimpleVO;
import com.dian.enums.WhetherEnum;
import com.dian.k3cloud.vo.purchaseOrder.PurchaseOrderVo;
import com.dian.mbo.sap.zmm006.request.UploadPlanRequest;
import com.dian.mbo.sap.zmm006.response.UploadPlanResponse;
import com.dian.modules.base.query.GoodsReqVo;
import com.dian.modules.base.service.ConfigService;
import com.dian.modules.base.vo.GoodsVO;
import com.dian.modules.base.vo.VendorVO;
import com.dian.vo.EmailMessageVo;
import com.dian.vo.sys.UserSimpleVO;
import com.dian.modules.dm.entity.*;
import com.dian.modules.dm.service.DeliveryItemService;
import com.dian.modules.dm.service.DeliveryPlanItemService;
import com.dian.modules.dm.service.DeliveryShoppingCartService;
import com.dian.modules.dm.vo.*;
import com.dian.modules.enums.common.InterfaceOperationEnumeration;
import com.dian.modules.enums.dm.DeliveryPlanDeleteFlagEnum;
import com.dian.modules.enums.dm.MaterialSourceEnum;
import com.dian.modules.enums.dm.OverwritePlanStatEnum;
import com.dian.modules.enums.dm.PlanReplyTypeEnum;
import com.dian.modules.enums.sys.Ent_EntTypeEnum;
import com.dian.modules.k3cloud.service.ExecuteJoggleService;
import com.dian.modules.order.query.PurQuery;
import com.dian.modules.order.vo.PurItemLineVO;
import com.dian.modules.sap.service.SapApiService;
import com.dian.modules.sys.vo.DeptVO;
import com.dian.util.EasypoiUtil;
import com.dian.util.ExcelExportStatisticStyler;
import com.dian.vo.SpeciaBasicDataBody;
import com.dian.vo.SubcontractingOrderVo;
import com.dian.vo.UpdateMinimumVO;
import com.dian.vo.sys.DeptInfoVo;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.collections.CollectionUtils;
import cn.hutool.core.util.StrUtil;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import com.dian.common.log.TraceLoggerFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import com.dian.common.utils.PageUtils;
import com.dian.common.utils.Query;
import com.dian.common.server.CommonService;
import com.dian.modules.dm.dao.DeliveryPlanDao;
import com.dian.modules.dm.entity.DeliveryPlanEntity;
import com.dian.modules.dm.service.DeliveryPlanService;

import javax.servlet.http.HttpServletResponse;

/**
 * 送货计划明细服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-10 10:56:26
 */
@Service("deliveryPlanService")
public class DeliveryPlanServiceImpl extends ServiceImpl<DeliveryPlanDao, DeliveryPlanEntity> implements DeliveryPlanService {
    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    private static final Pattern SCIENTIFIC_NOTATION_PATTERN = Pattern.compile("^[+-]?\\d*\\.?\\d+([eE][+-]?\\d+)$");
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    @Autowired
    public CommonService commonService;
   @Autowired
    public ConfigService configService;

    @Autowired
    private DeliveryPlanService deliveryPlanService;
    @Autowired
    private DeliveryItemService deliveryItemService;
    @Autowired
    private DeliveryPlanItemService deliveryPlanItemService;
    @Autowired
    private BaseClient baseClient;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private DeliveryShoppingCartService deliveryShoppingCartService;
    @Autowired
    private SysClient sysClient;
    @Autowired
    private SapApiService sapApiService;
    @Autowired
    private ExecuteJoggleService executeJoggleService;

    /**
     * 送货计划明细分页
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        Long tenantId = commonService.getTenantId();
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(commonService.getTenantInfo().getEntType())){
            VendorVO vendorVo = baseClient.getVendorVoBySourceId(tenantId);
            Assert.isNull(vendorVo,"供应商不存在！");
            tenantId = vendorVo.getTenantId();
            params.put("vendorId",vendorVo.getSoureId());
        }
        params.put("tenantId",tenantId);
        IPage<DeliveryPlanEntity> page = this.page(new Query<DeliveryPlanEntity>().getPage(params),getQueryWrapper(params) );
        return new PageUtils(page);
    }

    /**
     *  送货计划明细新增
     * @param deliveryPlanEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean saveInfo(DeliveryPlanEntity deliveryPlanEntity) {

        //设置编码，等基础默然初始化数据设置
        //数据完整性校验
        this.paramsCheck(deliveryPlanEntity,AddGroup.class);

        //保存
        this.save(deliveryPlanEntity);

        return true;
    }

    /**
     *送货计划明细更新
     * @param deliveryPlanEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateInfo(DeliveryPlanEntity deliveryPlanEntity) {

        //修改状态校验
        this.updateCheck(deliveryPlanEntity.getId());

        //主表数据完整性校验
        this.paramsCheck(deliveryPlanEntity, UpdateGroup.class);

        //更新主表
        this.updateById(deliveryPlanEntity);


        return true;
    }

    /**
     *送货计划明细删除
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean deleteInfo(Long[] ids) {

        //删除状态校验
        this.deleteCheck(ids);

        //更新主表
        this.remove(new QueryWrapper<DeliveryPlanEntity>().in("id",ids));

        return true;
    }

    /**
     * 送货计划明细详情
     * @param id
     * @return
     */
    @Override
    public DeliveryPlanEntity getInfo(Long id) {
        DeliveryPlanEntity deliveryPlanEntity = getById(id);
        if (Objects.isNull(deliveryPlanEntity)){
            throw new RRException(String.format("查询不到对应的送货(回货)计划主表信息,id:%s",id));
        }
        List<DeliveryPlanItemEntity> planItemList = deliveryPlanItemService.getItemListByPlanId(deliveryPlanEntity.getId());
//        if (CollectionUtil.isEmpty(planItemList)){
//            throw new RRException(String.format("查询不到对应的送货(回货)计划主对应的明细行数据,id:%s",id));
//        }
        deliveryPlanEntity.setPlanItemList(planItemList);
        return deliveryPlanEntity;
    }

    /**
     * 送货计划明细审核
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean checkInfo(Long id) {
        DeliveryPlanEntity deliveryPlanEntity =this.getById(id);
        checkCheck(deliveryPlanEntity);
        return this.updateById(deliveryPlanEntity);
    }

    /**
     * 下载送货计划导入模版 - 动态生成
     * @param params
     * @param response
     */
    @Override
    public void downloadPlanTemplate(Map<String, Object> params, HttpServletResponse response) {
//        //创建自定义导出模版
//        List<ExcelExportEntity> customizeModel = this.createCustomizeModel(params);
//        //往模版中填充数据
//        List<HashMap<String, Object>> list = this.dynamicListDataByKey(params);
//        String xlsFileName = "送货计划导入模版.xlsx";
//        ExportParams exportParams = new ExportParams();
//        exportParams.setStyle(ExcelExportStatisticStyler.class);
//        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, customizeModel, list);
//        EasypoiUtil.downLoadExcel(xlsFileName,response,workbook);
    }

    /**
     * 送货计划导入 - 导入动态计划模版
     * @param objList
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public List<AfterImportErrorPlanVO> importDeliveryPlan(List<Object> objList) {
        List<Map<String, Object>> list = toListMap(objList);
        Long tenantId = commonService.getTenantId();
        if(CollectionUtil.isEmpty(list)){
            throw new RRException(String.format("导入的数据为空,请检查Excel导入数据"));
        }
        List<DeliveryPlanEntity> deliveryPlanEntityList = new ArrayList<>();
        String planNo = sysClient.getBillNo("dm_delivery_plan");
        int index = 0;
        for (Map<String, Object> planMap:list) {
            index = index+1;
            //获取map的key值
            for (String planMapKey:planMap.keySet()){
                //判断key值是否为计划日期
                if(!"工厂(*)".equals(planMapKey) && !planMapKey.equals("供应商编码(*)")
                        && !planMapKey.equals("采购组(*)") && !planMapKey.equals("MRP区域")
                        && !planMapKey.equals("供应商名称") && !planMapKey.equals("物料编码(*)")
                        && !planMapKey.equals("物料名称") && !planMapKey.equals("物料规格")
                        && !planMapKey.equals("缺料数量") && !planMapKey.equals("日期")
                        && !planMapKey.equals("excelRowNum")){
                    DeliveryPlanEntity deliveryPlan = new DeliveryPlanEntity();
                    //计划单号
                    deliveryPlan.setPlanNo(planNo);
                    deliveryPlan.setPurchasingGroup(planMap.get("采购组(*)").toString().trim());
                    if (!StrUtil.isEmptyIfStr(planMap.get("MRP区域"))){
                        deliveryPlan.setMrpRegion(planMap.get("MRP区域").toString().trim());
                    } else {
                        deliveryPlan.setMrpRegion(planMap.get("工厂(*)").toString().trim());
                    }
                    //查询机构信息
                    DeptVO deptVO = sysClient.queryByDeptCode(planMap.get("工厂(*)").toString().trim(), tenantId);
                    //机构id
                    deliveryPlan.setDeptId(deptVO.getId());
                    //机构编码
                    deliveryPlan.setDeptCode(deptVO.getDeptCode());
                    //机构名称
                    deliveryPlan.setDeptName(deptVO.getDeptName());
                    //查询供应商信息
                    JSONObject vendor = baseClient.getVendor(tenantId, planMap.get("供应商编码(*)").toString().trim());
                    //供应商id（供应商来源id）
                    deliveryPlan.setVendorId(Long.parseLong(vendor.get("soureId").toString().trim()));
                    //供应商编码-erp供应商编码
                    deliveryPlan.setVendorCode(vendor.get("vendorCode").toString().trim());
                    //供应商名称-全称
                    deliveryPlan.setVendorName(vendor.get("vendorName").toString().trim());
                    String goodsErpCode = planMap.get("物料编码(*)").toString().trim();
                    if(SCIENTIFIC_NOTATION_PATTERN.matcher(goodsErpCode).matches()){
                        BigDecimal bd = new BigDecimal(goodsErpCode);
                        goodsErpCode = bd.stripTrailingZeros().toPlainString();
                    }
                    //查询物料信息
                    GoodsVO goodsVo = baseClient.getGoodsVoByCode(tenantId, goodsErpCode);
                    //物料id
                    deliveryPlan.setGoodsId(goodsVo.getId());
                    //物料编码
                    deliveryPlan.setGoodsCode(goodsVo.getGoodsCode());
                    //物料ERP编码
                    deliveryPlan.setGoodsErpCode(goodsVo.getGoodsErpCode());
                    //物料名称
                    deliveryPlan.setGoodsName(goodsVo.getGoodsName());
                    //物料规格
                    deliveryPlan.setGoodsModel(goodsVo.getGoodsModel());
                    //物料图号
                    deliveryPlan.setDrawingNo(goodsVo.getDrawingNo());
                    //计划天数下的计划数量为空或为0
                    if (StrUtil.isEmptyIfStr(planMap.get(planMapKey)) || planMap.get(planMapKey).equals(0)){
                        //计划总数量
                        deliveryPlan.setPlanNum(BigDecimal.ZERO);
                    } else {
                        //计划总数量
                        deliveryPlan.setPlanNum(new BigDecimal(planMap.get(planMapKey).toString().trim()));
                    }
                    if (!StrUtil.isEmptyIfStr(planMap.get("缺料数量"))){
                        //缺料数量
                        deliveryPlan.setShortageNum(new BigDecimal(planMap.get("缺料数量").toString().trim()));
                    }
                    Date planDate;
                    try {
                        planDate = dateFormat.parse(planMapKey);
                        //计划日期
                        deliveryPlan.setPlanDate(planDate);
                    } catch (Exception e) {
                        throw new RRException(String.format("导入的数据第[%s]行日期,字符串转日期时间格式出错,日期格式为yyyy-MM-dd",index,planMapKey));
                    }
                    deliveryPlanEntityList.add(deliveryPlan);
                }
            }
        }
        //匹配计划数据
        return this.importBatchPlanData(deliveryPlanEntityList);
    }

    /**
     * 送货计划动态交期数据分页查询
     * @param params
     * @return
     */
    @Override
    public PageUtils queryDyanPlanRelyDataPage(Map<String, Object> params) {
        // 送货计划日期范围天数
        Integer deliveryPlanDays = 14;
        if (StrUtil.isEmptyIfStr(params.get("deliveryPlanDays"))){
            throw new RRException("请选择送货计划日期范围天数");
        }
        deliveryPlanDays = Integer.parseInt(params.get("deliveryPlanDays").toString().trim());
        params = suppPlanQueryParams(params,deliveryPlanDays);
        List<String> dateList = this.getDateList(params.get("end").toString(), deliveryPlanDays);
        IPage<HashMap<String,Object>> page = new Query<HashMap<String,Object>>().getPage(params);
        List<HashMap<String, Object>> list = this.dynamicListDataByKey(page, params, deliveryPlanDays, dateList);
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 导出动态计划交期回复数据
     * @param params
     * @param response
     */
    @Override
    public void exportDynaPlanReplyData(Map<String, Object> params, HttpServletResponse response) {
        Integer deliveryPlanDays = 14;
        if (!StrUtil.isEmptyIfStr(params.get("deliveryPlanDays"))) {
            deliveryPlanDays = Integer.parseInt(params.get("deliveryPlanDays").toString().trim());
        }
        params = this.suppPlanQueryParams(params,deliveryPlanDays);
        if (StrUtil.isEmptyIfStr(params.get("end"))){
            throw new RRException("请选择计划日期范围");
        }
        List<String> dateList = this.getDateList(params.get("end").toString(), deliveryPlanDays);
        //创建自定义导出模版
        List<ExcelExportEntity> customizeModel = this.createCustomizeModel(params,deliveryPlanDays,dateList);
        //往模版中填充数据
        List<HashMap<String, Object>> list = this.dynamicListDataByKey(null,params,deliveryPlanDays,dateList);
        logger.info("动态数据=====》"+JSONObject.toJSONString(list));
        String xlsFileName = "送货计划交期回复数据.xlsx";
        ExportParams exportParams = new ExportParams();
        exportParams.setStyle(ExcelExportStatisticStyler.class);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, customizeModel, list);
        EasypoiUtil.downLoadExcel(xlsFileName,response,workbook);
    }

    /**
     * 送货计划导入 - 导入动态计划交期回复数据
     * @param objList
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public List<AfterImportErrorPlanVO> importDynaPlanReplyData(List<Object> objList) {
        List<Map<String, Object>> list = toListMap(objList);
        Long tenantId = null;
        VendorVO vendorVo = null;
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(commonService.getTenantInfo().getEntType())){
            vendorVo = baseClient.getVendorVoBySourceId(commonService.getTenantId());
        }
        Assert.isNull(vendorVo, "供应商信息不存在");
        tenantId = vendorVo.getTenantId();
        Assert.isNull(tenantId, "租户ID为空");
        if(CollectionUtil.isEmpty(list)){
            throw new RRException(String.format("导入的数据为空,请检查Excel导入数据"));
        }
        List<DeliveryPlanEntity> deliveryPlanEntityList = new ArrayList<>();
        int index = 0;
        for (Map<String, Object> planMap:list) {
            index = index+1;
            //获取map的key值
            for (String planMapKey:planMap.keySet()){
                //判断key值是否为计划日期
                if(!"供应商编码(*)".equals(planMapKey) && !"供应商名称".equals(planMapKey)
                        && !"采购员名称".equals(planMapKey) && !"物料编码(*)".equals(planMapKey)
                        && !"物料名称".equals(planMapKey) && !"物料规格".equals(planMapKey)
                        && !"交货地址".equals(planMapKey) && !"备注".equals(planMapKey)
                        && !"采购未交数量".equals(planMapKey) && !"汇总量".equals(planMapKey)
                        && !"3天汇总量".equals(planMapKey) && !"5天汇总量".equals(planMapKey)
                        && !"7天汇总量".equals(planMapKey) && !"日期".equals(planMapKey)
                        && !"excelRowNum".equals(planMapKey)){
                    DeliveryPlanEntity deliveryPlan = new DeliveryPlanEntity();
                    deliveryPlan.setTenantId(tenantId);
                    deliveryPlan.setDeptId(28905L);
                    deliveryPlan.setDeptCode("101");
                    deliveryPlan.setDeptName("佛山市顺德区冠宇达电源有限公司");
                    if (ObjectUtil.isEmpty(planMap.get("供应商编码(*)"))){
                        throw new RRException(String.format("导入的数据第[%s]行供应商编码为空,请检查Excel导入数据",index));
                    }
                    if (!planMap.get("供应商编码(*)").toString().trim().equals(vendorVo.getVendorErpCode())){
                        throw new RRException(String.format("导入的数据第[%s]行供应商编码与当前供应商编码不一致,请检查Excel导入数据",index));
                    }
                    deliveryPlan.setVendorId(vendorVo.getSoureId());
                    deliveryPlan.setVendorCode(vendorVo.getVendorErpCode());
                    deliveryPlan.setVendorName(vendorVo.getVendorName());
                    String goodsErpCode = planMap.get("物料编码(*)").toString().trim();
                    if(SCIENTIFIC_NOTATION_PATTERN.matcher(goodsErpCode).matches()){
                        BigDecimal bd = new BigDecimal(goodsErpCode);
                        goodsErpCode = bd.stripTrailingZeros().toPlainString();
                    }
                    deliveryPlan.setGoodsErpCode(goodsErpCode);
                    if (StrUtil.isEmptyIfStr(planMap.get("日期"))){
                       throw new RRException(String.format("导入的数据第[%s]行日期下的数量类型为空,请检查Excel导入数据",index));
                    }
                    if (!"计划数量".equals(planMap.get("日期")) && !"回复数量".equals(planMap.get("日期"))){
                        throw new RRException(String.format("导入的数据第[%s]行日期下的数量类型错误,请检查Excel导入数据",index));
                    }
                    deliveryPlan.setQtyType("计划数量".equals(planMap.get("日期"))?1:2);
                    BigDecimal qty = null;
                    //计划天数下的计划数量不为空
                    if (!StrUtil.isEmptyIfStr(planMap.get(planMapKey))){
                        //计划总数量
                        qty = new BigDecimal(planMap.get(planMapKey).toString().trim());
                    }
                    if (deliveryPlan.getQtyType().equals(1)){
                        deliveryPlan.setPlanNum(qty);
                    }
                    if (deliveryPlan.getQtyType().equals(2)){
                        deliveryPlan.setReplyQty(qty);
                    }
                    Date planDate;
                    try {
                        planDate = dateFormat.parse(planMapKey);
                        //计划日期
                        deliveryPlan.setPlanDate(planDate);
                    } catch (Exception e) {
                        throw new RRException(String.format("导入的数据第[%s]行日期,字符串转日期时间格式出错,日期格式为yyyy-MM-dd",index,planMapKey));
                    }
                    deliveryPlanEntityList.add(deliveryPlan);
                }
            }
        }
        logger.info(JSONObject.toJSONString(deliveryPlanEntityList));
        importUpdatePlanReplyData(deliveryPlanEntityList);
        return Collections.emptyList();
    }

    /**
     * SAP同步下发回货计划到SRM进行分配分发给对应供应商生成送货计划
     * @param deliveryPlanHostVO
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public List<PlanItemResultVO> synchronousDistributionPlanBySAPOrErp(DeliveryPlanHostVO deliveryPlanHostVO) {
        Long tenantId = commonService.getTenantId();
        if (CollectionUtil.isEmpty(deliveryPlanHostVO.getDeliveryPlanVOList())){
            throw new RRException("下发分配要货计划数据不能为空");
        }
        List<PlanItemResultVO> planItemResultVoAllList = new ArrayList<>();
        List<DeliveryPlanVO> planList = deliveryPlanHostVO.getDeliveryPlanVOList().stream().sorted(Comparator.comparing(DeliveryPlanVO::getPlanDate)).collect(Collectors.toList());
        List<DeliveryPlanVO> sortPlanList = planList.stream().sorted(Comparator.comparing(DeliveryPlanVO::getGoodsCode)).collect(Collectors.toList());
        // 筛选出为需要关闭的数据
        List<DeliveryPlanVO> needClosePlanList = sortPlanList.stream().filter(plan -> plan.getPlanNum().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needClosePlanList)){
            for (DeliveryPlanVO plan:needClosePlanList) {
                Map<String,Object> queryParams = new HashMap<>();
                queryParams.put("tenantId",tenantId);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//定义新的日期格式
                queryParams.put("planDate",formatter.format(plan.getPlanDate()));
                queryParams.put("vendorId",plan.getVendorId());
                queryParams.put("goodsId",plan.getGoodsId());
                queryParams.put("deptId",plan.getDeptId());
                queryParams.put("purchaserId)",plan.getPurchaserId());
                queryParams.put("purchasingGroup",plan.getPurchaserGroup());
                queryParams.put("mrpRegion",plan.getMrpRegion());
                queryParams.put("orderType",plan.getOrderType());
                //是否关闭 2-关闭
                queryParams.put("isClosed",DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
                //根据tenantId+当前计划要求天数+供应商id+采购组织id+物料id查询有无计划主表数据
                DeliveryPlanEntity originalDeliveryPlan = this.getOne(this.getQueryWrapper(queryParams));
                if (originalDeliveryPlan != null){
                    //将原计划关闭
                    closePlanByImport(originalDeliveryPlan);
                }
            }
        }
        // 筛选出不需要关闭的数据
        List<DeliveryPlanVO> needMatchList = sortPlanList.stream().filter(plan -> plan.getPlanNum().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(needMatchList)){
            //查询导入中每一条计划下的计划明细行是否存在未进行过业务操作或计划匹配数 = 计划数-送货数不合格数+暂退数的情况
            deliveryPlanItemService.closeNotOperatedPlanItemDataByVo(needMatchList);
            //关闭指定日期范围内的送货计划(范围日期+工厂+供应商+物料+采购组+MRP区域)
            deliveryPlanHostVO.setDeliveryPlanVOList(needMatchList);
            deliveryPlanItemService.closeNotOperatedPlanItemDataByDateRange(deliveryPlanHostVO);
            for (DeliveryPlanVO deliveryPlanVO:needMatchList) {
                Map<String,Object> queryParams = new HashMap<>();
                queryParams.put("tenantId",commonService.getTenantId());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//定义新的日期格式
                queryParams.put("planDate",formatter.format(deliveryPlanVO.getPlanDate()));
                queryParams.put("vendorId",deliveryPlanVO.getVendorId());
                queryParams.put("goodsId",deliveryPlanVO.getGoodsId());
                queryParams.put("deptId",deliveryPlanVO.getDeptId());
                // SAP采购组
                queryParams.put("purchasingGroup",deliveryPlanVO.getPurchaserGroup());
                queryParams.put("purchaserId",deliveryPlanVO.getPurchaserId());
                queryParams.put("orderType",deliveryPlanVO.getOrderType());
                queryParams.put("mrpRegion",deliveryPlanVO.getMrpRegion());
                //是否关闭 2-关闭(查询不为关闭状态的数据)
                queryParams.put("isClosed",DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
                // 根据tenantId+计划单号+要求送货日期+供应商ID+物料ID+机构ID+用户ID条件查询是否存在送货计划主表信息
                DeliveryPlanEntity deliveryPlan = this.getOne(getQueryWrapper(queryParams));
                // 为空就新增
                if (Objects.isNull(deliveryPlan)){
                    deliveryPlan = new DeliveryPlanEntity();
                    deliveryPlan = BeanConverter.convert(deliveryPlanVO,DeliveryPlanEntity.class);
                    deliveryPlan.setTenantPId(0L);
                    //租户组织id
                    deliveryPlan.setTenantId(commonService.getTenantId());
                    //是否匹配标志 0-否
                    deliveryPlan.setIsMatch(WhetherEnum.NO.getCode());
                    deliveryPlan.setDeleteFlag(DeliveryPlanDeleteFlagEnum.NOTCREATE.getCode());
                    // 采购组
                    deliveryPlan.setPurchasingGroup(deliveryPlanVO.getPurchaserGroup());
                    // 采购员ID、名称
                    deliveryPlan.setPurchaserId(Integer.parseInt(deliveryPlanVO.getPurchaserId().toString()));
                    deliveryPlan.setPurchaserName(deliveryPlanVO.getPurchaserName());
                    deliveryPlan.setMrpRegion(deliveryPlanVO.getMrpRegion());
                    this.save(deliveryPlan);
                    // 匹配计划明细行
                    deliveryPlanItemService.matchingPlanItemDataOnThrowAnException(deliveryPlan);
                } else {
                    //现计划数量>原计划数量总数量
                    if (deliveryPlanVO.getPlanNum().compareTo(deliveryPlan.getPlanNum()) > 0){
                        // 计算出现计划大于原计划总数量之间的差值（该差值可作为变更计划明细行大小的根据）
                        deliveryPlan.setNeedCoveringNum(deliveryPlanVO.getPlanNum().subtract(deliveryPlan.getPlanNum()));
                        deliveryPlan.setPlanNum(deliveryPlanVO.getPlanNum());
                        //缺料数量
                        deliveryPlan.setShortageNum(deliveryPlan.getPlanNum());
                        deliveryPlanItemService.overwritePrimaryPlanItem(deliveryPlan,OverwritePlanStatEnum.GREATERTHAN.getCode());
                        this.updateById(deliveryPlan);
                    }
                    //现计划数量<原计划数量总数量
                    if (deliveryPlanVO.getPlanNum().compareTo(deliveryPlan.getPlanNum()) < 0){
                        //现计划小于原计划数量时，需要覆盖的数量 = 现计划总数量
                        deliveryPlan.setNeedCoveringNum(deliveryPlanVO.getPlanNum());
                        deliveryPlan.setPlanNum(deliveryPlanVO.getPlanNum());
                        //缺料数量
                        deliveryPlan.setShortageNum(deliveryPlanVO.getShortageNum());
                        deliveryPlanItemService.overwritePrimaryPlanItem(deliveryPlan,OverwritePlanStatEnum.LESSTHAN.getCode());
                        this.updateById(deliveryPlan);
                    }
                    // 操作类型为关闭删除
                    if (InterfaceOperationEnumeration.DELETE.getValue().equals(deliveryPlanVO.getOperationType())){
                        closePlanByImport(deliveryPlan);
                    }
                }
            }
        }
        return planItemResultVoAllList;
    }

    /**
     * 批量下发ERP已分配指定订单行中的送货计划保存到SRM中
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean syncPlanOrder(List<DeliveryPlanSaveVo> list) {
        if(CollectionUtils.isEmpty(list)){
            throw new RRException("需要保存的数据不能为空");
        }
        DeliveryPlanSaveVo deliveryPlanSaveVo = list.get(0);
        Map<String, List<DeliveryPlanItemSaveVo>> planMap = list.get(0).getPlanItemList().stream().collect(Collectors.groupingBy(item -> item.getTenantId().toString()
                + '&' + item.getDeptId().toString()
                + '&' + item.getVendorId().toString()
                + '&' + item.getGoodsId().toString()
                + '&' + DateUtil.format(item.getPlanDate(), "yyyy-MM-dd")));
        planMap.forEach((key, value) -> {
            DeliveryPlanItemSaveVo planSaveVo = value.get(0);
            Map<String,Object> queryParams = new HashMap<>();
            queryParams.put("tenantId",commonService.getTenantId());
            queryParams.put("planDate",DateUtil.format(planSaveVo.getPlanDate(), "yyyy-MM-dd"));
            queryParams.put("vendorId",planSaveVo.getVendorId());
            queryParams.put("goodsId",planSaveVo.getGoodsId());
            queryParams.put("deptId",planSaveVo.getDeptId());
            //是否关闭
            queryParams.put("isClosed",WhetherEnum.YES.getValue());
            DeliveryPlanEntity deliveryPlan = this.getOne(getQueryWrapper(queryParams));
            if (ObjectUtil.isEmpty(deliveryPlan)){
                deliveryPlan = new DeliveryPlanEntity();
                deliveryPlan = BeanConverter.convert(planSaveVo,DeliveryPlanEntity.class);
                deliveryPlan.setTenantPId(0L);
                deliveryPlan.setTenantId(commonService.getTenantId());
                deliveryPlan.setDeleteFlag(DeliveryPlanDeleteFlagEnum.NOTCREATE.getCode());
                this.save(deliveryPlan);
            }
            Integer operationType = deliveryPlanSaveVo.getOperationType();
            if (operationType == 3){
                deliveryPlanSaveVo.getPlanItemList().forEach(planItem -> planItem.setIsClosed(WhetherEnum.YES.getCode()));
            }
            deliveryPlanItemService.syncPlanOrder(deliveryPlan,value);
            List<DeliveryPlanItemEntity> planItemList = deliveryPlanItemService.getItemListByPlanId(deliveryPlan.getId());
            if (CollectionUtils.isNotEmpty(planItemList)){
                // 汇总planItemList中的matchNum
                BigDecimal planSumNum = planItemList.stream().map(DeliveryPlanItemEntity::getMatchNum).reduce(BigDecimal.ZERO, BigDecimal::add);
                deliveryPlan.setPlanNum(planSumNum);
                this.updateById(deliveryPlan);
            }
        });
        return true;
    }

    /**
     * 批量下发ERP回货计划保存到SRM中-V3
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean syncPlanByErpV3(List<DeliveryPlanSaveVo> list) {
        Map<String, List<DeliveryPlanSaveVo>> planMap = list.stream().collect(Collectors.groupingBy(item -> item.getTenantId().toString()
                + '&' + item.getDeptId().toString()
                + '&' + item.getVendorId().toString()
                + '&' + item.getGoodsId().toString()
                + '&' + DateUtil.format(item.getPlanDate(), "yyyy-MM-dd")));
        planMap.forEach((key, value) -> {
            DeliveryPlanSaveVo planSaveVo = value.get(0);
            Map<String,Object> queryParams = new HashMap<>();
            queryParams.put("tenantId",commonService.getTenantId());
            queryParams.put("planDate",DateUtil.format(planSaveVo.getPlanDate(), "yyyy-MM-dd"));
            queryParams.put("vendorId",planSaveVo.getVendorId());
            queryParams.put("goodsId",planSaveVo.getGoodsId());
            queryParams.put("deptId",planSaveVo.getDeptId());
            //是否关闭
            queryParams.put("isClosed",WhetherEnum.YES.getValue());
            DeliveryPlanEntity deliveryPlan = this.getOne(getQueryWrapper(queryParams));
            if (ObjectUtil.isEmpty(deliveryPlan)){
                deliveryPlan = new DeliveryPlanEntity();
                deliveryPlan = BeanConverter.convert(planSaveVo,DeliveryPlanEntity.class);
                deliveryPlan.setTenantPId(0L);
                deliveryPlan.setTenantId(commonService.getTenantId());
                deliveryPlan.setDeleteFlag(DeliveryPlanDeleteFlagEnum.NOTCREATE.getCode());
                this.save(deliveryPlan);
            } else {
                deliveryPlan.setPlanNum(planSaveVo.getPlanNum());
                if (planSaveVo.getOperationType() == 3){
                    deliveryPlan.setDeleteFlag(WhetherEnum.YES.getCode());
                }
                this.updateById(deliveryPlan);
            }
        });
        return true;
    }

    /**
     *送货计划明细当前页or全部导出
     * @param params
     * @return
     */
    @Override
    public List<DeliveryPlanAllExportVO> exportList(Map<String, Object> params) {
        List<DeliveryPlanEntity> list;
        if (params.containsKey("tenantIds")){
            params.remove("tenantIds");
        }
        params.put("deleteFlag",0);
        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {
            list= this.page(new Query<DeliveryPlanEntity>().getPage(params),getQueryWrapper(params)).getRecords();
        }else{
            list= this.list(getQueryWrapper(params));
        }
        List<DeliveryPlanAllExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, DeliveryPlanAllExportVO.class);
        }
        return resultList;
    }

    /**
     * 送货计划导入
     * 2023-3-22
     * meng
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public void importNew(List<DeliveryPlanImportVO> list) {
        //导入数据不为空
        if(CollectionUtil.isNotEmpty(list)){
            //循环导入的数据
            for (DeliveryPlanImportVO deliveryPlanImportVO:list) {
                DeliveryPlanEntity deliveryPlanEntity = BeanConverter.convert(deliveryPlanImportVO, DeliveryPlanEntity.class);
                deliveryPlanEntity.setPlanNo(sysClient.getBillNo("dm_delivery_plan"));
                deliveryPlanEntity.setTenantId(commonService.getTenantId());
                deliveryPlanEntity.setTenantPId(0L);
                deliveryPlanEntity.setDeleteFlag(0);
                deliveryPlanEntity.setCreationDate(new Date());
                deliveryPlanEntity.setIsMatch(0);
                this.save(deliveryPlanEntity);
                Map<String, Object> params = new HashMap<>();
                params.put("goodsId", deliveryPlanEntity.getGoodsId());
                params.put("tenantId",deliveryPlanEntity.getTenantId());
                params.put("deptName",deliveryPlanEntity.getDeptName());
                if (!StrUtil.isEmptyIfStr(deliveryPlanEntity.getVendorCode())) {
                    params.put("vendorCode", deliveryPlanEntity.getVendorCode());
                }
                BigDecimal planNum = deliveryPlanEntity.getPlanNum();//要求送货数量
                /**
                 * 根据Excel导入文件中的送货日期+供应商+物料编码，
                 * 查询出是否有创建过计划，若有创建过计划直接将原有计划关闭重新匹配新的计划
                 * 注：查询需要计划完成的，只需要查询出已创建部分送货单的计划
                 */
                //根据答交日期和订单数量升序排序 查询剩余可匹配数量的订单
                List<HashMap<String, Object>> deliveryPurItemList = getBaseMapper().getDeliveryPurItemList(params);
                //deliveryPurItemList不为空
                if (CollectionUtil.isEmpty(deliveryPurItemList)){
                    throw new RRException(String.format("找不到采购组织[%s]对应物料编码[%s]已确认的采购订单信息，无法匹配送货计划数据，请检查采购组织信息和物料信息",
                            deliveryPlanEntity.getDeptName(),deliveryPlanEntity.getGoodsErpCode()));
                }
                for (HashMap<String, Object> map : deliveryPurItemList) {
                    DeliveryPlanItemEntity deliveryPlanItemEntity = new DeliveryPlanItemEntity();
                    deliveryPlanItemEntity.setTenantId(commonService.getTenantId());
                    if(!StrUtil.isEmptyIfStr(map.get("deptId"))){
                        deliveryPlanItemEntity.setDeptId(Long.parseLong(map.get("deptId").toString()));
                        deliveryPlanItemEntity.setDeptCode(map.get("deptCode").toString());
                        deliveryPlanItemEntity.setDeptName(map.get("deptName").toString());
                    }
                    deliveryPlanItemEntity.setVendorId(Long.parseLong(map.get("vendorId") + ""));//供应商id
                    deliveryPlanItemEntity.setVendorCode(map.get("vendorCode") + "");//供应商编码
                    deliveryPlanItemEntity.setVendorName(map.get("vendorName") + "");//供应商名称
                    deliveryPlanItemEntity.setDeliveryPlanId(deliveryPlanEntity.getId());//主表Id
                    deliveryPlanItemEntity.setPlanNo(deliveryPlanEntity.getPlanNo());//送货计划单号
                    deliveryPlanItemEntity.setSaleId(Long.parseLong(map.get("purId") + ""));//订单id
                    deliveryPlanItemEntity.setSaleItemId(Long.parseLong(map.get("id") + ""));//订单明细Id
                    deliveryPlanItemEntity.setSaleNo(map.get("purNo") + "");//订单号
                    deliveryPlanItemEntity.setGoodsId(deliveryPlanEntity.getGoodsId());//物料id
                    deliveryPlanItemEntity.setPurchaserName("");//采购员 目前为空
                    deliveryPlanItemEntity.setGoodsCode(deliveryPlanEntity.getGoodsErpCode());//物料编码
                    deliveryPlanItemEntity.setGoodsErpCode(deliveryPlanEntity.getGoodsErpCode());//ERP物料编码
                    deliveryPlanItemEntity.setDeliveryDate(deliveryPlanImportVO.getPlanDate());//计划要求送货日期
                    deliveryPlanItemEntity.setGoodsName(deliveryPlanEntity.getGoodsName());//物料名称
                    deliveryPlanItemEntity.setGoodsModel(deliveryPlanEntity.getGoodsModel());//物料规格
                    deliveryPlanItemEntity.setPlanDate(deliveryPlanEntity.getPlanDate());//计划要求送货日期
                    deliveryPlanItemEntity.setAddress(map.get("address") + "");//送货地址
                    //订单类型
                    if (Integer.parseInt(map.get("orderType") + "") == 1) {
                        deliveryPlanItemEntity.setOrderType(1);
                    } else {
                        deliveryPlanItemEntity.setOrderType(2);
                    }
                    //订单类型
                    deliveryPlanItemEntity.setDeleteFlag(0);//未生成送货单
                    BigDecimal canMatchNum = new BigDecimal(map.get("canMatchNum") + "");
                    //若导入的要求送货计划数量 = 订单剩余匹配数量时
                    if (planNum.compareTo(canMatchNum) == 1) {
                        //送货计划明细行计划匹配数量 = 当前行对应的订单剩余匹配数量
                        deliveryPlanItemEntity.setMatchNum(canMatchNum);
                        //送货计划明细行已制单数量为0
                        deliveryPlanItemEntity.setMakeNum(new BigDecimal(0));
                        deliveryPlanItemService.save(deliveryPlanItemEntity);
                        //导入的要求送货计划数量 = 导入的要求送货计划数量 - 当前行对应的订单剩余匹配数量
                        planNum = planNum.subtract(canMatchNum);
                    } else {
                        //若导入的要求送货计划数量 != 订单剩余匹配数量时
                        deliveryPlanItemEntity.setMatchNum(planNum);
                        deliveryPlanItemEntity.setMakeNum(new BigDecimal(0));
                        deliveryPlanItemService.save(deliveryPlanItemEntity);
                        planNum = BigDecimal.ZERO;
                        //跳出循环
                        break;
                    }
                }
            }
        }
    }

    /**
     * 导入送货计划
     * meng
     * 2021-4-10
     * @param params
     * @return
     */
    @Override
    public List<DeliveryPlanImportVO> importModel(Map<String, Object> params) {
        List<DeliveryPlanImportVO> resultList = new ArrayList<>();
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deliveryPlanDelete(List<DeliveryPlanVO> deliveryPlanList) {
        for (DeliveryPlanVO deliveryPlan : deliveryPlanList) {
            List<DeliveryPlanItemEntity> deliveryPlanItemEntity = deliveryPlanItemService.getByPlanId(deliveryPlan.getPlanId(),deliveryPlan.getPlanLineId());
            if (CollectionUtils.isNotEmpty(deliveryPlanItemEntity)) {
                for (DeliveryPlanItemEntity planItemEntity : deliveryPlanItemEntity) {
                    List<DeliveryItemEntity> list = deliveryItemService.queryByPlanId(deliveryPlan.getPlanLineId(), deliveryPlan.getPlanId());
                    if(CollectionUtils.isNotEmpty(list)) {
                        throw new RRException("送货计划单号" + deliveryPlan.getPlanNo() + "已经生成了送货单据，删除失败！");
                    }
                    deliveryPlanItemService.removeById(planItemEntity.getId());
                    deliveryShoppingCartService.queryByPlanSrmId(planItemEntity.getId());
                    this.removeById(planItemEntity.getDeliveryPlanId());
                }
            } else {
                throw new RRException("送货计划单号" + deliveryPlan.getPlanNo() + "不存在,删除失败");
            }
        }
    }

    @Override
    public BigDecimal qualityByPlan(Long saleId, Long saleItemId) {
     return    this.getBaseMapper().qualityByPlan(saleId,saleItemId);
    }

    @Override
    public List<DeliveryPlanItemVO> qualityByList(Long saleId, Long saleItemId) {
        QueryWrapper<DeliveryPlanItemEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("sale_id",saleId);
        queryWrapper.eq("sale_item_id",saleItemId);
        List<DeliveryPlanItemEntity> list = deliveryPlanItemService.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)) {
            List<DeliveryPlanItemVO> resultList = new ArrayList<>();
                resultList = BeanConverter.convertList(list, DeliveryPlanItemVO.class);
            return resultList;
        }else {
            return null;
        }
    }

    @Override
    public boolean qualityByPlanlist(Long saleId, Long saleItemId) {
        QueryWrapper<DeliveryPlanItemEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("sale_id",saleId);
        queryWrapper.eq("sale_item_id",saleItemId);
        queryWrapper.eq("delete_flag",0);
        List<DeliveryPlanItemEntity> list = deliveryPlanItemService.list(queryWrapper);
        if(CollectionUtils.isNotEmpty(list)) {
            throw new RRException("当前订单计划已占用，关闭失败！");
        }else {
            return false;
        }
    }

    @Override
    public PageUtils findDeliveryPlan(Map<String, Object> params) {
        if (params.containsKey("tenantIds") && !StrUtil.isEmptyIfStr(params.get("tenantIds"))) { // 采购方进入送货列表
            params.put("tenantId", commonService.getTenantId());
        } else if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))) { // 供应商进入送货列表
            params.put("vendorId", commonService.getTenantId());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> dataList = new ArrayList<>();
        Calendar c = Calendar.getInstance();
        Integer deliveryPlanDays = 7;
        if (!StrUtil.isBlankIfStr(params.get("deliveryPlanDays")+"")) {
            deliveryPlanDays = Integer.parseInt(params.get("deliveryPlanDays") + "");
        } else {}
        try {
            if (params.get("startDates") != null && !params.get("startDates").equals("")){
                String[] split = params.get("startDates").toString().split(" 至 ");
                params.put("start", split[0] + " 00:00:00");
                params.put("end", split[1] + " 23:59:59");
            }
            if (params.get("startDate") != null && !params.get("startDate").equals("")) {
                params.remove("startDates");
                String[] split = params.get("startDate").toString().split(" 至 ");
                params.put("start", split[0] + " 00:00:00");
                params.put("end", split[1] + " 23:59:59");
            }
            Date sd = sdf.parse(params.get("end") + "");
            c.setTime(sd);
            dataList.add(sdf.format(c.getTime()));
            for (int i = 1; i <= deliveryPlanDays; i++) {
                c.add(Calendar.DATE, -1);
                dataList.add(sdf.format(c.getTime()));
            }
        } catch (Exception e) {
            throw new RRException("日期输入有误");
        }
        StringBuffer selectBoardColumns = new StringBuffer();
        StringBuffer filterCondition = new StringBuffer();
        for (int i = 0; i <= deliveryPlanDays; i++) {
            selectBoardColumns.append(
                "(SUM(CASE DATE_FORMAT(plan_date,'%Y-%m-%d')WHEN '"
                        + dataList.get(i)
                        + "'"
                        + "THEN match_num ELSE 0 END)) AS '"
                        + dataList.get(i) +"_match'"
                        + ",");
            selectBoardColumns.append(
                    "(SUM(CASE DATE_FORMAT(plan_date,'%Y-%m-%d')WHEN '"
                            + dataList.get(i)
                            + "'"
                            + "THEN (select ifnull(sum(dev_num),0) from dm_delivery_item where plan_srm_line_id= ddpi.id) ELSE 0 END)) AS '"
                            + dataList.get(i) +"_fix_num'"
                            + ",");
            selectBoardColumns.append(
                    "(SUM(CASE DATE_FORMAT(plan_date,'%Y-%m-%d')WHEN '"
                            + dataList.get(i)
                            + "'"
                            + "THEN (select ifnull(sum(inv_num),0) from dm_delivery_item where plan_srm_line_id= ddpi.id) ELSE 0 END)) AS '"
                            + dataList.get(i) +"_erp_num'"
                            + ",");
        }
        params.put("selectBoardColumns", selectBoardColumns);
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        if (!StrUtil.isBlankIfStr(params.get("reportType"))){
            if (params.get("reportType").toString().equals("1")){
                List<HashMap<String, Object>> list = getBaseMapper().findDeliveryPlan(page,params);
                page.setRecords(list);
                return new PageUtils(page);
            }else if(params.get("reportType").toString().equals("2")){
                List<HashMap<String, Object>> list = getBaseMapper().findDeliveryPlanTwo(page,params);
                page.setRecords(list);
                return new PageUtils(page);
            }
        }
        return new PageUtils();
    }

    @Override
    public PageUtils findDeliveryVendorPlan(Map<String, Object> params) {
        if (params.containsKey("vendorIds") && !StrUtil.isEmptyIfStr(params.get("vendorIds"))) { // 供应商进入送货列表
            params.put("vendorId", commonService.getTenantId());
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> dataList = new ArrayList<>();
        Calendar c = Calendar.getInstance();
        Integer deliveryPlanDays = 7;
        if (!StrUtil.isBlankIfStr(params.get("deliveryPlanDays")+"")) {
            deliveryPlanDays = Integer.parseInt(params.get("deliveryPlanDays") + "");
        } else {}
        try {
            if (params.get("startDates") != null && !params.get("startDates").equals("")){
                String[] split = params.get("startDates").toString().split(" 至 ");
                params.put("start", split[0] + " 00:00:00");
                params.put("end", split[1] + " 23:59:59");
            }
            if (params.get("startDate") != null && !params.get("startDate").equals("")) {
                params.remove("startDates");
                String[] split = params.get("startDate").toString().split(" 至 ");
                params.put("start", split[0] + " 00:00:00");
                params.put("end", split[1] + " 23:59:59");
            }
            Date sd = sdf.parse(params.get("end") + "");
            c.setTime(sd);
            dataList.add(sdf.format(c.getTime()));
            for (int i = 1; i <= deliveryPlanDays; i++) {
                c.add(Calendar.DATE, -1);
                dataList.add(sdf.format(c.getTime()));
            }
        } catch (Exception e) {
            throw new RRException("日期输入有误");
        }
        StringBuffer selectBoardColumns = new StringBuffer();
        for (int i = 0; i <= deliveryPlanDays; i++) {
            selectBoardColumns.append(
                    "(SUM(CASE DATE_FORMAT(plan_date,'%Y-%m-%d')WHEN '"
                            + dataList.get(i)
                            + "'"
                            + "THEN match_num ELSE 0 END)) AS '"
                            + dataList.get(i) +"_match'"
                            + ",");
            selectBoardColumns.append(
                    "(SUM(CASE DATE_FORMAT(plan_date,'%Y-%m-%d')WHEN '"
                            + dataList.get(i)
                            + "'"
                            + "THEN (select ifnull(sum(dev_num),0) from dm_delivery_item where plan_srm_line_id= ddpi.id) ELSE 0 END)) AS '"
                            + dataList.get(i) +"_fix_num'"
                            + ",");
            selectBoardColumns.append(
                    "(SUM(CASE DATE_FORMAT(plan_date,'%Y-%m-%d')WHEN '"
                            + dataList.get(i)
                            + "'"
                            + "THEN (select ifnull(sum(inv_num),0) from dm_delivery_item where plan_srm_line_id= ddpi.id) ELSE 0 END)) AS '"
                            + dataList.get(i) +"_erp_num'"
                            + ",");
        }
        params.put("selectBoardColumns", selectBoardColumns);
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().findDeliveryVendorPlan(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public void downloadErrorFile(String fileName, HttpServletResponse response, Workbook workbook) {
        EasypoiUtil.downLoadExcel(fileName,response,workbook);
    }

    /**
     * 获取采购组织机构信息
     * @param params
     * @return
     */
    @Override
    public DeptInfoVo getDeptInfo(Map<String, Object> params) {
        DeptInfoVo deptInfo = this.getBaseMapper().getDeptInfo(params);
        return deptInfo;
    }

    /**
     * 根据id修改计划总数
     * @param id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean updatePlanNumById(Long id) {
        // 查询送货计划信息
        DeliveryPlanEntity planInfo = this.getInfo(id);
        // 汇总明细行的已匹配计划数
        BigDecimal totalPlanNum = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(planInfo.getPlanItemList())){
            // 汇总明细行的已匹配计划数
            totalPlanNum = planInfo.getPlanItemList().stream().map(DeliveryPlanItemEntity::getMatchNum).reduce(BigDecimal.ZERO, (b1, b2) -> b1.add(b2));
        }
        planInfo.setPlanNum(totalPlanNum);
        planInfo.setShortageNum(totalPlanNum);
        // 计划总数小于或等于0
        if (planInfo.getPlanNum().compareTo(BigDecimal.ZERO) < 0 || planInfo.getPlanNum().compareTo(BigDecimal.ZERO) == 0){
            planInfo.setDeleteFlag(DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
        }
        this.updateById(planInfo);
        return Boolean.TRUE;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean batchConfirmPlanByVendor(List<DeliveryPlanEntity> list) {
        if (CollectionUtil.isEmpty(list)){
            throw new RRException("请选择需要确认交期的数据");
        }
        List<ErpPlanReplyVo> erpPlanReplyList = new ArrayList<>();
        list.forEach(plan -> {
            if (ObjectUtil.isEmpty(plan.getReplyQty()) && ObjectUtil.isNotEmpty(plan.getPlanNum())){
                plan.setReplyQty(plan.getPlanNum());
            }
            // 组装回传至ERP的接口数据
            ErpPlanReplyVo erpPlanReplyVo = new ErpPlanReplyVo();
            erpPlanReplyVo.setVendor_code(plan.getVendorCode());
            erpPlanReplyVo.setDept_code(plan.getDeptCode());
            erpPlanReplyVo.setGoods_erp_code(plan.getGoodsErpCode());
            erpPlanReplyVo.setPlan_date(DateUtil.format(plan.getPlanDate(), "yyyy-MM-dd"));
            erpPlanReplyVo.setPlan_num(plan.getPlanNum());
            erpPlanReplyVo.setRemark(plan.getRemark());
            erpPlanReplyList.add(erpPlanReplyVo);
            this.updateById(plan);
        });
        executeJoggleService.execute("FSGYD.KingdeeK3Cloud.PlugIn.SRMSupplierReplyWebApiService.SRMSupplierReply,FSGYD.KingdeeK3Cloud.PlugIn", JSON.toJSONString(erpPlanReplyList),list.get(0).getTenantId());
        return true;
    }


    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 自定义模版信息
     * @param params
     * @return
     */
    private List<ExcelExportEntity> createCustomizeModel(Map<String, Object> params,Integer deliveryPlanDays,List<String> dateList) {
        //表头的集合，用于添加表头
        List<ExcelExportEntity> entityList = new ArrayList<>();
        //ExcelExportEntity构造参数
        ExcelExportEntity vendorCode = new ExcelExportEntity("供应商编码(*)", "vendorCode", 15);
        //增加列表頭-供应商编码
        entityList.add(vendorCode);
        ExcelExportEntity vendorName = new ExcelExportEntity("供应商名称", "vendorName", 15);
        //增加列表頭-供应商名称
        entityList.add(vendorName);
        ExcelExportEntity purName = new ExcelExportEntity("采购员名称", "purName", 15);
        //增加列表頭-采购员名称
        entityList.add(purName);
        ExcelExportEntity goodsErpCode = new ExcelExportEntity("物料编码(*)", "goodsErpCode", 15);
        //增加列表頭-物料编码
        entityList.add(goodsErpCode);
        ExcelExportEntity goodsName = new ExcelExportEntity("物料名称", "goodsName", 20);
        //增加列表頭-物料名称
        entityList.add(goodsName);
        ExcelExportEntity goodsModel = new ExcelExportEntity("物料规格", "goodsModel", 20);
        //增加列表頭-物料规格
        entityList.add(goodsModel);
        ExcelExportEntity warehouseName = new ExcelExportEntity("交货地址", "warehouseName", 20);
        //增加列表頭-交货地址
        entityList.add(warehouseName);
        ExcelExportEntity remark = new ExcelExportEntity("备注", "remark", 20);
        //增加列表頭-备注
        entityList.add(remark);
        ExcelExportEntity unPaidQty = new ExcelExportEntity("采购未交数量", "unPaidQty", 20);
        //增加列表頭-采购未交数量
        entityList.add(unPaidQty);
        ExcelExportEntity planSumQty = new ExcelExportEntity("汇总量", "planSumQty", 20);
        //增加列表頭-汇总量
        entityList.add(planSumQty);
        ExcelExportEntity planSumQty3 = new ExcelExportEntity("3天汇总量", "planSumQty3", 20);
        //增加列表頭-3天汇总量
        entityList.add(planSumQty3);
        ExcelExportEntity planSumQty5 = new ExcelExportEntity("5天汇总量", "planSumQty5", 20);
        //增加列表頭-5天汇总量
        entityList.add(planSumQty5);
        ExcelExportEntity planSumQty7 = new ExcelExportEntity("7天汇总量", "planSumQty7", 20);
        //增加列表頭-7天汇总量
        entityList.add(planSumQty7);
        ExcelExportEntity qtyName = new ExcelExportEntity("日期", "quantityType", 20);
        //增加列表頭-缺料数量
        entityList.add(qtyName);
        for (int i = deliveryPlanDays; i >= 0 ; i--) {
            //当前获取的日期
            String currentDay = dateList.get(i);
            String year = currentDay.substring(0, 4);
            String month = currentDay.substring(5, 7);
            String day = currentDay.substring(8, 10);
            String tableName = year+"-"+month+"-"+day;
            String fieldName = "planDate"+year+month+day;
            ExcelExportEntity planDate = new ExcelExportEntity(tableName, fieldName, 12);
            //每一天的物料计划总数
            entityList.add(planDate);
        }
        return entityList;
    }

    /**
     * 动态查询数据
     * @param page
     * @param params
     * @param deliveryPlanDays
     * @param dateList
     * @return
     */
    private List<HashMap<String, Object>> dynamicListDataByKey(IPage<HashMap<String,Object>> page,
                                                               Map<String,Object> params,
                                                               Integer deliveryPlanDays,
                                                               List<String> dateList){
        StringBuilder planQtySql = new StringBuilder();
        StringBuilder replyQtySql = new StringBuilder();
        for (int i = deliveryPlanDays; i >= 0 ; i--) {
            //当前获取的日期
            String currentDay = dateList.get(i);
            String year = currentDay.substring(0, 4);
            String month = currentDay.substring(5, 7);
            String day = currentDay.substring(8, 10);
            String yearMonthDay = year+"-"+month+"-"+day;
            String fieldName = "plan_date"+year+'_'+month+'_'+day;
            planQtySql.append("(SELECT SUM(ppn.plan_num) FROM dm_delivery_plan ppn " +
                    "WHERE ppn.tenant_id = dp.tenant_id AND ppn.vendor_id = dp.vendor_id " +
                    "AND ppn.dept_id = dp.dept_id " +
                    "AND ppn.goods_id = dp.goods_id AND ppn.plan_date " +
                    "BETWEEN '"+yearMonthDay+" 00:00:00' AND '"+yearMonthDay+" 23:59:59' AND ppn.delete_flag = 0) AS "+fieldName+ ",");
            replyQtySql.append("(SELECT SUM(ppn.reply_qty) FROM dm_delivery_plan ppn " +
                    "WHERE ppn.tenant_id = dp.tenant_id AND ppn.vendor_id = dp.vendor_id " +
                    "AND ppn.dept_id = dp.dept_id " +
                    "AND ppn.goods_id = dp.goods_id AND ppn.plan_date " +
                    "BETWEEN '"+yearMonthDay+" 00:00:00' AND '"+yearMonthDay+" 23:59:59' AND ppn.delete_flag = 0) AS "+fieldName+ ",");
        }
        params.put("planQtySql", planQtySql.toString());
        params.put("replyQtySql", replyQtySql.toString());
        List<HashMap<String, Object>> list = new ArrayList<>();
        if (ObjectUtil.isEmpty(page)){
            // 非分页查询
            list = this.getBaseMapper().queryPlanReplyReportList(params);
        } else {
            // 分页查询
            list = this.getBaseMapper().queryPlanReplyReportList(page, params);
        }
        List<HashMap<String, Object>> newList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)){
            List<String> goodsErpCodeList = list.stream()
                    .filter(item -> !StrUtil.isEmptyIfStr(item.get("goodsErpCode")))
                    .map(item -> item.get("goodsErpCode").toString())
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, GoodsVO> goodsMap = this.queryGoodsList(goodsErpCodeList);
            Map<String, List<HashMap<String, Object>>> planGroupMap = list.stream()
                    .collect(Collectors.groupingBy(item -> item.get("vendorId").toString()
                            + "&" + item.get("goodsId").toString()
                            + "&" + item.get("tenantId").toString()
                            + "&" + item.get("deptId").toString()
                    ));
            planGroupMap.forEach((key, value) -> {
                String[] mapKeys = key.split("&");
                List<DeliveryPlanEntity> statisticsDataList = this.list(new LambdaQueryWrapper<DeliveryPlanEntity>()
                        .eq(DeliveryPlanEntity::getTenantId, Long.parseLong(mapKeys[2].trim()))
                        .eq(DeliveryPlanEntity::getDeptId, Long.parseLong(mapKeys[3].trim()))
                        .eq(DeliveryPlanEntity::getVendorId, Long.parseLong(mapKeys[0].trim()))
                        .eq(DeliveryPlanEntity::getGoodsId, Long.parseLong(mapKeys[1].trim()))
                        .between(DeliveryPlanEntity::getPlanDate, params.get("start"), params.get("end"))
                );
                //
                PurQuery purQuery = new PurQuery();
                purQuery.setTenantId(Long.parseLong(mapKeys[2].trim()));
                purQuery.setDeptId(Long.parseLong(mapKeys[3].trim()));
                purQuery.setVendorId(Long.parseLong(mapKeys[0].trim()));
                purQuery.setGoodsId(Long.parseLong(mapKeys[1].trim()));
                BigDecimal orderUnpaidQty = orderClient.queryPurOrderUnpaidQty(purQuery);
                // 将statisticsDataList中的planNum进行汇总
                BigDecimal sumPlanNum = statisticsDataList.stream()
                        .map(e -> Optional.ofNullable(e.getPlanNum()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sumPlanNum3Days = null;
                BigDecimal sumPlanNum5Days = null;
                BigDecimal sumPlanNum7Days = null;

                // 获取当前日期
                LocalDate currentDate = LocalDate.now();

                // 计算各时间窗口的统计值
                if (CollectionUtil.isNotEmpty(statisticsDataList)) {
                    // 统计3天窗口
                    LocalDate date3Days = currentDate.plusDays(2); // 包含当天共3天
                    sumPlanNum3Days = statisticsDataList.stream()
                            .filter(e -> {
                                LocalDate planDate = e.getPlanDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                                return !planDate.isAfter(date3Days) && !planDate.isBefore(currentDate);
                            })
                            .map(e -> Optional.ofNullable(e.getPlanNum()).orElse(BigDecimal.ZERO)) // 处理null值
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 统计5天窗口
                    LocalDate date5Days = currentDate.plusDays(4);
                    sumPlanNum5Days = statisticsDataList.stream()
                            .filter(e -> {
                                LocalDate planDate = e.getPlanDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                                return !planDate.isAfter(date5Days) && !planDate.isBefore(currentDate);
                            })
                            .map(e -> Optional.ofNullable(e.getPlanNum()).orElse(BigDecimal.ZERO)) // 处理null值
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 统计7天窗口
                    LocalDate date7Days = currentDate.plusDays(6);
                    sumPlanNum7Days = statisticsDataList.stream()
                            .filter(e -> {
                                LocalDate planDate = e.getPlanDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                                return !planDate.isAfter(date7Days) && !planDate.isBefore(currentDate);
                            })
                            .map(e -> Optional.ofNullable(e.getPlanNum()).orElse(BigDecimal.ZERO)) // 处理null值
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                for (HashMap<String, Object> item:value){
                    GoodsVO goodsErpCode = goodsMap.get(item.get("goodsErpCode").toString());
                    item.put("warehouseName", goodsErpCode.getWarehouseName());
                    item.put("unPaidQty", orderUnpaidQty);
                    item.put("planSumQty", sumPlanNum);
                    item.put("planSumQty3", sumPlanNum3Days);
                    item.put("planSumQty5", sumPlanNum5Days);
                    item.put("planSumQty7", sumPlanNum7Days);
                    newList.add(item);
                }
            });
        }
        return list;
    }

    /**
     * 送货计划导入-匹配计划数据
     * @param list
     * @return
     */
    @GlobalTransactional(rollbackFor = Throwable.class)
    private List<AfterImportErrorPlanVO> importBatchPlanData(List<DeliveryPlanEntity> list){
        Long tenantId = commonService.getTenantId();
        List<AfterImportErrorPlanVO> afterImportErrorPlanVOList = new ArrayList<>();
        List<DeliveryPlanEntity> planList = list.stream().sorted(Comparator.comparing(DeliveryPlanEntity::getPlanDate)).collect(Collectors.toList());
        List<DeliveryPlanEntity> sortPlanList = planList.stream().sorted(Comparator.comparing(DeliveryPlanEntity::getGoodsErpCode)).collect(Collectors.toList());
        // 筛选出为需要关闭的数据
        List<DeliveryPlanEntity> needClosePlanList = sortPlanList.stream().filter(plan -> plan.getPlanNum().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needClosePlanList)){
            for (DeliveryPlanEntity plan:needClosePlanList) {
                Map<String,Object> queryParams = new HashMap<>();
                queryParams.put("tenantId",plan.getTenantId());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//定义新的日期格式
                queryParams.put("planDate",formatter.format(plan.getPlanDate()));
                queryParams.put("vendorId",plan.getVendorId());
                queryParams.put("goodsId",plan.getGoodsId());
                queryParams.put("deptId",plan.getDeptId());
                queryParams.put("purchasingGroup",plan.getPurchasingGroup());
                queryParams.put("mrpRegion",plan.getMrpRegion());
                //是否关闭 2-关闭
                queryParams.put("isClosed",DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
                //根据tenantId+当前计划要求天数+供应商id+采购组织id+物料id查询有无计划主表数据
                DeliveryPlanEntity originalDeliveryPlan = this.getOne(this.getQueryWrapper(queryParams));
                if (originalDeliveryPlan != null){
                    //将原计划关闭
                    closePlanByImport(originalDeliveryPlan);
                }
            }
        }
        List<DeliveryPlanEntity> needMatchList = sortPlanList.stream().filter(plan -> plan.getPlanNum().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        //list --- 需要进行匹配计划的数据
        if(CollectionUtil.isNotEmpty(needMatchList)){
            //查询导入中每一条计划下的计划明细行是否存在未进行过业务操作或计划匹配数 = 计划数-送货数不合格数+暂退数的情况
            deliveryPlanItemService.closeNotOperatedPlanItemData(needMatchList) ;
            for (DeliveryPlanEntity deliveryPlan:needMatchList) {
                Map<String,Object> queryParams = new HashMap<>();
                queryParams.put("tenantId",deliveryPlan.getTenantId());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//定义新的日期格式
                queryParams.put("planDate",formatter.format(deliveryPlan.getPlanDate()));
                queryParams.put("vendorId",deliveryPlan.getVendorId());
                queryParams.put("goodsId",deliveryPlan.getGoodsId());
                queryParams.put("purchasingGroup",deliveryPlan.getPurchasingGroup());
                queryParams.put("mrpRegion",deliveryPlan.getMrpRegion());
                //是否关闭 2-关闭
                queryParams.put("isClosed",DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
                //根据tenantId+当前计划要求天数+供应商id+采购组织id+物料id查询有无计划主表数据
                DeliveryPlanEntity originalDeliveryPlan = this.getOne(this.getQueryWrapper(queryParams));
                if (originalDeliveryPlan == null){
                    //只有计划总数>0,才新增计划主表以及匹配计划明细行信息
                    if (deliveryPlan.getPlanNum().compareTo(BigDecimal.ZERO) == 1){
                        deliveryPlan.setTenantPId(0L);
                        //租户组织id
                        deliveryPlan.setTenantId(tenantId);
                        //是否匹配标志 0-否
                        deliveryPlan.setIsMatch(WhetherEnum.NO.getCode());
                        deliveryPlan.setDeleteFlag(DeliveryPlanDeleteFlagEnum.NOTCREATE.getCode());
                        this.save(deliveryPlan);
                        //匹配计划明细数据
                        String errorMsg = deliveryPlanItemService.matchingPlanItemData(deliveryPlan);
                        //错误信息不为空
                        if(!StrUtil.isEmptyIfStr(errorMsg)){
                            AfterImportErrorPlanVO afterImportErrorPlanVO = this.getAfterImportErrorPlanVO(deliveryPlan, errorMsg, OverwritePlanStatEnum.NEWADD.getCode(),
                                    BigDecimal.ZERO, deliveryPlan.getPlanNum());
                            //删除上面新增的计划主表数据
                            this.remove(new LambdaQueryWrapper<DeliveryPlanEntity>().eq(DeliveryPlanEntity::getId,deliveryPlan.getId()));
                            afterImportErrorPlanVOList.add(afterImportErrorPlanVO);
                        } else {
                            //推送站内信信息
                            JSONObject mailJson = new JSONObject();
                            mailJson.put("tenantId",deliveryPlan.getVendorId());
                            mailJson.put("userId",0L);
                            mailJson.put("content",deliveryPlan.getDeptName()+"新发布了一条送货计划:"+deliveryPlan.getPlanNo()+",请查看");
                            mailJson.put("menuTitle","送货看板");//菜单标题
                            mailJson.put("url","dm/deliveryBoard/vendor?planNo="+deliveryPlan.getPlanNo());//菜单地址
                            sysClient.sendMail(mailJson);
                        }
                    }
                } else {
                    //现计划的计划总数量 > 原计划的计划总数量时,更改覆盖原计划数据
                    if (deliveryPlan.getPlanNum().compareTo(originalDeliveryPlan.getPlanNum()) == 1){
                        BigDecimal originalPlanNum = originalDeliveryPlan.getPlanNum();
                        originalDeliveryPlan.setNeedCoveringNum(deliveryPlan.getPlanNum().subtract(originalDeliveryPlan.getPlanNum()));
                        originalDeliveryPlan.setPlanNum(deliveryPlan.getPlanNum());
                        //缺料数量
                        originalDeliveryPlan.setShortageNum(originalDeliveryPlan.getPlanNum());
                        //覆盖原计划
                        String errorMsg = deliveryPlanItemService.overwriteOriginaPlanItem(originalDeliveryPlan, OverwritePlanStatEnum.GREATERTHAN.getCode());
                        if (!StrUtil.isEmptyIfStr(errorMsg)){
                            AfterImportErrorPlanVO afterImportErrorPlanVO = this.getAfterImportErrorPlanVO(deliveryPlan, errorMsg, OverwritePlanStatEnum.GREATERTHAN.getCode(),
                                    originalPlanNum, deliveryPlan.getPlanNum());
                            afterImportErrorPlanVOList.add(afterImportErrorPlanVO);
                            originalDeliveryPlan.setPlanNum(originalPlanNum);
                            originalDeliveryPlan.setShortageNum(originalPlanNum);
                            this.updateById(originalDeliveryPlan);
                        } else {
                            this.updateById(originalDeliveryPlan);
                            //推送站内信信息
                            JSONObject mailJson = new JSONObject();
                            mailJson.put("tenantId",deliveryPlan.getVendorId());
                            mailJson.put("userId",0L);
                            mailJson.put("content",deliveryPlan.getDeptName()+"将原计划单号:"+deliveryPlan.getPlanNo()+"中的计划数覆盖更新了,请查看");
                            mailJson.put("menuTitle","送货看板");//菜单标题
                            mailJson.put("url","dm/deliveryBoard/vendor?planNo="+deliveryPlan.getPlanNo());//菜单地址
                            sysClient.sendMail(mailJson);
                        }
                        continue;
                    }
                    //若现计划天数中的计划数量为空为0时，将原计划关闭
                    if (deliveryPlan.getPlanNum().compareTo(BigDecimal.ZERO) == 0){
                        continue;
                    }
                    //现计划的计划总数量 = 原计划的计划总数量
                    if (deliveryPlan.getPlanNum().compareTo(originalDeliveryPlan.getPlanNum()) == 0){
                        continue;
                    }
                    //现计划的计划总数量 < 原计划的计划总数量时
                    if (deliveryPlan.getPlanNum().compareTo(originalDeliveryPlan.getPlanNum()) == -1){
                        BigDecimal originalPlanNum = originalDeliveryPlan.getPlanNum();
                        //现计划小于原计划数量时，需要覆盖的数量 = 现计划总数量
                        originalDeliveryPlan.setNeedCoveringNum(deliveryPlan.getPlanNum());
                        originalDeliveryPlan.setPlanNum(deliveryPlan.getPlanNum());
                        //缺料数量
                        originalDeliveryPlan.setShortageNum(deliveryPlan.getShortageNum());
                        //覆盖原计划
                        String errorMsg = deliveryPlanItemService.overwriteOriginaPlanItem(originalDeliveryPlan, OverwritePlanStatEnum.LESSTHAN.getCode());
                        if (!StrUtil.isEmptyIfStr(errorMsg)){
                            AfterImportErrorPlanVO afterImportErrorPlanVO = this.getAfterImportErrorPlanVO(deliveryPlan, errorMsg, OverwritePlanStatEnum.LESSTHAN.getCode(),
                                    originalPlanNum, deliveryPlan.getPlanNum());
                            afterImportErrorPlanVOList.add(afterImportErrorPlanVO);
                            originalDeliveryPlan.setPlanNum(originalPlanNum);
                            originalDeliveryPlan.setShortageNum(originalPlanNum);
                            this.updateById(originalDeliveryPlan);
                        } else {
                            this.updateById(originalDeliveryPlan);
                            //推送站内信信息
                            JSONObject mailJson = new JSONObject();
                            mailJson.put("tenantId",deliveryPlan.getVendorId());
                            mailJson.put("userId",0L);
                            mailJson.put("content",deliveryPlan.getDeptName()+"将原计划单号:"+deliveryPlan.getPlanNo()+"中的计划数覆盖更新了,请查看");
                            mailJson.put("menuTitle","送货看板");//菜单标题
                            mailJson.put("url","dm/deliveryBoard/vendor?planNo="+deliveryPlan.getPlanNo());//菜单地址
                            sysClient.sendMail(mailJson);
                        }
                        continue;
                    }
                }
            }
        }
        return afterImportErrorPlanVOList;
    }

    /**
     * 导入覆盖原计划/新增计划回复交期数据
     * @param list
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    private List<AfterImportErrorPlanVO> importUpdatePlanReplyData(List<DeliveryPlanEntity> list){
        if (CollectionUtil.isNotEmpty(list)){
            list = list.stream().filter(item -> PlanReplyTypeEnum.REPLY_NUM.getValue().equals(item.getQtyType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(list)){
                List<String> goodsErpCodeList = list.stream().map(DeliveryPlanEntity::getGoodsErpCode).distinct().collect(Collectors.toList());
                List<Long> tenantIdList = list.stream().map(DeliveryPlanEntity::getTenantId).distinct().collect(Collectors.toList());
                Map<String, GoodsVO> goodsMap = this.queryGoodsList(goodsErpCodeList);
                List<ErpPlanReplyVo> erpPlanReplyList = new ArrayList<>();
                // 用于收集需要发送通知的送货计划数据
                List<DeliveryPlanEntity> notificationPlans = new ArrayList<>();
                list.forEach(plan -> {
                    GoodsVO goodsVO = goodsMap.get(plan.getGoodsErpCode());
                    if (ObjectUtil.isNotEmpty(goodsVO)){
                        Map<String,Object> queryParams = new HashMap<>();
                        queryParams.put("tenantId",plan.getTenantId());
                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");//定义新的日期格式
                        queryParams.put("planDate",formatter.format(plan.getPlanDate()));
                        queryParams.put("vendorId",plan.getVendorId());
                        queryParams.put("goodsId",goodsVO.getId());
                        queryParams.put("deptId",plan.getDeptId());
                        //查询不为关闭
                        queryParams.put("isClosed",DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
                        //根据tenantId+当前计划要求天数+供应商id+采购组织id+物料id查询有无计划主表数据
                        DeliveryPlanEntity originalDeliveryPlan = this.getOne(this.getQueryWrapper(queryParams));
                        if (ObjectUtil.isNotEmpty(originalDeliveryPlan)){
                            // 修改回复数量
                            originalDeliveryPlan.setReplyQty(plan.getReplyQty());
                            originalDeliveryPlan.setRemark(plan.getRemark());
                            this.updateById(originalDeliveryPlan);
                            notificationPlans.add(originalDeliveryPlan);
                        } else {
                            plan.setGoodsId(goodsVO.getId());
                            plan.setGoodsErpCode(goodsVO.getGoodsErpCode());
                            plan.setGoodsCode(goodsVO.getGoodsCode());
                            plan.setGoodsName(goodsVO.getGoodsName());
                            plan.setGoodsModel(goodsVO.getGoodsModel());
                            plan.setRemark(plan.getRemark());
                            plan.setDeleteFlag(DeliveryPlanDeleteFlagEnum.NOTCREATE.getCode());
                            this.save(plan);
                            notificationPlans.add(plan);
                        }
                        // 组装回传至ERP的接口数据
                        ErpPlanReplyVo erpPlanReplyVo = new ErpPlanReplyVo();
                        erpPlanReplyVo.setVendor_code(plan.getVendorCode());
                        erpPlanReplyVo.setDept_code(plan.getDeptCode());
                        erpPlanReplyVo.setGoods_erp_code(plan.getGoodsErpCode());
                        erpPlanReplyVo.setPlan_date(DateUtil.format(plan.getPlanDate(), "yyyy-MM-dd"));
                        erpPlanReplyVo.setPlan_num(ObjectUtil.isEmpty(plan.getPlanNum()) ? BigDecimal.ZERO : plan.getPlanNum());
                        erpPlanReplyVo.setReply_qty(ObjectUtil.isEmpty(plan.getReplyQty()) ? BigDecimal.ZERO : plan.getReplyQty());
                        erpPlanReplyVo.setRemark(StrUtil.isEmptyIfStr(plan.getRemark()) ? "" : plan.getRemark());
                        erpPlanReplyList.add(erpPlanReplyVo);
                    }
                });
                // 发送站内信息和邮件通知采购员
                this.sendReplyPlanMsgToPur(notificationPlans);
                try {
                    executeJoggleService.execute("FSGYD.KingdeeK3Cloud.PlugIn.SRMSupplierReplyWebApiService.SRMSupplierReply,FSGYD.KingdeeK3Cloud.PlugIn", JSON.toJSONString(erpPlanReplyList),tenantIdList.get(0));
                } catch (Exception e){
                    logger.info(e.getMessage());
                }
            }
        }
        return CollectionUtil.newArrayList();
    }

    /**
     * 供应商回复送货计划后发送站内信息和邮件通知给采购员
     * @param planList
     */
    private void sendReplyPlanMsgToPur(List<DeliveryPlanEntity> planList) {
        if (CollectionUtil.isEmpty(planList)) {
            return;
        }
        try {
            planList.forEach(plans-> {
                // 按采购员分组
                Map<String, List<DeliveryPlanEntity>> plansByPurchaser = plans.stream()
                        .filter(plan -> !StrUtil.isEmptyIfStr(plan.getPurchaserName()))
                        .collect(Collectors.groupingBy(DeliveryPlanEntity::getPurchaserName));
                plansByPurchaser.forEach((purchaserName, purchaserPlans) -> {
                    try {
                        // 获取供应商信息
                        VendorVO vendorVo = baseClient.getVendorVoBySourceId(tenantId, purchaserPlans.get(0).getVendorId());
                        if (vendorVo == null) {
                            logger.warn("未找到供应商信息，vendorId: {}", purchaserPlans.get(0).getVendorId());
                            return;
                        }

                        // 构建通知内容
                        String vendorName = vendorVo.getVendorFullName();
                        int planCount = purchaserPlans.size();
                        String content = String.format("供应商%s回复了%d条送货计划的交期信息，请及时查看处理！", vendorName, planCount);

                        // 发送站内信
                        JSONObject mailJson = new JSONObject();
                        mailJson.put("tenantId", tenantId);
                        mailJson.put("userId", 0L);
                        mailJson.put("content", content);
                        mailJson.put("menuTitle", "送货计划管理");
                        mailJson.put("url", "dm/deliveryPlan/list");
                        sysClient.sendMail(mailJson);

                        // 发送邮件通知
                        List<UserSimpleVO> sysUserVOS = sysClient.queryUserByNames(tenantId, purchaserName);
                        if (CollectionUtil.isNotEmpty(sysUserVOS) && !StrUtil.isEmptyIfStr(sysUserVOS.get(0).getUserEmail())) {
                            EmailMessageVo emailMessageVo = new EmailMessageVo();
                            emailMessageVo.setTenantId(tenantId);
                            emailMessageVo.setTitle("送货计划");

                            StringBuilder emailContent = new StringBuilder();
                            emailContent.append(purchaserName).append("，您好：\n");
                            emailContent.append("<br>\n");
                            emailContent.append("<br>供应商：").append(vendorName).append("已回复了送货计划的交期信息，详情如下：\n");
                            emailContent.append("<br>\n");

                            // 添加计划详情
                            for (DeliveryPlanEntity plan : purchaserPlans) {
                                emailContent.append("<br>• 计划单号：").append(plan.getPlanNo())
                                        .append("，物料：").append(plan.getGoodsName())
                                        .append("，计划日期：").append(DateUtil.format(plan.getPlanDate(), "yyyy-MM-dd"))
                                        .append("，回复数量：").append(plan.getReplyQty() != null ? plan.getReplyQty() : null);
                                if (!StrUtil.isEmptyIfStr(plan.getRemark())) {
                                    emailContent.append("，备注：").append(plan.getRemark());
                                }
                                emailContent.append("\n");
                            }

                            emailContent.append("<br>\n");
                            emailContent.append("<br>请您及时查阅、处理！如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n");
                            emailContent.append("<br>\n");

                            emailMessageVo.setContent(emailContent.toString());
                            emailMessageVo.setEmail(sysUserVOS.get(0).getUserEmail());
                            baseClient.customSendEmail(emailMessageVo);
                        }
                    } catch (Exception e) {
                        logger.error("发送送货计划回复通知失败，采购员：{}，错误信息：{}", purchaserName, e.getMessage(), e);
                    }
                });
            });
        } catch (Exception e) {
            logger.error("发送送货计划回复通知失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 导入送货计划时关闭计划
     * @param
     * @return
     */
    @GlobalTransactional(rollbackFor = Throwable.class)
    private boolean closePlanByImport(DeliveryPlanEntity deliveryPlanEntity){
        boolean flag = deliveryPlanItemService.closeByDeliveryPlanId(deliveryPlanEntity.getId());
        Map<String,Object> params = new HashMap<>();
        params.put("tenantId",commonService.getTenantId());
        params.put("deliveryPlanId",deliveryPlanEntity.getId());
//        params.put("userId",commonService.getUserId());
        BigDecimal planItemMatchedNum = deliveryPlanItemService.countMatchedNumByPlanId(params);
        if (planItemMatchedNum.compareTo(BigDecimal.ZERO) == 0){
            deliveryPlanEntity.setDeleteFlag(DeliveryPlanDeleteFlagEnum.CLOSED.getCode());
        } else {
            deliveryPlanEntity.setPlanNum(planItemMatchedNum);
            deliveryPlanEntity.setShortageNum(planItemMatchedNum);
        }
        if (flag){
            this.updateById(deliveryPlanEntity);
            //推送站内信信息
            JSONObject mailJson = new JSONObject();
            mailJson.put("tenantId",deliveryPlanEntity.getVendorId());
            mailJson.put("userId",0L);
            mailJson.put("content",deliveryPlanEntity.getDeptName()+"将原计划单号:"+deliveryPlanEntity.getPlanNo()+"进行了更改/关闭操作");
            mailJson.put("menuTitle","送货看板");//菜单标题
            mailJson.put("url","dm/deliveryBoard/vendor");//菜单地址
            sysClient.sendMail(mailJson);
        }
        return true;
    }

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        DeliveryPlanEntity deliveryPlanEntity =this.getById(id);
        //if(deliveryPlanEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("已审核的销售订单禁止修改"));
        //}
    }
    /**
     * 审核状态校验
     *
     * @param
     */
    private void checkCheck(DeliveryPlanEntity deliveryPlanEntity) {
        //if(deliveryPlanEntity.getOrderState().equals(OrderHead_OrderStateEnum.AUDITED.getValue())){
        //    throw new RRException(String.format("[%s] 此销售单已审核"));
        //}
    }

    /**
     * 删除状态校验
     *
     * @param ids
     */
    private void deleteCheck(Long[] ids) {
        for (Long id:ids) {
                DeliveryPlanEntity deliveryPlanEntity = this.getById(id);
        }
        //if(!saleOrderHeadEntity.getOrderState().equals(OrderHead_OrderStateEnum.WAITCHECK.getValue())){
        //    throw new RRException(String.format("只允许删除未审核的销售订单"));
        //}
    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(DeliveryPlanEntity record, Class<?> cls) {
        ValidatorUtils.validateEntity(record, cls);
    }
    /**
     * 获取查询条件
     *
     * @param
     */
    private QueryWrapper<DeliveryPlanEntity> getQueryWrapper(Map<String, Object> params) {
        QueryWrapper<DeliveryPlanEntity> queryWrapper=new QueryWrapper<>();
        if (!StrUtil.isEmptyIfStr(params.get("tenantId"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getTenantId, Long.parseLong(params.get("tenantId").toString()));
        }
        if (!StrUtil.isEmptyIfStr(params.get("vendorId"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getVendorId, Long.parseLong(params.get("vendorId").toString()));
        }
        if(!StrUtil.isEmptyIfStr(params.get("goods"))){
            queryWrapper.and(wrapper -> wrapper
                    .like("goods_code",params.get("goods").toString().trim()).or()
                    .like("goods_erp_code",params.get("goods").toString().trim()).or()
                    .like("goods_name",params.get("goods").toString().trim()).or()
                    .like("goods_model",params.get("goods").toString().trim())
            );
        }
        if (!StrUtil.isEmptyIfStr(params.get("vendor"))){
            queryWrapper.lambda().and(wrapper ->
                    wrapper.like(DeliveryPlanEntity::getVendorName, params.get("vendor").toString().trim())
                    .or()
                    .like(DeliveryPlanEntity::getVendorCode, params.get("vendor").toString().trim())
            );
        }
        if (!StrUtil.isEmptyIfStr(params.get("bubGroupStatus"))){
            if (params.get("bubGroupStatus").equals(1)){
                queryWrapper.lambda().and(
                        wrapper -> wrapper.isNull(DeliveryPlanEntity::getReplyQty)
                );
            }
            if (params.get("bubGroupStatus").equals(2)){
                queryWrapper.lambda().and(
                        wrapper -> wrapper.isNotNull(DeliveryPlanEntity::getReplyQty)
                                .gt(DeliveryPlanEntity::getReplyQty, 0)
                );
            }
        }
        if (!StrUtil.isEmptyIfStr(params.get("planNo"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getPlanNo, params.get("planNo").toString());
        }
        if (!StrUtil.isEmptyIfStr(params.get("negaIsAbsMatch"))){
            queryWrapper.ne("is_match", Integer.parseInt(params.get("negaIsAbsMatch").toString()));
        }
        try {
            if (params.get("queryDate") != null && !params.get("queryDate").equals("")) {
                String[] split = params.get("queryDate").toString().split(" 至 ");
                queryWrapper.lambda().between(DeliveryPlanEntity::getPlanDate, split[0] + " 00:00:00", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("搜索日期有误");
            throw new RRException("日期输入有误");
        }
        //物料id
        if (!StrUtil.isEmptyIfStr(params.get("goodsId"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getGoodsId,params.get("goodsId").toString().trim());
        }
        //供应商id
        if(!StrUtil.isEmptyIfStr(params.get("vendorId"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getVendorId,params.get("vendorId").toString().trim());
        }
        //机构id
        if(!StrUtil.isEmptyIfStr(params.get("deptId"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getDeptId,params.get("deptId").toString().trim());
        }
        //计划要求送货日期
        if(!StrUtil.isEmptyIfStr(params.get("planDate"))){
            queryWrapper.eq("plan_date", params.get("planDate").toString());
        }
        //查询不为关闭
        if(!StrUtil.isEmptyIfStr(params.get("isClosed"))){
            queryWrapper.lambda().ne(DeliveryPlanEntity::getDeleteFlag, WhetherEnum.YES.getCode());
        }
        //采购员
        if(!StrUtil.isEmptyIfStr(params.get("purchaserId"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getPurchaserId, Long.parseLong(params.get("purchaserId").toString()));
        }
        //采购组
        if(!StrUtil.isEmptyIfStr(params.get("purchasingGroup"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getPurchasingGroup, params.get("purchasingGroup").toString());
        }
        //采购组
        if(!StrUtil.isEmptyIfStr(params.get("mrpRegion"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getMrpRegion, params.get("mrpRegion").toString());
        }
        if (!StrUtil.isEmptyIfStr(params.get("orderType"))){
            queryWrapper.lambda().eq(DeliveryPlanEntity::getOrderType, Integer.parseInt(params.get("orderType").toString()));
        }
        queryWrapper.lambda().orderByAsc(DeliveryPlanEntity::getPlanDate);
        return queryWrapper;
    }

    /**
     * 获取查询条件
     *
     * @param
     */
    private QueryWrapper<DeliveryPlanItemEntity> getQueryWrapperDeliveryItem(Map<String, Object> params) {
        QueryWrapper<DeliveryPlanItemEntity> queryWrapper=new QueryWrapper<>();
        if(!StrUtil.isEmptyIfStr(params.get("propKey"))){

        }
        if (!StrUtil.isEmptyIfStr(params.get("deliveryPlanId"))){
            queryWrapper.eq("delivery_plan_id", Long.parseLong(params.get("deliveryPlanId").toString()));
        }
        if (!StrUtil.isEmptyIfStr(params.get("deleteFlag"))){
            queryWrapper.eq("delete_flag", Integer.parseInt(params.get("deleteFlag").toString()));
        }

        queryWrapper.orderByAsc("id");
        return queryWrapper;
    }

    private List<Map<String, Object>> toListMap(List<Object> list) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Object object : list) {
            Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(object), new TypeReference<Map<String, Object>>(){});
            result.add(map);
        }
        return result;
    }

    /**
     * 补充计划查询参数
     * @param params
     * @param deliveryPlanDays
     * @return
     */
    private Map<String,Object> suppPlanQueryParams(Map<String,Object> params,Integer deliveryPlanDays){
        Long tenantId = commonService.getTenantId();
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(commonService.getTenantInfo().getEntType())){
            VendorVO vendorVo = baseClient.getVendorVoBySourceId(commonService.getTenantId());
            tenantId = vendorVo.getTenantId();
            params.put("vendorId",vendorVo.getSoureId());
        }
        params.put("tenantId",tenantId);
        if (params.get("planDates") != null && !params.get("planDates").equals("")){
            String[] split = params.get("planDates").toString().split(" 至 ");
            params.put("start", split[0]);
            params.put("end", split[1]);
        } else {
            params.put("start", DateUtil.format(new Date(),"yyyy-MM-dd") + " 00:00:00");
            // 当前日期+deliveryPlanDays后的日期
            params.put("end", DateUtil.format(DateUtil.offsetDay(new Date(), deliveryPlanDays), "yyyy-MM-dd") + " 23:59:59");
        }
        return params;
    }

    /**
     * 获取日期列表
     * @param endDate
     * @param deliveryPlanDays
     * @return
     */
    private List<String> getDateList(String endDate,Integer deliveryPlanDays){
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        try {
            Date sd = sdf.parse(endDate);
            c.setTime(sd);
            dateList.add(sdf.format(c.getTime()));
            for (int i = 1; i <= deliveryPlanDays; i++) {
                c.add(Calendar.DATE, -1);
                dateList.add(sdf.format(c.getTime()));
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RRException("日期输入有误");
        }
        return dateList;
    }

    /**
     * 获取单条送货计划主表数据导入报错信息VO类
     * @return
     */
    private AfterImportErrorPlanVO getAfterImportErrorPlanVO(DeliveryPlanEntity deliveryPlan,String errorMsg,Integer coverType,BigDecimal originalNum,BigDecimal nowNum){
        AfterImportErrorPlanVO afterImportExportPlanVO = new AfterImportErrorPlanVO();
        afterImportExportPlanVO.setDeptCode(deliveryPlan.getDeptCode());
        afterImportExportPlanVO.setMrpRegion(deliveryPlan.getMrpRegion());
        afterImportExportPlanVO.setPurchasingGroup(deliveryPlan.getPurchasingGroup());
        afterImportExportPlanVO.setVendorCode(deliveryPlan.getVendorCode());
        afterImportExportPlanVO.setVendorName(deliveryPlan.getVendorName());
        afterImportExportPlanVO.setGoodsErpCode(deliveryPlan.getGoodsErpCode());
        afterImportExportPlanVO.setGoodsName(deliveryPlan.getGoodsName());
        afterImportExportPlanVO.setGoodsModel(deliveryPlan.getGoodsModel());
        afterImportExportPlanVO.setPlanDate(deliveryPlan.getPlanDate());
        //覆盖类型：1-大于 2.等于 3.小于 4.新增
        afterImportExportPlanVO.setCoverType(coverType);
        //原计划总数
        afterImportExportPlanVO.setOriginalPlanNum(originalNum);
        //现导入的计划总数
        afterImportExportPlanVO.setPlanNum(nowNum);
        //错误信息
        afterImportExportPlanVO.setErrorMsg(errorMsg);
        return afterImportExportPlanVO;
    }

    /**
     * 根据物料编码查询物料信息
     * @param goodsErpCodeList
     * @return
     */
    private Map<String, GoodsVO> queryGoodsList(List<String> goodsErpCodeList){
        List<GoodsVO> goodsVoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(goodsErpCodeList)){
            return new HashMap<>();
        }
        // 每次批量查询的物料数量
        int batchSize = 10;
        for (int i = 0; i < goodsErpCodeList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, goodsErpCodeList.size());
            List<String> subList = goodsErpCodeList.subList(i, end);
            GoodsReqVo goodsReqVo = new GoodsReqVo();
            goodsReqVo.setGoodsErpCodeList(subList);
            List<GoodsVO> goodsList = baseClient.queryGoodsList(goodsReqVo);
            // 合并添加物料信息
            if (CollectionUtil.isNotEmpty(goodsList)){
                goodsVoList.addAll(goodsList);
            }
        }
        Map<String, GoodsVO> goodsMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(goodsVoList)){
            goodsMap = goodsVoList.stream()
                    .filter(vo -> StrUtil.isNotEmpty(vo.getGoodsErpCode()))
                    .collect(Collectors.toMap(
                            GoodsVO::getGoodsErpCode,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        }

        return goodsMap;
    }

    private UploadPlanResponse uploadPlanToSap(List<PlanItemResultVO> planItemResultVoAllList){
        List<UploadPlanResponse> uploadPlanResponseList = new ArrayList<>();
        String sapLinkUrl = sysClient.getValueByKeyAndTenantId("SAPLinkUrl", commonService.getTenantId());
        if (StrUtil.isEmptyIfStr(sapLinkUrl)){
            throw new RRException("没有维护SAP接口访问IP,请求接口失败,请联系IT进行维护数据");
        }
        sapLinkUrl = sapLinkUrl + "/zmm006_http?sap-client=300";
        if (CollectionUtil.isNotEmpty(planItemResultVoAllList)){
            for (PlanItemResultVO planItemResultVO:planItemResultVoAllList) {
                List<UploadPlanRequest> uploadPlanRequestList = new ArrayList<>();
                UploadPlanRequest uploadPlanRequest = new UploadPlanRequest();
                uploadPlanRequest.setEBELN(planItemResultVO.getOrderNo());
                uploadPlanRequest.setEBELP(planItemResultVO.getOrderRowNo());
                uploadPlanRequest.setMATNR(planItemResultVO.getGoodsErpCode());
                uploadPlanRequest.setZDATE(DateUtil.format(planItemResultVO.getPlanDate(),"yyyyMMdd"));
                uploadPlanRequest.setZMENGE(planItemResultVO.getMatchNum().toString());
                if (DeliveryPlanDeleteFlagEnum.CLOSED.getValue().equals(planItemResultVO.getDeleteFlag())){
                    uploadPlanRequest.setZCELFA("X");
                }
                uploadPlanRequestList.add(uploadPlanRequest);
                String jsonData = JSON.toJSONString(uploadPlanRequestList);
                logger.info("接口请求数据===================》"+jsonData);
                String result = sapApiService.sendSap(sapLinkUrl, jsonData, "SRM调用SAP配额上传接口", commonService.getTenantId());
                logger.info("接口返回数据===================》"+result);
                JSONArray objects = JSONObject.parseArray(result);
                uploadPlanResponseList = BeanConverter.convertList(objects, UploadPlanResponse.class);
                if (CollectionUtil.isNotEmpty(uploadPlanResponseList)){
                    if ("E".equals(uploadPlanResponseList.get(0).getMSGTY())){
                        throw new RRException(String.format("调用SAP回货计划数量上传接口,失败原因:%s",uploadPlanResponseList.get(0).getMSGTX()));
                    }
                    return uploadPlanResponseList.get(0);
                }
            }
        }
        return null;
    }
}
