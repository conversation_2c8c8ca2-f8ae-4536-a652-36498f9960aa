/**
 * 站内信跳转混入
 * 用于处理站内信点击后的页面跳转和详情打开
 */
export default {
  watch: {
    $route: {
      handler(to) {
        // 监听路由变化，处理站内信跳转
        if (to.query && to.query.id) {
          // 如果URL中有id参数，直接打开详情
          this.handleMessageNavigation(to.query.id);
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 处理站内信跳转
     * @param {String|Number} id - 记录ID
     */
    handleMessageNavigation(id) {
      if (id) {
        // 清除URL参数，避免重复触发
        this.$router.replace({ 
          path: this.$route.path,
          query: {}
        });
        
        // 调用页面的详情打开方法
        // 不同页面可能有不同的方法名，需要在具体页面中重写此方法
        if (this.openDetailFromMessage) {
          this.openDetailFromMessage(id);
        } else if (this.addOrUpdateHandle) {
          // 默认使用 addOrUpdateHandle 方法
          this.addOrUpdateHandle(id);
        } else {
          console.warn('页面未定义 openDetailFromMessage 或 addOrUpdateHandle 方法');
        }
      }
    }
  }
}
