<template>
  <div style="background-color: white;height: 100%">
  <el-row>
    <el-col :span="24">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>数据统计</span>
        </div>
        <div>
          <PanelGroup v-if="userInfo.entType == 1"></PanelGroup>
          <VendorPanelGroup v-else></VendorPanelGroup>
        </div>
      </el-card>
    </el-col>
    <div v-if="userInfo.entType != 1">
      <el-col :span="16">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>业务单据消息列表</span>
          </div>
          <div>
            <d-table ref="listTable" :data="massageList" hasC height="calc(58vh - 100px)">
              <el-table-column prop="content" label="消息内容" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-link type="primary" @click="openForm(scope.row)">{{scope.row.content}}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="createDate" label="发出时间" align="center" show-overflow-tooltip width="150"/>
            </d-table>
            <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initMassageList"/>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card" style="height: 10%">
          <div slot="header" class="clearfix">
            <span>公告通知列表</span>
          </div>
          <div>
            <d-table ref="listTable" :data="noticeMsgList" height="calc(63vh - 100px)">
              <el-table-column prop="content" label="消息内容" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-link type="primary" @click="openForm(scope.row)">{{scope.row.content}}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="createDate" label="发出时间" align="center" show-overflow-tooltip width="150"/>
            </d-table>
          </div>
        </el-card>
      </el-col>
    </div>
    <div v-else>
      <el-col :span="16">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>业务单据消息列表</span>
          </div>
          <div>
            <d-table ref="listTable" :data="massageList" hasC height="calc(58vh - 100px)">
              <el-table-column prop="content" label="消息内容" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-link type="primary" @click="openForm(scope.row)">{{scope.row.content}}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="createDate" label="发出时间" align="center" show-overflow-tooltip width="150"/>
            </d-table>
            <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initMassageList"/>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card" style="height: 10%">
          <div slot="header" class="clearfix">
            <span>快速通道</span>
          </div>
          <div style="height: calc(63vh - 102px)">

          </div>
        </el-card>
      </el-col>
    </div>
  </el-row>
  </div>
</template>

<script>
import echarts from 'echarts'
import PanelGroup from './components/tenantPanelGroup'
import VendorPanelGroup from './components/vendorPanelGroup'
import LineChart from './components/LineChart'
import CountTo from 'vue-count-to'
import {queryLastMailList} from '@/api/base/mail';
import {queryMsgByNotice} from '@/api/base/notice';
import store from "@/store";

export default {
  name: 'DashboardAdmin',
  components: {
    PanelGroup,
    LineChart,
    CountTo,
    VendorPanelGroup
  },
  data() {
    return {
      value1: '',
      charts: '',
      total:0,
      queryParam:{
        page:1,
        limit:20,
      },
      userInfo:store.getters.userInfo,/*获取当前登录的用户信息*/
      opinionData: ["3", "2", "4", "4", "5"],
      massageList: [],//消息列表数据
      noticeMsgList: [],//消息列表数据
    }
  },
  created() {
    this.userInfo = store.getters.userInfo;
    this.initMassageList();
    if (this.userInfo.entType != 1){
      this.initNoticeMsgList();
    }
  },
  methods: {
    //初始化查询消息列表数据
    initMassageList(){
      queryLastMailList(this.queryParam).then(res => {
        this.massageList = res.data.list;
        this.total = res.data.totalCount;
      }).catch(res => {

      })
    },
    initNoticeMsgList(){
      queryMsgByNotice({}).then(res => {
        this.noticeMsgList = res.data
      }).catch(res => {

      })
    },
    // 点击打开表单
    openForm(data) {
      try {
        const routerUrl = data && data.url;
        if (!routerUrl) {
          console.warn('openForm: URL is empty or undefined');
          return;
        }

        // 统一处理路径格式
        const normalizedPath = this.normalizePath(routerUrl);

        // 使用 Vue Router 的解析功能
        const resolved = this.$router.resolve(normalizedPath);

        // 跳转到目标页面
        this.$router.push(resolved.route);
      } catch (error) {
        console.error('openForm: Failed to navigate to', data && data.url, error);
        this.$message.error('页面跳转失败，请检查链接是否正确');
      }
    },

    // 标准化路径格式
    normalizePath(url) {
      if (!url) return '/';

      // 如果已经是完整的路径格式，直接返回
      if (url.startsWith('/')) {
        return url;
      }

      // 添加前导斜杠
      return `/${url}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.sale {
  background: #fff;
  margin-bottom: 10px;
  border-radius: 4px;
  .sale_title {
    padding-top: 16px;
    padding-left: 22px;
    span {
      &:first-child {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
      &:nth-child(2) {
        color: #999;
        font-size: 12px;
      }
    }
  }
  .sale_items {
    display: flex;
    justify-content: space-around;
    padding: 34px 0;
    li {
      display: flex;
      flex-direction: column;
      text-align: center;
      .nums {
        font-size: 26px !important;
        padding-top: 13px;
        padding-bottom: 9px;
        color: #1890ff;
        font-weight: 600;
        span {
          font-size: 26px !important;
          padding-top: 13px;
          padding-bottom: 9px;
          color: #1890ff !important;
          font-weight: 600;
        }
      }
      .lastnums {
        font-size: 26px !important;
        padding-top: 13px;
        padding-bottom: 9px;
        font-weight: 600;
        color: #333;
      }
      &:first-child {
        span:last-child {
          color: #08b41f;
        }
      }
      &:nth-child(4) {
        span {
          &:last-child {
            color: #08b41f;
          }
        }
      }
      &:nth-child(2) {
        span {
          &:last-child {
            color: #b40808;
          }
        }
      }
      &:nth-child(3) {
        span {
          &:last-child {
            color: #999;
          }
        }
      }
      &:last-child {
        span {
          &:last-child {
            color: #000;
          }
        }
      }
      span {
        &:nth-child(2) {
          font-size: 26px !important;
          padding-top: 13px;
          padding-bottom: 9px;
          color: #1890ff;
          font-weight: 600;
        }
        &:nth-child(3) {
          font-size: 12px;
        }
      }
    }
  }
}
.notice {
  padding-bottom: 20px;
  .notice_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 25px;
    .title_left {
      display: flex;
      align-items: center;
      span {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }
    .title_right {
      font-size: 12px;
      color: #999;
      line-height: 30px;
    }
  }
  .notice_right {
    background: #fff;
    height: 400px;
    border-radius: 4px;
    overflow: hidden;
    .no_right_massage {
      li {
        display: flex;
        background: #fff;
        border: 1;
        height: 170px;
        border-top: 1px solid #f2f2f5;
        .right_item {
          width: 50%;
          padding: 16px 20px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .right-top {
            font-size: 14px;
            display: flex;
            align-items: center;
            img {
              margin-right: 10px;
            }
          }
          p {
            font-size: 14px;
            line-height: 22px;
            padding: 20px 0;
            color: #999;
            display: -webkit-box;
            line-clamp: 3;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
          }
          .bt {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
          }
        }
        .linet {
          border-left: 1px solid #f2f2f5;
        }
      }
    }
  }
  .notice_left {
    background: #fff;
    height: 400px;
    border-radius: 4px;
    overflow: hidden;
    .no_left_massage {
      height: 340px;
      padding: 10px 20px;
      border-top: 1px solid #f2f2f5;
      ul {
        li {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          color: #303133;
          padding: 10px 0;
          cursor: pointer;
          p {
            width: calc(100% - 80px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .time {
            width: 80px;
            display: inline-block;
            text-align: right;
            color: #999999;
          }
        }
      }
    }
  }
}

.componey {
  font-size: 14px;
  text-align: center;
  color: #999;
  padding-bottom: 10px;
}

.dashboard-editor-container {
  background-color: #ebeef5;
  position: relative;
  width: 100%;
  overflow: hidden;

  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 20px;
  }
}
.dateSelect {
  position: absolute;
  right: 67px;
  top: 38px;
  z-index: 100;
  .el-date-editor {
    width: 160px;
  }
}
@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
