{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\vendor\\index.vue", "mtime": 1754373498546}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.regexp.split\");\nrequire(\"core-js/modules/es6.regexp.search\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _deliveryPlan = require(\"@/api/dm/deliveryPlan\");\nvar _elementUi = require(\"element-ui\");\nvar _common = require(\"@/api/common\");\nvar _message = require(\"@/utils/message\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  name: \"dm-deliveryPlan-vendor\",\n  components: {},\n  data: function data() {\n    return {\n      PlanDeleteFlagOptions: _store.default.getters.commonEnums['dm.DeliveryPlanDeleteFlagEnum'],\n      // 订单类型\n      queryParam: {\n        page: 1,\n        limit: 20,\n        planNo: \"\",\n        orderNo: \"\",\n        goods: \"\",\n        vendorIds: 'vendorIds',\n        //用于区分是否为供应商\n        sortObj: 'dd.id DESC',\n        //排序\n        whereType: 1,\n        //气泡查询条件 默认为1 - 全部\n        vendor: '',\n        // 供应商编码|名称\n        keyword: '',\n        // 物料编码|名称|描述|图号\n        queryDate: '',\n        //要求送货日期\n        startDate: '',\n        // 开始日期\n        orderType: '',\n        // 订单类型\n        bubGroupStatus: 1,\n        deleteFlag: 0\n      },\n      deliveryCount: {},\n      //气泡数\n      isUpdate: 0,\n      listLoading: false,\n      btnLoading: false,\n      formVisible: false,\n      list: [],\n      //列表数据\n      total: 0,\n      //条数\n      selectedDatas: [],\n      /*选择的数据*/\n      selectedNum: 0,\n      /*选择数据的条数*/\n      userInfo: _store.default.getters.userInfo,\n      /*获取当前用户*/\n      buttonFrom: {\n        count1: 'primary',\n        count2: '',\n        count3: '',\n        count4: '',\n        count5: '',\n        count6: ''\n      },\n      queryDate: []\n    };\n  },\n  created: function created() {\n    this.initData();\n  },\n  methods: {\n    initData: function initData() {\n      var _this = this;\n      this.listLoading = true;\n      if (this.queryDate.length !== 0) {\n        var startDate = this.$dian.dateFormat(this.queryDate[0], 'YYYY-MM-DD');\n        var endDate = this.$dian.dateFormat(this.queryDate[1], 'YYYY-MM-DD');\n        this.queryParam.queryDate = startDate + \" 至 \" + endDate;\n      }\n      (0, _deliveryPlan.getDeliveryPlanList)(this.queryParam).then(function (res) {\n        _this.total = res.data.totalCount;\n        _this.list = res.data.list;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    //点击气泡查询\n    changeCountsButton: function changeCountsButton(stat, name) {\n      // 动态变换\n      this.buttonFrom.count1 = ''; //全部\n      this.buttonFrom.count2 = ''; //已送未收\n      this.buttonFrom.count3 = ''; //待发出\n      this.buttonFrom.count4 = ''; //暂收\n      this.buttonFrom.count5 = ''; //暂退\n      this.buttonFrom.count7 = ''; //可创建让步单据\n      this.buttonFrom[name] = 'primary';\n      this.queryParam.bubGroupStatus = stat;\n      this.search();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedDatas = selection.map(function (item) {\n        return item;\n      });\n      //获取所有选中项数组的长度\n      this.selectedNum = selection.length;\n    },\n    downTrendsTemplate: function downTrendsTemplate() {\n      var params = {};\n      var url = '/api/dm/deliveryPlan/exportDynaPlanReplyData';\n      this.loadFile(url, '送货计划交期回复数据', params);\n    },\n    importPlan: function importPlan() {\n      var url = \"/api/dm/deliveryPlan/importDynaPlanReplyData\";\n      this.$refs.upload.init(url);\n    },\n    loadFile: function loadFile(url, fileName, params) {\n      var _this2 = this;\n      (0, _elementUi.Message)({\n        duration: 0,\n        // 设置为0就可以使永久停留\n        type: 'warning',\n        message: fileName + '下载中....'\n      });\n      (0, _common.fileExport)(url, params).then(function (res) {\n        var data = res.data;\n        var fileReader = new FileReader();\n        fileReader.onload = function (result) {\n          try {\n            var jsonData = JSON.parse(this.result); // 说明是普通对象数据，后台转换失败\n            if (jsonData.code === 0) {\n              // 接口返回的错误信息\n              (0, _message.message)({\n                message: '导出失败',\n                type: 'error',\n                duration: 1000\n              });\n            }\n            this.btnLoading = false;\n          } catch (err) {\n            // 解析成对象失败，说明是正常的文件流\n            var blob = new Blob([res.data], {\n              type: 'application/vnd.ms-excel'\n            }); // 如类型为excel,type为：'application/vnd.ms-excel'\n            var _fileName = '送货计划交期回复数据';\n            if (res.headers[\"content-disposition\"] && res.headers[\"content-disposition\"].split(\"=\").length > 1) {\n              _fileName = '送货计划交期回复数据';\n            }\n            var _url = window.URL.createObjectURL(blob);\n            var link = document.createElement('a');\n            link.style.display = 'none';\n            link.href = _url;\n            link.setAttribute('download', _fileName);\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link); // 点击后移除，防止生成很多个隐藏a标签\n            _elementUi.Message.closeAll();\n            (0, _message.message)({\n              type: 'success',\n              message: '导出成功'\n            });\n          }\n        };\n        _this2.loading = false;\n        fileReader.readAsText(data); // 注意别落掉此代码，可以将 Blob 或者 File 对象转根据特殊的编码格式转化为内容(字符串形式)\n      }).catch(function (err) {\n        console.error(err);\n        (0, _message.message)({\n          message: \"导出失败\",\n          type: 'error',\n          duration: 3000\n        });\n        _this2.loading = false;\n      });\n    },\n    // 批量确认\n    batchConfirm: function batchConfirm() {\n      var _this3 = this;\n      if (this.selectedDatas.length === 0) {\n        this.$message.warning('请选择要操作的数据！');\n        return;\n      }\n      this.$confirm('是否要批量确认选中的' + this.selectedNum + '条数据吗？', '提示', {\n        confirmButtonText: '确认',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        _this3.btnLoading = true;\n        _this3.listLoading = true;\n        (0, _deliveryPlan.batchConfirmPlanByVendor)(_this3.selectedDatas).then(function (res) {\n          _this3.isUpdate = 0;\n          _this3.btnLoading = false;\n          _this3.$message.success('确认成功！');\n          _this3.initData();\n        }).catch(function (err) {\n          _this3.btnLoading = false;\n          _this3.listLoading = false;\n        });\n      });\n    },\n    // 取消修改\n    updateCancel: function updateCancel() {\n      var _this4 = this;\n      this.isUpdate = 0;\n      this.$nextTick(function () {\n        _this4.initData();\n      });\n    },\n    // 搜索方法，并返回到第一页\n    search: function search() {\n      this.initData();\n    },\n    // 重置方法\n    reset: function reset() {\n      this.queryParam = this.$options.data().queryParam;\n      this.queryDate = [];\n      this.search();\n    },\n    //打开送货单详情弹窗\n    openInfoForm: function openInfoForm(id) {\n      var _this5 = this;\n      this.formVisible = true;\n      this.$nextTick(function () {\n        _this5.$refs.form.init(id);\n      });\n    },\n    // 快速跳转至计划交期确认报表\n    openReplyReport: function openReplyReport() {\n      this.$router.push({\n        path: '/dm/report/planReplyReport'\n      });\n    },\n    //关闭刷新列表数据\n    callDeliveryBoardList: function callDeliveryBoardList() {\n      this.formVisible = false;\n      this.search();\n    },\n    //导出\n    exportHandle: function exportHandle() {\n      this.$refs.export.init('/api/dm/deliveryPlanItem/export', '送货计划', this.queryParam);\n    }\n  }\n};\nexports.default = _default;", null]}