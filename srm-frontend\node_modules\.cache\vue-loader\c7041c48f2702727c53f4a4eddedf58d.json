{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\index.vue", "mtime": 1754405497870}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {getSampleList,delSample,receiveMaterial} from '@/api/base/sample'\r\nimport { getSampleDemandList } from '@/api/base/sampleDemand';\r\nimport Form from './form'\r\nimport QualityForm from './qualityForm.vue'\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  components: {Form,QualityForm},\r\n  name: 'base-sample-tenant',\r\n  data() {\r\n    return {\r\n      list: [],\r\n      total: 0,\r\n      showAll: false,\r\n      queryParam: {//查询条件\r\n        sampleNo: '',//需求申请单号\r\n        ifTenant:'ifTenant',//用于区分是否为供应商登录，为ifTenant则是采购方\r\n        sampleStat:'',//主表单据状态\r\n        itemStat:'',//明细表检验状态\r\n        vendor:'',//供应商名称/编码\r\n        page: 1,\r\n        limit: 20,\r\n        goods:'',//物料信息\r\n        dept:'',//采购组织机构信息\r\n        sampleDate:'',\r\n        startDate:'',\r\n        endDate:'',\r\n        demandDate: '', // 需求日期\r\n        demandDateStart: '', // 需求开始日期\r\n        demandDateEnd: '', // 需求结束日期\r\n        replyDeliveryDate: '', // 回复日期\r\n        replyDateStart: '', // 回复开始日期\r\n        replyDateEnd: '', // 回复结束日期\r\n      },\r\n      detailVisible: false,\r\n      qualityVisible: false,\r\n      listLoading: true,\r\n      btnLoading: false,\r\n      selectedDatas: [],\r\n      sampleDates: [],\r\n      demandDates: [],\r\n      replyDates: [],\r\n      // sampleItemStatOptions: store.getters.commonEnums['base.SampleItemEnums'], // 明细行检验状态\r\n      demandTypeOption: store.getters.commonEnums['base.DemandClassTypeEnum'], // 单据类型\r\n      sampleStatOptions:[\r\n        {key:3,value:'待送样'},\r\n        {key:4,value:'待收样'},\r\n        {key:5,value:'已收样'},\r\n        {key:11,value:'已拒绝'}\r\n      ],\r\n    }\r\n  },\r\n  created() {\r\n    this.initData()\r\n  },\r\n  filters:{\r\n    date(time){\r\n      if (!time){\r\n        return ''\r\n      }\r\n      let date = new Date(time)\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth()+1;\r\n      let day = date.getDate();\r\n      return year+\"-\"+month+\"-\"+day;\r\n    }\r\n  },\r\n  methods: {\r\n    // 页面初始化加载列表数据\r\n    initData() {\r\n      this.listLoading = true\r\n      let query = {\r\n        ...this.queryParam,\r\n      }\r\n      getSampleList(query).then(res => {\r\n        debugger\r\n        this.total = res.data.totalCount\r\n        this.list = res.data.list\r\n        this.listLoading = false\r\n      }).catch(() => {\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    //打开新增/修改弹窗页面\r\n    addOrUpdateHandle(id) {\r\n      this.detailVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.detail.init(id);\r\n      })\r\n    },\r\n    //审核\r\n    handleCheck(id) {\r\n\r\n    },\r\n    search() {\r\n      this.queryParam.page=1;\r\n      // 处理送样日期\r\n      if (this.sampleDates.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.sampleDates[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.sampleDates[1], 'YYYY-MM-DD');\r\n        this.queryParam.sampleDate = startDate +\" 至 \"+endDate;\r\n        this.queryParam.startDate = startDate;\r\n        this.queryParam.endDate = endDate;\r\n      } else {\r\n        this.queryParam.sampleDate = '';\r\n        this.queryParam.startDate = '';\r\n        this.queryParam.endDate = '';\r\n      }\r\n\r\n      // 处理需求日期\r\n      if (this.demandDates.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.demandDates[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.demandDates[1], 'YYYY-MM-DD');\r\n        this.queryParam.demandDate = startDate +\" 至 \"+endDate;\r\n        this.queryParam.demandDateStart = startDate;\r\n        this.queryParam.demandDateEnd = endDate;\r\n      } else {\r\n        this.queryParam.demandDate = '';\r\n        this.queryParam.demandDateStart = '';\r\n        this.queryParam.demandDateEnd = '';\r\n      }\r\n\r\n      // 处理回复日期\r\n      if (this.replyDates.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.replyDates[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.replyDates[1], 'YYYY-MM-DD');\r\n        this.queryParam.replyDeliveryDate = startDate +\" 至 \"+endDate;\r\n        this.queryParam.replyDateStart = startDate;\r\n        this.queryParam.replyDateEnd = endDate;\r\n      } else {\r\n        this.queryParam.replyDeliveryDate = '';\r\n        this.queryParam.replyDateStart = '';\r\n        this.queryParam.replyDateEnd = '';\r\n      }\r\n\r\n      this.initData()\r\n    },\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.sampleDates = [];\r\n      this.demandDates = [];\r\n      this.replyDates = [];\r\n      this.initData()\r\n    },\r\n    //删除\r\n    handleDel(id) {\r\n      this.$confirm(this.$t('common.delTip'), this.$t('common.tipTitle'), {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delSample(id).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.search()\r\n            }\r\n          })\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    callRefreshList() {\r\n      this.detailVisible = false;\r\n      this.qualityVisible = false;\r\n      this.search();\r\n    },\r\n    handleSelectionChange(selection){\r\n      this.selectedDatas = selection.map(item => item)\r\n    },\r\n    quality(row){\r\n      this.qualityVisible = true;\r\n      let isAdd = false;\r\n      if(row.ifInspection > 0){\r\n        isAdd = true\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.quality.init(row.id,isAdd);\r\n      })\r\n    },\r\n    goToSampleDemand(sourceNo) {\r\n      if (!sourceNo) {\r\n        this.$message.warning('PLM打样单号不能为空');\r\n        return;\r\n      }\r\n      // 根据sourceNo查询送样需求单\r\n      const params = {\r\n        demandNo: sourceNo,\r\n        demandClassType: 3, // 包括内部打样和采购打样的所有单据\r\n        limit: 1\r\n      };\r\n      getSampleDemandList(params).then(res => {\r\n        if (res.data && res.data.list && res.data.list.length > 0) {\r\n          const demandId = res.data.list[0].id;\r\n          const timestamp = Date.now();\r\n          this.$router.push({\r\n            path: '/base/sampleDemand/tenant',\r\n            query: {\r\n              detailVisible: true,\r\n              id: demandId,\r\n              t: timestamp\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.warning(`未找到PLM打样单号为\"${sourceNo}\"的送样需求单`);\r\n        }\r\n      }).catch(error => {\r\n        console.error('查询送样需求单失败:', error);\r\n        this.$message.error('查询送样需求单失败，请稍后重试');\r\n      });\r\n    },\r\n    //批量确认收样\r\n    batchConfirmRecSample(){\r\n      // 检查是否有选中的数据\r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message.error('请至少选择一条需要确认收样的记录');\r\n        return;\r\n      }\r\n      // 检查是否已收样\r\n      if(this.selectedDatas.some(item => item.caseStat ==  5)){\r\n        this.$message.error('存在已收样的物料明细记录，请勿重复收样');\r\n        return;\r\n      }\r\n      // 检查是否为待收样状态\r\n      if(this.selectedDatas.some(item => item.caseStat != 4)){\r\n        this.$message.error('存在非待收样状态的物料明细记录，无法确认收样');\r\n        return;\r\n      }\r\n      this.btnLoading = true;\r\n      const ids = this.selectedDatas.map(item => item.itemId); // 物料明细ID\r\n      this.$confirm(`确认收样选中的${this.selectedDatas.length}条记录？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        receiveMaterial(ids).then(res => {\r\n          this.$message({\r\n            message: \"批量确认收样成功\",\r\n            type: 'success',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.btnLoading = false;\r\n              this.search(); // 刷新列表\r\n            }\r\n          });\r\n        }).catch(err => {\r\n          this.btnLoading = false;\r\n          this.$message.error('批量确认收样失败: ' + (err.message || '未知错误'));\r\n        });\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消批量确认收样'\r\n        });\r\n      });\r\n    },\r\n  }\r\n}\r\n", null]}