{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\vendor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dm\\deliveryPlan\\vendor\\index.vue", "mtime": 1754373498546}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport store from \"@/store\";\r\nimport {\r\n  getDeliveryPlanList,\r\n  batchConfirmPlanByVendor\r\n} from \"@/api/dm/deliveryPlan\";\r\nimport { Message } from \"element-ui\";\r\nimport { fileExport } from \"@/api/common\";\r\nimport { message } from \"@/utils/message\";\r\n\r\nexport default {\r\n  name: \"dm-deliveryPlan-vendor\",\r\n  components: {\r\n\r\n  },\r\n  data() {\r\n    return {\r\n      PlanDeleteFlagOptions: store.getters.commonEnums['dm.DeliveryPlanDeleteFlagEnum'], // 订单类型\r\n      queryParam: {\r\n        page: 1,\r\n        limit: 20,\r\n        planNo: \"\",\r\n        orderNo: \"\",\r\n        goods: \"\",\r\n        vendorIds:'vendorIds',//用于区分是否为供应商\r\n        sortObj: 'dd.id DESC',//排序\r\n        whereType:1,//气泡查询条件 默认为1 - 全部\r\n        vendor : '', // 供应商编码|名称\r\n        keyword : '',  // 物料编码|名称|描述|图号\r\n        queryDate:'',//要求送货日期\r\n        startDate : '',  // 开始日期\r\n        orderType : '', // 订单类型\r\n        bubGroupStatus: 1,\r\n        deleteFlag:0,\r\n      },\r\n      deliveryCount:{},//气泡数\r\n      isUpdate: 0,\r\n      listLoading: false,\r\n      btnLoading: false,\r\n      formVisible:false,\r\n      list: [],//列表数据\r\n      total: 0,//条数\r\n      selectedDatas: [],/*选择的数据*/\r\n      selectedNum: 0,/*选择数据的条数*/\r\n      userInfo:store.getters.userInfo,/*获取当前用户*/\r\n      buttonFrom: {\r\n        count1: 'primary',\r\n        count2: '',\r\n        count3: '',\r\n        count4: '',\r\n        count5: '',\r\n        count6:'',\r\n      },\r\n      queryDate: [],\r\n    }\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.listLoading = true;\r\n      if (this.queryDate.length !== 0) {\r\n        const startDate = this.$dian.dateFormat(this.queryDate[0], 'YYYY-MM-DD');\r\n        const endDate = this.$dian.dateFormat(this.queryDate[1], 'YYYY-MM-DD');\r\n        this.queryParam.queryDate = startDate +\" 至 \"+endDate;\r\n      }\r\n      getDeliveryPlanList(this.queryParam).then(res => {\r\n        this.total = res.data.totalCount;\r\n        this.list = res.data.list;\r\n        this.listLoading = false;\r\n      }).catch(() => {\r\n        this.listLoading = false;\r\n      })\r\n    },\r\n    //点击气泡查询\r\n    changeCountsButton(stat, name){\r\n      // 动态变换\r\n      this.buttonFrom.count1 = '';//全部\r\n      this.buttonFrom.count2 = '';//已送未收\r\n      this.buttonFrom.count3 = '';//待发出\r\n      this.buttonFrom.count4 = '';//暂收\r\n      this.buttonFrom.count5 = '';//暂退\r\n      this.buttonFrom.count7 = '';//可创建让步单据\r\n      this.buttonFrom[name] = 'primary';\r\n\r\n      this.queryParam.bubGroupStatus = stat;\r\n      this.search();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedDatas = selection.map(item => item)\r\n      //获取所有选中项数组的长度\r\n      this.selectedNum = selection.length\r\n    },\r\n    downTrendsTemplate(){\r\n      let params = {\r\n      }\r\n      let url = '/api/dm/deliveryPlan/exportDynaPlanReplyData';\r\n      this.loadFile(url,'送货计划交期回复数据',params);\r\n    },\r\n    importPlan(){\r\n      let url = \"/api/dm/deliveryPlan/importDynaPlanReplyData\";\r\n      this.$refs.upload.init(url);\r\n    },\r\n    loadFile(url,fileName,params){\r\n      Message({\r\n        duration: 0,   // 设置为0就可以使永久停留\r\n        type: 'warning',\r\n        message: fileName + '下载中....'\r\n      })\r\n      fileExport(url, params).then(res => {\r\n        let data = res.data;\r\n        let fileReader = new FileReader();\r\n        fileReader.onload = function (result) {\r\n          try {\r\n            let jsonData = JSON.parse(this.result); // 说明是普通对象数据，后台转换失败\r\n            if (jsonData.code === 0) {\r\n              // 接口返回的错误信息\r\n              message({\r\n                message: '导出失败',\r\n                type: 'error',\r\n                duration: 1000,\r\n              })\r\n            }\r\n            this.btnLoading = false;\r\n          } catch (err) {\r\n            // 解析成对象失败，说明是正常的文件流\r\n            const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' }); // 如类型为excel,type为：'application/vnd.ms-excel'\r\n            let fileName = '送货计划交期回复数据'\r\n            if(res.headers[\"content-disposition\"]&&res.headers[\"content-disposition\"].split(\"=\").length>1){\r\n              fileName = '送货计划交期回复数据'\r\n            }\r\n            const url = window.URL.createObjectURL(blob);\r\n            const link = document.createElement('a');\r\n            link.style.display = 'none';\r\n            link.href = url;\r\n            link.setAttribute('download', fileName);\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link); // 点击后移除，防止生成很多个隐藏a标签\r\n            Message.closeAll()\r\n            message({\r\n              type: 'success',\r\n              message: '导出成功'\r\n            })\r\n          }\r\n        }\r\n        this.loading = false;\r\n        fileReader.readAsText(data); // 注意别落掉此代码，可以将 Blob 或者 File 对象转根据特殊的编码格式转化为内容(字符串形式)\r\n      }).catch(err => {\r\n        console.error(err);\r\n        message({\r\n          message: \"导出失败\",\r\n          type: 'error',\r\n          duration: 3000,\r\n        })\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 批量确认\r\n    batchConfirm(){\r\n      if (this.selectedDatas.length === 0){\r\n        this.$message.warning('请选择要操作的数据！');\r\n        return;\r\n      }\r\n      this.$confirm('是否要批量确认选中的' + this.selectedNum + '条数据吗？', '提示', {\r\n        confirmButtonText: '确认',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.btnLoading = true;\r\n        this.listLoading = true;\r\n        batchConfirmPlanByVendor(this.selectedDatas).then(res => {\r\n          this.isUpdate = 0;\r\n          this.btnLoading = false;\r\n          this.$message.success('确认成功！');\r\n          this.initData();\r\n        }).catch((err) => {\r\n          this.btnLoading = false;\r\n          this.listLoading = false;\r\n        })\r\n      })\r\n    },\r\n    // 取消修改\r\n    updateCancel(){\r\n      this.isUpdate = 0;\r\n      this.$nextTick(() => {\r\n        this.initData();\r\n      })\r\n    },\r\n    // 搜索方法，并返回到第一页\r\n    search() {\r\n      this.initData();\r\n    },\r\n    // 重置方法\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.queryDate = [];\r\n      this.search();\r\n    },\r\n    //打开送货单详情弹窗\r\n    openInfoForm(id){\r\n      this.formVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.form.init(id);\r\n      })\r\n    },\r\n    // 快速跳转至计划交期确认报表\r\n    openReplyReport() {\r\n      this.$router.push({path: '/dm/report/planReplyReport'})\r\n    },\r\n    //关闭刷新列表数据\r\n    callDeliveryBoardList(){\r\n      this.formVisible = false;\r\n      this.search();\r\n    },\r\n    //导出\r\n    exportHandle() {\r\n      this.$refs.export.init('/api/dm/deliveryPlanItem/export', '送货计划', this.queryParam);\r\n    },\r\n  }\r\n}\r\n", null]}