<template>
  <div class="DIAN-common-layout DIAN-flex-main">
    <div class="DIAN-common-layout-center">
      <el-row class="DIAN-common-search-box" :gutter="16">
        <el-form @submit.native.prevent>
          <el-col :span="4">
            <el-input v-model.trim="queryParam.sourceNo" placeholder="请输入PLM打样单号" clearable/>
          </el-col>
          <el-col :span="4">
            <!-- v-model.trim 可以去除前后空格 -->
            <el-input v-model.trim="queryParam.sampleNo" placeholder="请输入送样单号" clearable/>
          </el-col>
          <el-col :span="4">
            <el-input v-model.trim="queryParam.vendor" placeholder="请输入供应商编码/名称" clearable/>
          </el-col>
          <el-col :span="4">
            <el-input v-model.trim="queryParam.goods" placeholder="请输入物料编码/名称/型号" clearable/>
          </el-col>
          <el-col :span="4">
            <el-input v-model.trim="queryParam.pur" placeholder="请输入采购员名称" clearable/>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-select v-model="queryParam.sampleStat" placeholder="单据状态" clearable>
                <el-option :key="item.key" :label="item.value" :value="item.key"
                           v-for="item in sampleStatOptions"/>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="3">
            <el-form-item>
              <el-select v-model="queryParam.itemStat" placeholder="检验状态" clearable>
                <el-option :key="item.key" :label="item.value" :value="item.key"
                           v-for="item in sampleItemStatOptions"/>
              </el-select>
            </el-form-item>
          </el-col> -->
          <template v-if="showAll">
            <el-col :span="3">
              <el-form-item>
                <el-input v-model.trim="queryParam.dept" placeholder="采购组织编码/名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item>
                <el-select v-model="queryParam.demandClassType" placeholder="请选择单据类型" clearable>
                  <el-option :key="item.key" :label="item.value" :value="item.key"
                            v-for="item in demandTypeOption"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="sampleDates"
                  type="daterange"
                  placeholder="请选择要求送样日期"
                  range-separator="至"
                  start-placeholder="（送样）开始日期"
                  end-placeholder="（送样）结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="demandDates"
                  type="daterange"
                  placeholder="请选择需求日期"
                  range-separator="至"
                  start-placeholder="（需求）开始日期"
                  end-placeholder="（需求）结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="replyDates"
                  type="daterange"
                  placeholder="请选择回复日期"
                  range-separator="至"
                  start-placeholder="（回复）开始日期"
                  end-placeholder="（回复）结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="6">
            <el-form-item>
              <!-- 查询按钮 -->
              <el-button type="primary" icon="el-icon-search" @click="search()">
                {{ $t('common.search') }}
              </el-button>
              <!-- 重置按钮 -->
              <el-button icon="el-icon-refresh-right" @click="reset()">
                {{ $t('common.reset') }}
              </el-button>
              <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true"
                         v-if="!showAll">展开
              </el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else>
                收起
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <div class="DIAN-common-head">
          <div>

          </div>
          <div class="DIAN-common-head-right">
            <!-- <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus"
                       v-has-per="'base:sample:save'">
              {{ $t('common.addBtn')}}
            </el-button> -->
            <el-button type="primary" :loading="btnLoading" icon="el-icon-check" @click="batchConfirmRecSample()" :disabled="selectedDatas.length === 0" v-has-per="'base:sample:confirmRecSample'">批量确认收样</el-button>
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false"
                       @click="search()"/>
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>
        <d-table v-loading="listLoading" :data="list" :hasNO="false" :hasC="true" @selection-change="handleSelectionChange">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column prop="deptName" label="采购组织" align="center" show-overflow-tooltip width="115" sortable/>
          <el-table-column prop="applicant" label="申请人" align="center" show-overflow-tooltip width="115" sortable/>
          <el-table-column prop="applyDeptName" label="申请部门" align="center" show-overflow-tooltip width="115" sortable/>
          <el-table-column prop="applyDate" label="申请日期" align="center" show-overflow-tooltip width="125" sortable/>
          <el-table-column prop="sourceNo" label="PLM打样单号" align="center" show-overflow-tooltip width="150" sortable>
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" @click="goToSampleDemand(scope.row.sourceNo)" v-if="scope.row.sourceNo">
                {{scope.row.sourceNo}}
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="sampleNo" label="送样单号" align="center" show-overflow-tooltip width="125" sortable/>
          <!-- <el-table-column prop="sampleDate" label="送样日期" show-overflow-tooltip width="125" sortable>
            <template slot-scope="scope">
                <span>{{scope.row.sampleDate | date}}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="caseDate" label="送样日期" show-overflow-tooltip width="125" align="center" sortable>
            <template slot-scope="scope">
                <span>{{scope.row.caseDate | date}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="vendorCode" label="供应商编码" align="center" show-overflow-tooltip width="120" sortable/>
          <el-table-column prop="vendorName" label="供应商名称" show-overflow-tooltip width="120" sortable/>
          <el-table-column prop="demandClassType" label="单据类型" align="center" show-overflow-tooltip width="115" sortable>
            <template slot-scope="scope">
                <span>{{scope.row.demandClassType | commonEnumsTurn('base.DemandClassTypeEnum')}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="caseStat" label="单据状态" align="center" show-overflow-tooltip width="115" sortable>
            <template slot-scope="scope">
                <span>{{scope.row.caseStat | commonEnumsTurn('base.SampleEnums')}}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="itemStat" label="检验状态" align="center" show-overflow-tooltip width="115" sortable>
            <template slot-scope="scope">
              <span>{{scope.row.itemStat | commonEnumsTurn('base.SampleItemEnums')}}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="goodsErpCode" label="物料编码" align="center" show-overflow-tooltip width="120" sortable/>
          <el-table-column prop="goodsName" label="物料名称" show-overflow-tooltip width="120" sortable/>
          <el-table-column prop="goodsModel" label="物料型号" show-overflow-tooltip width="120" sortable/>
          
          <el-table-column prop="demandQty" label="需求数量" align="center" show-overflow-tooltip width="100" sortable/>
          <el-table-column prop="demandDate" label="需求日期" align="center" show-overflow-tooltip width="100" sortable>
            <template slot-scope="scope">
                <span>{{scope.row.demandDate | date}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="replyQuantity" label="回复数量" align="center" show-overflow-tooltip width="100" sortable/>
          <el-table-column prop="replyDeliveryDate" label="回复交期" align="center" show-overflow-tooltip width="100" sortable>
            <template slot-scope="scope">
                <span>{{scope.row.replyDeliveryDate | date}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="replyState" label="答交状态" align="center" width="150">
                  <template slot-scope="scope">
                    <span>{{scope.row.replyState | commonEnumsTurn('base.ReplyStateEnum')}}</span>
                  </template>
                </el-table-column>
          <el-table-column prop="purName" label="采购员" align="center" show-overflow-tooltip width="100" sortable/>
          
          <el-table-column prop="goodsNum" label="送样数量" align="center" show-overflow-tooltip width="100" sortable/>
          <el-table-column prop="vendorRemark" label="供应商说明" show-overflow-tooltip width="200" sortable/>
          <el-table-column prop="creater" label="创建人" show-overflow-tooltip width="150" sortable/>
          <el-table-column prop="createDate" label="创建时间" width="150" sortable/>
          <el-table-column prop="modifier" label="更新人" show-overflow-tooltip width="150" sortable/>
          <el-table-column prop="modifyDate" label="更新时间" width="150" sortable/>
          <el-table-column label="操作" fixed="right" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="addOrUpdateHandle(scope.row.id)" v-has-per="'base:sample:info'">
                <span v-if="scope.row.sampleStat == 1">
                  {{ $t('common.editBtn')}}
                </span>
                <span v-if="scope.row.sampleStat > 1">
                  {{ $t('common.lookBtn')}}
                </span>
              </el-button>
            </template>
          </el-table-column>
        </d-table>
        <d-pagination :total="total" :page.sync="queryParam.page"
                    :limit.sync="queryParam.limit" @pagination="initData"/>
      </div>
      <!-- FORM表单 -->
      <Form ref="detail" v-show="detailVisible" @callRefreshList="callRefreshList"></Form>
      <!-- 送样单质检表单 -->
      <QualityForm ref="quality" v-show="qualityVisible" @callRefreshList="callRefreshList"></QualityForm>
    </div>
  </div>
</template>

<script>
import {getSampleList,delSample,receiveMaterial} from '@/api/base/sample'
import { getSampleDemandList } from '@/api/base/sampleDemand';
import Form from './form'
import QualityForm from './qualityForm.vue'
import store from "@/store";

export default {
  components: {Form,QualityForm},
  name: 'base-sample-tenant',
  data() {
    return {
      list: [],
      total: 0,
      showAll: false,
      queryParam: {//查询条件
        sampleNo: '',//需求申请单号
        ifTenant:'ifTenant',//用于区分是否为供应商登录，为ifTenant则是采购方
        sampleStat:'',//主表单据状态
        itemStat:'',//明细表检验状态
        vendor:'',//供应商名称/编码
        page: 1,
        limit: 20,
        goods:'',//物料信息
        dept:'',//采购组织机构信息
        sampleDate:'',
        startDate:'',
        endDate:'',
        demandDate: '', // 需求日期
        demandDateStart: '', // 需求开始日期
        demandDateEnd: '', // 需求结束日期
        replyDeliveryDate: '', // 回复日期
        replyDateStart: '', // 回复开始日期
        replyDateEnd: '', // 回复结束日期
      },
      detailVisible: false,
      qualityVisible: false,
      listLoading: true,
      btnLoading: false,
      selectedDatas: [],
      sampleDates: [],
      demandDates: [],
      replyDates: [],
      // sampleItemStatOptions: store.getters.commonEnums['base.SampleItemEnums'], // 明细行检验状态
      demandTypeOption: store.getters.commonEnums['base.DemandClassTypeEnum'], // 单据类型
      sampleStatOptions:[
        {key:3,value:'待送样'},
        {key:4,value:'待收样'},
        {key:5,value:'已收样'},
        {key:11,value:'已拒绝'}
      ],
    }
  },
  created() {
    this.initData()
  },
  watch: {
    $route: {
      handler(to) {
        // 监听路由变化，处理站内信跳转
        if (to.query && to.query.id) {
          // 如果URL中有id参数，直接打开详情
          this.handleMessageNavigation(to.query.id);
        }
      },
      immediate: true
    }
  },
  filters:{
    date(time){
      if (!time){
        return ''
      }
      let date = new Date(time)
      let year = date.getFullYear();
      let month = date.getMonth()+1;
      let day = date.getDate();
      return year+"-"+month+"-"+day;
    }
  },
  methods: {
    // 页面初始化加载列表数据
    initData() {
      this.listLoading = true
      let query = {
        ...this.queryParam,
      }
      getSampleList(query).then(res => {
        debugger
        this.total = res.data.totalCount
        this.list = res.data.list
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    //打开新增/修改弹窗页面
    addOrUpdateHandle(id) {
      this.detailVisible = true;
      this.$nextTick(() => {
        this.$refs.detail.init(id);
      })
    },
    // 处理站内信跳转
    handleMessageNavigation(id) {
      if (id) {
        // 清除URL参数，避免重复触发
        this.$router.replace({
          path: this.$route.path,
          query: {}
        });
        // 打开详情页面
        this.addOrUpdateHandle(id);
      }
    },
    //审核
    handleCheck(id) {

    },
    search() {
      this.queryParam.page=1;
      // 处理送样日期
      if (this.sampleDates.length !== 0) {
        const startDate = this.$dian.dateFormat(this.sampleDates[0], 'YYYY-MM-DD');
        const endDate = this.$dian.dateFormat(this.sampleDates[1], 'YYYY-MM-DD');
        this.queryParam.sampleDate = startDate +" 至 "+endDate;
        this.queryParam.startDate = startDate;
        this.queryParam.endDate = endDate;
      } else {
        this.queryParam.sampleDate = '';
        this.queryParam.startDate = '';
        this.queryParam.endDate = '';
      }

      // 处理需求日期
      if (this.demandDates.length !== 0) {
        const startDate = this.$dian.dateFormat(this.demandDates[0], 'YYYY-MM-DD');
        const endDate = this.$dian.dateFormat(this.demandDates[1], 'YYYY-MM-DD');
        this.queryParam.demandDate = startDate +" 至 "+endDate;
        this.queryParam.demandDateStart = startDate;
        this.queryParam.demandDateEnd = endDate;
      } else {
        this.queryParam.demandDate = '';
        this.queryParam.demandDateStart = '';
        this.queryParam.demandDateEnd = '';
      }

      // 处理回复日期
      if (this.replyDates.length !== 0) {
        const startDate = this.$dian.dateFormat(this.replyDates[0], 'YYYY-MM-DD');
        const endDate = this.$dian.dateFormat(this.replyDates[1], 'YYYY-MM-DD');
        this.queryParam.replyDeliveryDate = startDate +" 至 "+endDate;
        this.queryParam.replyDateStart = startDate;
        this.queryParam.replyDateEnd = endDate;
      } else {
        this.queryParam.replyDeliveryDate = '';
        this.queryParam.replyDateStart = '';
        this.queryParam.replyDateEnd = '';
      }

      this.initData()
    },
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.sampleDates = [];
      this.demandDates = [];
      this.replyDates = [];
      this.initData()
    },
    //删除
    handleDel(id) {
      this.$confirm(this.$t('common.delTip'), this.$t('common.tipTitle'), {
        type: 'warning'
      }).then(() => {
        delSample(id).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功',
            duration: 1500,
            onClose: () => {
              this.search()
            }
          })
        })
      }).catch(() => {
      })
    },
    callRefreshList() {
      this.detailVisible = false;
      this.qualityVisible = false;
      this.search();
    },
    handleSelectionChange(selection){
      this.selectedDatas = selection.map(item => item)
    },
    quality(row){
      this.qualityVisible = true;
      let isAdd = false;
      if(row.ifInspection > 0){
        isAdd = true
      }
      this.$nextTick(() => {
        this.$refs.quality.init(row.id,isAdd);
      })
    },
    goToSampleDemand(sourceNo) {
      if (!sourceNo) {
        this.$message.warning('PLM打样单号不能为空');
        return;
      }
      // 根据sourceNo查询送样需求单
      const params = {
        demandNo: sourceNo,
        demandClassType: 3, // 包括内部打样和采购打样的所有单据
        limit: 1
      };
      getSampleDemandList(params).then(res => {
        if (res.data && res.data.list && res.data.list.length > 0) {
          const demandId = res.data.list[0].id;
          const timestamp = Date.now();
          this.$router.push({
            path: '/base/sampleDemand/tenant',
            query: {
              detailVisible: true,
              id: demandId,
              t: timestamp
            }
          });
        } else {
          this.$message.warning(`未找到PLM打样单号为"${sourceNo}"的送样需求单`);
        }
      }).catch(error => {
        console.error('查询送样需求单失败:', error);
        this.$message.error('查询送样需求单失败，请稍后重试');
      });
    },
    //批量确认收样
    batchConfirmRecSample(){
      // 检查是否有选中的数据
      if (this.selectedDatas.length === 0) {
        this.$message.error('请至少选择一条需要确认收样的记录');
        return;
      }
      // 检查是否已收样
      if(this.selectedDatas.some(item => item.caseStat ==  5)){
        this.$message.error('存在已收样的物料明细记录，请勿重复收样');
        return;
      }
      // 检查是否为待收样状态
      if(this.selectedDatas.some(item => item.caseStat != 4)){
        this.$message.error('存在非待收样状态的物料明细记录，无法确认收样');
        return;
      }
      this.btnLoading = true;
      const ids = this.selectedDatas.map(item => item.itemId); // 物料明细ID
      this.$confirm(`确认收样选中的${this.selectedDatas.length}条记录？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        receiveMaterial(ids).then(res => {
          this.$message({
            message: "批量确认收样成功",
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.btnLoading = false;
              this.search(); // 刷新列表
            }
          });
        }).catch(err => {
          this.btnLoading = false;
          this.$message.error('批量确认收样失败: ' + (err.message || '未知错误'));
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消批量确认收样'
        });
      });
    },
  }
}
</script>
<style></style>
