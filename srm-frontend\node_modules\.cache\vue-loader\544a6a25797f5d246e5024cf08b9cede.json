{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue?vue&type=template&id=0a0920c3&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue", "mtime": 1754404617956}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n<div class=\"DIAN-common-layout DIAN-flex-main\">\n  <div class=\"DIAN-common-layout-center\">\n    <el-row class=\"DIAN-common-search-box\" :gutter=\"16\">\n      <el-form @submit.native.prevent>\n        <el-col :span=\"6\">\n          <!-- v-model.trim 可以去除前后空格 -->\n          <el-input v-model.trim=\"queryParam.demandNo\" placeholder=\"请输入需求单号\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-input v-model.trim=\"queryParam.applicant\" placeholder=\"请输入申请人名称\" clearable/>\n        </el-col>\n        <!-- <el-col :span=\"4\">\n          <el-form-item>\n            <el-select v-model=\"queryParam.isNeedUpFile\" placeholder=\"检验状态\" clearable>\n              <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                         v-for=\"item in validOps\"/>\n            </el-select>\n          </el-form-item>\n        </el-col> -->\n        <el-col :span=\"4\">\n          <el-input v-model.trim=\"queryParam.pur\" placeholder=\"请输入采购员名称或编码\" clearable/>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-input v-model.trim=\"queryParam.goods\" placeholder=\"请输入物料编码/名称/型号\" clearable/>\n        </el-col>\n        <el-col :span=\"4\">\n            <el-form-item>\n              <el-select v-model=\"queryParam.caseStat\" placeholder=\"请选择需求状态\" clearable>\n                <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                          v-for=\"item in caseStatOptions\"/>\n              </el-select>\n            </el-form-item>\n        </el-col>\n        \n        <template v-if=\"showAll\">\n          <!-- <el-col :span=\"4\">\n            <el-form-item>\n              <el-input v-model.trim=\"queryParam.dept\" placeholder=\"采购组织编码/名称\" clearable />\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"6\">\n            <el-form-item>\n              <el-date-picker\n                v-model=\"demandDates\"\n                type=\"daterange\"\n                placeholder=\"请选择需求日期\"\n                range-separator=\"至\"\n                start-placeholder=\"（需求）开始日期\"\n                end-placeholder=\"（需求）结束日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item>\n              <el-date-picker\n                v-model=\"applyDates\"\n                type=\"daterange\"\n                placeholder=\"请选择申请日期\"\n                range-separator=\"至\"\n                start-placeholder=\"（申请）开始日期\"\n                end-placeholder=\"（申请）结束日期\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"4\">\n            <el-form-item>\n              <el-select v-model=\"queryParam.selectType\" placeholder=\"请选择查询类型\" clearable>\n                <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                          v-for=\"item in selectTypeOptions\"/>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"4\">\n            <el-form-item>\n              <el-select v-model=\"queryParam.demandClassType\" placeholder=\"请选择单据类型\" clearable>\n                <el-option :key=\"item.key\" :label=\"item.value\" :value=\"item.key\"\n                          v-for=\"item in demandTypeOption\"/>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n        </template>\n        <el-col :span=\"6\">\n          <el-form-item>\n            <!-- 查询按钮 -->\n            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search()\">\n              {{ $t('common.search') }}\n            </el-button>\n            <!-- 重置按钮 -->\n            <el-button icon=\"el-icon-refresh-right\" @click=\"reset()\">\n              {{ $t('common.reset') }}\n            </el-button>\n            <el-button type=\"text\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\"\n                       v-if=\"!showAll\">展开\n            </el-button>\n            <el-button type=\"text\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>\n              收起\n            </el-button>\n          </el-form-item>\n        </el-col>\n      </el-form>\n    </el-row>\n    <div class=\"DIAN-common-layout-main DIAN-flex-main\">\n      <div class=\"DIAN-common-head\">\n        <div>\n\n        </div>\n        <div class=\"DIAN-common-head-right\">\n          <el-button type=\"primary\" @click=\"addOrUpdateHandle()\" icon=\"el-icon-plus\"\n                     v-has-per=\"'base:sampleDemand:save'\">\n            {{ $t('common.addBtn')}}\n          </el-button>\n          <el-button type=\"primary\" @click=\"batchReturnSample()\" icon=\"el-icon-back\" :loading=\"btnLoading\" :disabled=\"selectedDatas.length === 0\" v-has-per=\"'base:sampleDemand:returnSample'\">退回PLM</el-button>\n          <el-button type=\"primary\" @click=\"openVendorDialog()\" icon=\"el-icon-plus\"\n                    :disabled=\"selectedDatas.length === 0\" :loading=\"btnLoading\" v-has-per=\"'base:sampleDemand:issuedSample'\">\n            添加供应商\n          </el-button>\n          <el-button type=\"primary\" @click=\"openBuyerDialog()\" icon=\"el-icon-user\"\n                    :disabled=\"selectedDatas.length === 0\" :loading=\"btnLoading\" v-has-per=\"'base:sampleDemand:updateBuyer'\">\n            更换采购员\n          </el-button>\n          <el-button type=\"primary\" @click=\"batchLocalClosed()\" icon=\"el-icon-check\" :loading=\"btnLoading\" :disabled=\"selectedDatas.length === 0\" v-has-per=\"'base:sampleDemand:localClosed'\">\n            本地结案\n          </el-button>\n          <el-tooltip effect=\"dark\" :content=\"$t('common.refresh')\" placement=\"top\">\n            <el-link icon=\"icon-ym icon-ym-Refresh DIAN-common-head-icon\" :underline=\"false\"\n                     @click=\"search()\"/>\n          </el-tooltip>\n          <d-screen-full/>\n        </div>\n      </div>\n      <d-table v-loading=\"listLoading\" :data=\"list\" :hasNO=\"false\" :hasC=\"true\" @selection-change=\"handleSelectionChange\">\n        <!-- 物料对应供应商数据展开行 -->\n        <el-table-column type=\"expand\">\n          <template slot-scope=\"props\">\n            <el-table\n              :data=\"props.row.sampleDemandVendorEntityList || []\"\n              style=\"width: 75%\"\n              size=\"mini\"\n              border>\n              <el-table-column type=\"index\" width=\"100\" label=\"序号\" align=\"center\" sortable/>\n              <el-table-column prop=\"goodsErpCode\" label=\"物料编码\" align=\"center\" width=\"150\" show-overflow-tooltip/>\n              <el-table-column prop=\"goodsName\" label=\"物料名称\" align=\"center\" width=\"150\" show-overflow-tooltip/>\n              <el-table-column prop=\"vendorCode\" label=\"供应商编码\" width=\"120\"></el-table-column>\n              <el-table-column prop=\"vendorName\" label=\"供应商名称\" width=\"180\"></el-table-column>\n              <el-table-column prop=\"caseStat\" label=\"结案状态\" width=\"120\">\n                <template slot-scope=\"scope\">\n                  <span>{{ scope.row.caseStat | commonEnumsTurn('base.CaseStatEnum') }}</span>\n                </template>\n              </el-table-column>\n              <!-- <el-table-column prop=\"caseDate\" label=\"结案日期\" width=\"150\"></el-table-column> -->\n              <!-- <el-table-column prop=\"isReturn\" label=\"是否退回\" width=\"100\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.isReturn | commonEnumsTurn('comm.ValidEnum') }}\n                </template>\n              </el-table-column> -->\n              <el-table-column prop=\"assigner\" label=\"分配人\" width=\"100\" />\n              <el-table-column prop=\"assignDate\" label=\"分配时间\" width=\"150\" />\n              <el-table-column prop=\"modifier\" label=\"更新人\" width=\"100\" />\n              <el-table-column prop=\"modifyDate\" label=\"更新时间\" width=\"150\" />\n              <!-- <el-table-column prop=\"returnRemark\" label=\"退回备注\" width=\"200\" show-overflow-tooltip></el-table-column> -->\n              <el-table-column prop=\"remark\" label=\"备注\" width=\"200\" show-overflow-tooltip></el-table-column>\n            </el-table>\n          </template>\n        </el-table-column>\n        <el-table-column type=\"index\" width=\"50\" label=\"序号\" align=\"center\" sortable />\n        <el-table-column prop=\"orgName\" label=\"组织名称\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <el-table-column prop=\"demandNo\" label=\"需求单号\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <el-table-column prop=\"demandClassType\" label=\"单据类型\" align=\"center\" show-overflow-tooltip width=\"120\" sortable>\n          <template slot-scope=\"scope\">\n            <span>{{ scope.row.demandClassType | commonEnumsTurn('base.DemandClassTypeEnum') }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"isNeedUpFile\" label=\"是否需要上传文件\" align=\"center\" show-overflow-tooltip width=\"150\" sortable>\n          <template slot-scope=\"scope\">\n            <el-tag v-if=\"scope.row.isNeedUpFile === 1\">是</el-tag>\n            <el-tag v-else>否</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"applicant\" label=\"申请人\" align=\"center\" show-overflow-tooltip width=\"120\" sortable />\n        <el-table-column prop=\"applyDeptName\" label=\"申请部门\" show-overflow-tooltip width=\"120\" sortable />\n        <el-table-column prop=\"applyDate\" label=\"申请日期\" show-overflow-tooltip width=\"120\" sortable />\n        <!-- <el-table-column prop=\"remark\" label=\"备注\" align=\"center\" show-overflow-tooltip width=\"200\" sortable /> -->\n\n        <!-- 物料相关信息 -->\n        <el-table-column prop=\"purName\" label=\"采购员\" align=\"center\" show-overflow-tooltip width=\"100\" sortable />\n        <el-table-column prop=\"purCode\" label=\"采购员编码\" align=\"center\" show-overflow-tooltip width=\"120\" sortable />\n        <el-table-column prop=\"goodsCode\" label=\"物料编码\" align=\"center\" show-overflow-tooltip width=\"120\" sortable />\n        <!-- <el-table-column prop=\"goodsErpCode\" label=\"物料ERP编码\" align=\"center\" show-overflow-tooltip width=\"120\" sortable /> -->\n        <el-table-column prop=\"goodsName\" label=\"物料名称\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <el-table-column prop=\"goodsModel\" label=\"物料规格型号\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <!-- <el-table-column prop=\"vendorCode\" label=\"供应商编码\" align=\"center\" show-overflow-tooltip width=\"150\" sortable /> -->\n        <el-table-column prop=\"vendorName\" label=\"建议供应商\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <el-table-column prop=\"demandQty\" label=\"需求数量\" align=\"center\" width=\"100\" sortable />\n        <el-table-column prop=\"uomName\" label=\"单位\" align=\"center\" width=\"80\" sortable />\n        <el-table-column prop=\"demandDate\" label=\"需求日期\" align=\"center\" show-overflow-tooltip width=\"120\" sortable>\n          <template slot-scope=\"scope\">\n            {{ scope.row.demandDate | date }}\n          </template>\n        </el-table-column>\n        <!-- <el-table-column prop=\"saleDeptCode\" label=\"销售部门编码\" align=\"center\" show-overflow-tooltip width=\"150\" sortable /> -->\n        <el-table-column prop=\"saleDeptName\" label=\"销售部门名称\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <el-table-column prop=\"model\" label=\"机型\" align=\"center\" show-overflow-tooltip width=\"150\" sortable />\n        <el-table-column prop=\"purpose\" label=\"用途\" align=\"center\" show-overflow-tooltip width=\"120\" sortable />\n        <el-table-column prop=\"caseStat\" label=\"需求状态\" align=\"center\" show-overflow-tooltip width=\"150\" sortable>\n                <template slot-scope=\"scope\">\n                  <span>{{ scope.row.caseStat | commonEnumsTurn('base.CaseStatEnum') }}</span>\n                </template>\n        </el-table-column>\n        <el-table-column prop=\"returnCause\" label=\"退回原因\" width=\"200\" show-overflow-tooltip sortable></el-table-column>\n        <el-table-column prop=\"caseDate\" label=\"结案日期\" align=\"center\" show-overflow-tooltip width=\"150\" sortable>\n          <template slot-scope=\"scope\">\n            {{ scope.row.caseDate | date }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"itemRemark\" label=\"行备注\" align=\"center\" show-overflow-tooltip width=\"200\" sortable />\n        <el-table-column prop=\"creater\" label=\"创建人\" width=\"100\" sortable />\n        <el-table-column prop=\"createDate\" label=\"创建时间\" width=\"150\" sortable />\n        <el-table-column prop=\"modifier\" label=\"更新人\" width=\"100\" sortable />\n        <el-table-column prop=\"modifyDate\" label=\"更新时间\" width=\"150\" sortable />\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"addOrUpdateHandle(scope.row.id)\" v-has-per=\"'base:sample:info'\">\n              {{ $t('common.lookBtn')}}\n            </el-button>\n          </template>\n        </el-table-column>\n      </d-table>\n      <d-pagination :total=\"total\" :page.sync=\"queryParam.page\"\n                  :limit.sync=\"queryParam.limit\" @pagination=\"initData\"/>\n    </div>\n    <!-- FORM表单 -->\n    <Form ref=\"detail\" v-show=\"detailVisible\" @callRefreshList=\"callRefreshList\"></Form>\n    <!-- 供应商选择弹窗 -->\n    <VendorProp ref=\"vendor\" :singleChoice=\"false\" @callData=\"vendorSelect\"></VendorProp>\n    <!-- 用户选择弹窗 -->\n    <UserProp ref=\"userProp\" :singleChoice=\"true\" :title=\"'采购员'\" @callData=\"userSelect\"></UserProp>\n  </div>\n</div>\n", null]}