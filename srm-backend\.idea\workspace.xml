<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ec739fda-18fb-40c8-b26c-a0532412eed4" name="Changes" comment="送样通知单增加申请人字段">
      <change beforePath="$PROJECT_DIR$/1007-cloud-base-applicaion/src/main/java/com/dian/modules/base/service/impl/SampleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/1007-cloud-base-applicaion/src/main/java/com/dian/modules/base/service/impl/SampleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/1008-cloud-order-applicaion/src/main/java/com/dian/modules/order/service/impl/PurServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/1008-cloud-order-applicaion/src/main/java/com/dian/modules/order/service/impl/PurServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/controller/DeliveryPlanController.java" beforeDir="false" afterPath="$PROJECT_DIR$/1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/controller/DeliveryPlanController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/service/impl/DeliveryPlanServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/service/impl/DeliveryPlanServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/service/impl/DeliveryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/service/impl/DeliveryServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Interface" />
        <option value="Mybatis Mapper" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="LOCAL">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="gve_dev_lf" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="gve_dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/1007-cloud-base-applicaion/src/main/java/com/dian/modules/base/service/SampleService.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/1007-cloud-base-applicaion/src/main/java/com/dian/modules/base/service/impl/DemandCommitServiceImpl.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/1007-cloud-base-applicaion/src/main/java/com/dian/modules/base/service/impl/SampleServiceImpl.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\apache-maven-3.9.8-bin\apache-maven-3.9.8" />
        <option name="localRepository" value="E:\apache-maven-3.9.8-bin\maven-localRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\apache-maven-3.9.8-bin\apache-maven-3.9.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="vmOptionsForImporter" value="-Xmx1024m" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="vmOptions" value="-DarchetypeCatalog=internal" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yRCl1L5b6o3FNzARKwBhs7Gk8N" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.SimpleAppliction.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;gve__dev__lf&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/Desktop/srm/srm-frontend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;service.view.auto.scroll.from.source&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\IntelliJ IDEA 2025.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql_aurora_aws&quot;
    ]
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="hiddenConfigurations">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <set>
              <option value="OnLineApplication" />
              <option value="SimpleAppliction" />
            </set>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SimpleAppliction">
    <configuration name="OnLineApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="4000-cloud-online-applicaion" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.dian.OnLineApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimpleAppliction" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="5000-cloud-simple-start" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.dian.SimpleAppliction" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.OnLineApplication" />
      <item itemvalue="Spring Boot.SimpleAppliction" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ec739fda-18fb-40c8-b26c-a0532412eed4" name="Changes" comment="" />
      <created>1749788147338</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749788147338</updated>
      <workItem from="1749788149438" duration="159000" />
      <workItem from="1749793160108" duration="6834000" />
      <workItem from="1749800197365" duration="5624000" />
      <workItem from="1749806506792" duration="307000" />
      <workItem from="1749886563232" duration="45000" />
      <workItem from="1749889062359" duration="16000" />
      <workItem from="1749893914541" duration="1399000" />
      <workItem from="1749895414098" duration="1300000" />
      <workItem from="1749900087021" duration="188000" />
      <workItem from="1750034515580" duration="14102000" />
      <workItem from="1750056961777" duration="12698000" />
      <workItem from="1750120922179" duration="5350000" />
      <workItem from="1750126475458" duration="18572000" />
      <workItem from="1750169229669" duration="615000" />
      <workItem from="1750207417264" duration="22337000" />
      <workItem from="1750293736700" duration="16367000" />
      <workItem from="1750339913208" duration="2150000" />
      <workItem from="1750380167119" duration="23651000" />
      <workItem from="1750599867191" duration="2478000" />
      <workItem from="1750639729705" duration="20064000" />
      <workItem from="1750726223940" duration="18886000" />
      <workItem from="1750812713309" duration="25746000" />
      <workItem from="1750899585938" duration="23079000" />
      <workItem from="1750985714231" duration="21559000" />
      <workItem from="1751027865091" duration="612000" />
      <workItem from="1751074906246" duration="69000" />
      <workItem from="1751074989825" duration="41000" />
      <workItem from="1751075041715" duration="40000" />
      <workItem from="1751075117873" duration="103000" />
      <workItem from="1751075248013" duration="78000" />
      <workItem from="1751075362848" duration="473000" />
      <workItem from="1751205813247" duration="5498000" />
      <workItem from="1751245017033" duration="21460000" />
      <workItem from="1751281956169" duration="6366000" />
      <workItem from="1751331443072" duration="1257000" />
      <workItem from="1751338472348" duration="12486000" />
      <workItem from="1751424650281" duration="4000" />
      <workItem from="1751424679005" duration="236000" />
      <workItem from="1751424933593" duration="5828000" />
      <workItem from="1751438578782" duration="13062000" />
      <workItem from="1751504018567" duration="1073000" />
      <workItem from="1751506243235" duration="694000" />
      <workItem from="1751507539218" duration="12988000" />
      <workItem from="1751527578896" duration="60000" />
      <workItem from="1751527678112" duration="912000" />
      <workItem from="1751530146443" duration="5858000" />
      <workItem from="1751590498639" duration="9001000" />
      <workItem from="1751607121015" duration="13171000" />
      <workItem from="1751677700958" duration="101000" />
      <workItem from="1751677955616" duration="2407000" />
      <workItem from="1751682394992" duration="8991000" />
      <workItem from="1751699132704" duration="37000" />
      <workItem from="1751699194360" duration="1079000" />
      <workItem from="1751700291727" duration="97000" />
      <workItem from="1751700404100" duration="103000" />
      <workItem from="1751700530180" duration="242000" />
      <workItem from="1751700787182" duration="93000" />
      <workItem from="1751700893290" duration="7007000" />
      <workItem from="1751850703320" duration="29411000" />
      <workItem from="1751937110201" duration="31192000" />
      <workItem from="1752022995732" duration="38099000" />
      <workItem from="1752109488234" duration="30011000" />
      <workItem from="1752159461932" duration="81000" />
      <workItem from="1752195904439" duration="3787000" />
      <workItem from="1752200369157" duration="19806000" />
      <workItem from="1752233378997" duration="1109000" />
      <workItem from="1752346331205" duration="2424000" />
      <workItem from="1752350412108" duration="49000" />
      <workItem from="1752350519172" duration="528000" />
      <workItem from="1752454732110" duration="16696000" />
      <workItem from="1752477390023" duration="10029000" />
      <workItem from="1752488793102" duration="97000" />
      <workItem from="1752541146836" duration="32557000" />
      <workItem from="1752627884243" duration="23673000" />
      <workItem from="1752714221048" duration="28580000" />
      <workItem from="1752800486322" duration="23900000" />
      <workItem from="1752843870268" duration="6265000" />
      <workItem from="1752891027432" duration="17788000" />
      <workItem from="1753059753757" duration="29754000" />
      <workItem from="1753147393674" duration="26097000" />
      <workItem from="1753232414445" duration="25848000" />
      <workItem from="1753274722258" duration="3785000" />
      <workItem from="1753319139116" duration="28485000" />
      <workItem from="1753356127438" duration="12586000" />
      <workItem from="1753405386241" duration="26854000" />
      <workItem from="1753445534521" duration="6531000" />
      <workItem from="1753492430214" duration="8951000" />
      <workItem from="1753501526844" duration="2000000" />
      <workItem from="1753504522251" duration="18283000" />
      <workItem from="1753588977272" duration="16188000" />
      <workItem from="1753623423932" duration="51000" />
      <workItem from="1753664808741" duration="232000" />
      <workItem from="1753665078197" duration="28099000" />
      <workItem from="1753750816636" duration="12178000" />
      <workItem from="1753769567615" duration="438000" />
      <workItem from="1753770058529" duration="721000" />
      <workItem from="1753771032835" duration="102000" />
      <workItem from="1753771430589" duration="10641000" />
      <workItem from="1753837394916" duration="19832000" />
      <workItem from="1753923836710" duration="18904000" />
      <workItem from="1753957105820" duration="442000" />
      <workItem from="1753957579489" duration="46000" />
      <workItem from="1753964308501" duration="1565000" />
      <workItem from="1753966581726" duration="93000" />
      <workItem from="1754010268591" duration="19259000" />
      <workItem from="1754042960684" duration="201000" />
      <workItem from="1754218731520" duration="1508000" />
      <workItem from="1754220292087" duration="426000" />
      <workItem from="1754236592099" duration="150000" />
      <workItem from="1754237907323" duration="106000" />
      <workItem from="1754269262986" duration="24936000" />
      <workItem from="1754309283697" duration="3796000" />
      <workItem from="1754355787987" duration="16297000" />
    </task>
    <task id="LOCAL-00001" summary="文档管理功能（采购）">
      <option name="closed" value="true" />
      <created>1750904176369</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750904176369</updated>
    </task>
    <task id="LOCAL-00002" summary="Merge branch 'gve_dev' of codeup.aliyun.com:9dyun/GYD-SRM/srm-backend into gve_dev_lf&#10;&#10;# Conflicts:&#10;#&#9;1009-cloud-dm-applicaion/src/main/java/com/dian/modules/dm/service/impl/TrialProdResServiceImpl.java">
      <option name="closed" value="true" />
      <created>1750906888602</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750906888602</updated>
    </task>
    <task id="LOCAL-00003" summary="文档管理接口调整">
      <option name="closed" value="true" />
      <created>1751445709568</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751445709568</updated>
    </task>
    <task id="LOCAL-00004" summary="试产结果单接口调整">
      <option name="closed" value="true" />
      <created>1751450492197</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751450492197</updated>
    </task>
    <task id="LOCAL-00005" summary="试产结果单接口（分页查询条件）调整">
      <option name="closed" value="true" />
      <created>1751528385096</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751528385096</updated>
    </task>
    <task id="LOCAL-00006" summary="测试环境数据库链接地址修改（域名---&gt;IP）">
      <option name="closed" value="true" />
      <created>1751530525000</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751530525000</updated>
    </task>
    <task id="LOCAL-00007" summary="文档管理接口调整">
      <option name="closed" value="true" />
      <created>1751703358150</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751703358150</updated>
    </task>
    <task id="LOCAL-00008" summary="驳回审核接口调整">
      <option name="closed" value="true" />
      <created>1751707964824</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751707964824</updated>
    </task>
    <task id="LOCAL-00009" summary="送样流程调整">
      <option name="closed" value="true" />
      <created>1752130883335</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752130883335</updated>
    </task>
    <task id="LOCAL-00010" summary="根据PLM同步保存样品需求单接口调整">
      <option name="closed" value="true" />
      <created>1752140226246</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752140226246</updated>
    </task>
    <task id="LOCAL-00011" summary="内部打样供应商默认为线材车间">
      <option name="closed" value="true" />
      <created>1752218380864</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752218380864</updated>
    </task>
    <task id="LOCAL-00012" summary="送样需求单列表新增过滤&#10;需求状态字段调整">
      <option name="closed" value="true" />
      <created>1752233943281</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752233943281</updated>
    </task>
    <task id="LOCAL-00013" summary="采购订单确认（供应方）">
      <option name="closed" value="true" />
      <created>1752463332638</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752463332638</updated>
    </task>
    <task id="LOCAL-00014" summary="采购订单确认接口调整">
      <option name="closed" value="true" />
      <created>1752475243565</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752475243565</updated>
    </task>
    <task id="LOCAL-00015" summary="送样通知单新增单据类型字段">
      <option name="closed" value="true" />
      <created>1752481559213</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752481559213</updated>
    </task>
    <task id="LOCAL-00016" summary="送样通知单新增单据类型字段">
      <option name="closed" value="true" />
      <created>1752483423325</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752483423325</updated>
    </task>
    <task id="LOCAL-00017" summary="送样需求单新增单据类型查询条件">
      <option name="closed" value="true" />
      <created>1752544236107</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752544236107</updated>
    </task>
    <task id="LOCAL-00018" summary="送样需求单更新接口调整">
      <option name="closed" value="true" />
      <created>1752557511118</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752557511118</updated>
    </task>
    <task id="LOCAL-00019" summary="送样通知单新增查询条件">
      <option name="closed" value="true" />
      <created>1752629267103</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1752629267103</updated>
    </task>
    <task id="LOCAL-00020" summary="新增库存填报单接口">
      <option name="closed" value="true" />
      <created>1752721674996</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752721674996</updated>
    </task>
    <task id="LOCAL-00021" summary="库存填报单导入功能">
      <option name="closed" value="true" />
      <created>1752806216738</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1752806216738</updated>
    </task>
    <task id="LOCAL-00022" summary="库存填报单接口调整">
      <option name="closed" value="true" />
      <created>1752807224285</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1752807224285</updated>
    </task>
    <task id="LOCAL-00023" summary="库存填报单接口调整">
      <option name="closed" value="true" />
      <created>1752807787692</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1752807787692</updated>
    </task>
    <task id="LOCAL-00024" summary="送样需求、通知单优化调整">
      <option name="closed" value="true" />
      <created>1752915265052</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752915265052</updated>
    </task>
    <task id="LOCAL-00025" summary="送样需求、通知单优化调整">
      <option name="closed" value="true" />
      <created>1753167285605</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753167285605</updated>
    </task>
    <task id="LOCAL-00026" summary="采购订单查询接口调整&#10;送样需求单、通知单优化调整">
      <option name="closed" value="true" />
      <created>1753264236293</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753264236293</updated>
    </task>
    <task id="LOCAL-00027" summary="新建送样通知单保存接口调整 采购订单列表查询接口调整">
      <option name="closed" value="true" />
      <created>1753335802386</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753335802386</updated>
    </task>
    <task id="LOCAL-00028" summary="送样优化调整">
      <option name="closed" value="true" />
      <created>1753370051020</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753370051020</updated>
    </task>
    <task id="LOCAL-00029" summary="送样优化调整">
      <option name="closed" value="true" />
      <created>1753412874113</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753412874113</updated>
    </task>
    <task id="LOCAL-00030" summary="答交送样接口调整">
      <option name="closed" value="true" />
      <created>1753415518055</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753415518055</updated>
    </task>
    <task id="LOCAL-00031" summary="送样优化调整">
      <option name="closed" value="true" />
      <created>1753536082943</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753536082943</updated>
    </task>
    <task id="LOCAL-00032" summary="送样优化调整">
      <option name="closed" value="true" />
      <created>1753622714939</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753622714939</updated>
    </task>
    <task id="LOCAL-00033" summary="送样优化调整">
      <option name="closed" value="true" />
      <created>1753755845436</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753755845436</updated>
    </task>
    <task id="LOCAL-00034" summary="采购订单列表调整（供应商）">
      <option name="closed" value="true" />
      <created>1753769636451</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753769636451</updated>
    </task>
    <task id="LOCAL-00035" summary="增加PLM同步送样需求单结案数据接口">
      <option name="closed" value="true" />
      <created>1753782688907</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753782688907</updated>
    </task>
    <task id="LOCAL-00036" summary="PLM结案回传接口调整">
      <option name="closed" value="true" />
      <created>1753853653860</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753853653860</updated>
    </task>
    <task id="LOCAL-00037" summary="增加送样需求单本地结案接口">
      <option name="closed" value="true" />
      <created>1753931864165</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753931864165</updated>
    </task>
    <task id="LOCAL-00038" summary="采购订单列表分页查询条件调整">
      <option name="closed" value="true" />
      <created>1753944757609</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1753944757609</updated>
    </task>
    <task id="LOCAL-00039" summary="送样需求单结案接口调整">
      <option name="closed" value="true" />
      <created>1753957266681</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1753957266681</updated>
    </task>
    <task id="LOCAL-00040" summary="代码问题优化调整">
      <option name="closed" value="true" />
      <created>1754042204836</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1754042204836</updated>
    </task>
    <task id="LOCAL-00041" summary="供应商确认采购订单和回复送样通知单通知采购员">
      <option name="closed" value="true" />
      <created>1754278428623</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1754278428623</updated>
    </task>
    <task id="LOCAL-00042" summary="送样通知单增加字段、增加查看图纸，采购订单应答列表行增加条码打印">
      <option name="closed" value="true" />
      <created>1754293136778</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1754293136778</updated>
    </task>
    <option name="localTasksCounter" value="43" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/gve_dev" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/gve_dev_lf" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="gve_dev_lf" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/gve_xpord" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="采购订单确认（供应方）" />
    <MESSAGE value="采购订单确认接口调整" />
    <MESSAGE value="送样通知单新增单据类型字段" />
    <MESSAGE value="送样需求单新增单据类型查询条件" />
    <MESSAGE value="送样需求单更新接口调整" />
    <MESSAGE value="送样通知单新增查询条件" />
    <MESSAGE value="新增库存填报单接口" />
    <MESSAGE value="库存填报单导入功能" />
    <MESSAGE value="库存填报单接口调整" />
    <MESSAGE value="送样需求、通知单优化调整" />
    <MESSAGE value="采购订单查询接口调整&#10;送样需求单、通知单优化调整" />
    <MESSAGE value="新建送样通知单保存接口调整 采购订单列表查询接口调整" />
    <MESSAGE value="答交送样接口调整" />
    <MESSAGE value="送样优化调整" />
    <MESSAGE value="采购订单列表调整" />
    <MESSAGE value="采购订单列表调整" />
    <MESSAGE value="增加PLM同步送样需求单结案数据接口" />
    <MESSAGE value="PLM结案回传接口调整" />
    <MESSAGE value="增加送样需求单本地结案接口" />
    <MESSAGE value="采购订单列表分页查询条件调整" />
    <MESSAGE value="送样需求单结案接口调整" />
    <MESSAGE value="代码问题优化调整" />
    <MESSAGE value="供应商确认采购订单和回复送样通知单通知采购员" />
    <MESSAGE value="送样通知单增加申请人字段" />
    <MESSAGE value="送样通知单增加申请人字段" />
    <option name="LAST_COMMIT_MESSAGE" value="送样通知单增加申请人字段" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>