{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1754404317743}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport echarts from 'echarts'\r\nimport PanelGroup from './components/tenantPanelGroup'\r\nimport VendorPanelGroup from './components/vendorPanelGroup'\r\nimport LineChart from './components/LineChart'\r\nimport CountTo from 'vue-count-to'\r\nimport {queryLastMailList} from '@/api/base/mail';\r\nimport {queryMsgByNotice} from '@/api/base/notice';\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: 'DashboardAdmin',\r\n  components: {\r\n    PanelGroup,\r\n    LineChart,\r\n    CountTo,\r\n    VendorPanelGroup\r\n  },\r\n  data() {\r\n    return {\r\n      value1: '',\r\n      charts: '',\r\n      total:0,\r\n      queryParam:{\r\n        page:1,\r\n        limit:20,\r\n      },\r\n      userInfo:store.getters.userInfo,/*获取当前登录的用户信息*/\r\n      opinionData: [\"3\", \"2\", \"4\", \"4\", \"5\"],\r\n      massageList: [],//消息列表数据\r\n      noticeMsgList: [],//消息列表数据\r\n    }\r\n  },\r\n  created() {\r\n    this.userInfo = store.getters.userInfo;\r\n    this.initMassageList();\r\n    if (this.userInfo.entType != 1){\r\n      this.initNoticeMsgList();\r\n    }\r\n  },\r\n  methods: {\r\n    //初始化查询消息列表数据\r\n    initMassageList(){\r\n      queryLastMailList(this.queryParam).then(res => {\r\n        this.massageList = res.data.list;\r\n        this.total = res.data.totalCount;\r\n      }).catch(res => {\r\n\r\n      })\r\n    },\r\n    initNoticeMsgList(){\r\n      queryMsgByNotice({}).then(res => {\r\n        this.noticeMsgList = res.data\r\n      }).catch(res => {\r\n\r\n      })\r\n    },\r\n    // //点击打开表单\r\n    // openForm(data){\r\n    //   let routerUrl = data.url;\r\n    //   this.$router.push({path: routerUrl})\r\n    // }\r\n    //点击打开表单\r\n    openForm(data){\r\n      let routerUrl = data.url;\r\n      if (routerUrl.includes('?')) {\r\n        const [path, queryString] = routerUrl.split('?');\r\n        const query = {};\r\n        // 解析查询参数\r\n        if (queryString) {\r\n          queryString.split('&').forEach(param => {\r\n            const [key, value] = param.split('=');\r\n            if (key && value) {\r\n              query[key] = decodeURIComponent(value);\r\n            }\r\n          });\r\n        }\r\n        // const fullPath = path.startsWith('/') ? path : `/${path}`;\r\n        // if (fullPath === '/base/sampleDemand/tenant' && query.id && !query.detailVisible) {\r\n        //   query.detailVisible = true;\r\n        //   query.t = Date.now();\r\n        // }\r\n        this.$router.push({\r\n          path: fullPath,\r\n          query: query\r\n        });\r\n      } else {\r\n        const fullPath = routerUrl.startsWith('/') ? routerUrl : `/${routerUrl}`;\r\n        this.$router.push({path: fullPath});\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}