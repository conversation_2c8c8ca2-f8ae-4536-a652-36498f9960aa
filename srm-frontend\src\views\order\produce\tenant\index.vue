<template>
  <div class="DIAN-common-layout">
    <div class="DIAN-common-layout-center">

      <!-- 搜索框 -->
      <el-row class="DIAN-common-search-box" :gutter="24">
        <el-form @submit.native.prevent>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.purNo" placeholder="请输入采购订单号" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.vendor" placeholder="请输入供应商编码/名称" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-select v-model="queryParam.itemStat" placeholder="请选择单据状态" clearable>
                <el-option
                  :key="item.key"
                  :label="item.value"
                  :value="parseInt(item.key)"
                  v-for="item in orderStatOptions"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-input v-model.trim="queryParam.goods" placeholder="请输入物料编码/名称/型号" clearable/>
          </el-col>
          <template v-if="showAll">
          <el-col :span="4">
            <el-form-item>
              <el-input v-model.trim="queryParam.purName" placeholder="请输入采购员名称" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="orderDates"
                  type="daterange"
                  placeholder="请选择订单日期"
                  range-separator="至"
                  start-placeholder="（订单）开始日期"
                  end-placeholder="（订单）结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="search()">{{$t('common.search')}}</el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()">{{$t('common.reset')}}</el-button>
              <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true"
                         v-if="!showAll">展开
              </el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else>
                收起
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <!-- body -->
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <!-- 表头工具栏 -->
        <div class="DIAN-common-head">
          <div>
            <template>
<!--              <el-dropdown>-->
<!--                <el-button type="primary" style="margin-right: 10px" icon="el-icon-top" v-has-per="'order:pur:import'">{{ $t('common.importBtn') }}</el-button>-->
<!--                <el-dropdown-menu slot="dropdown">-->
<!--                  <el-dropdown-item icon="el-icon-download" @click.native="downTemplate" v-has-per="'order:pur:exportNew'">下载导入模板</el-dropdown-item>-->
<!--                  <el-dropdown-item icon="el-icon-upload2" @click.native="uploadInitModel" v-has-per="'order:pur:importNew'">导入</el-dropdown-item>-->
<!--                </el-dropdown-menu>-->
<!--              </el-dropdown>-->
              <el-dropdown>
                <el-button type="primary" style="margin-right: 10px" icon="el-icon-top" v-has-per="'order:pur:updateInitOrder'">{{ $t('common.importBtn') }}</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-download" @click.native="downLoadTemplate" v-has-per="'order:pur:updateInitOrder'">下载导入模板</el-dropdown-item>
                  <el-dropdown-item icon="el-icon-upload2" @click.native="importUpdateInitOrder" v-has-per="'order:pur:updateInitOrder'">导入</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </div>
          <div class="DIAN-common-head-right">
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false" @click="search()" />
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>

        <!-- 表格 -->
        <d-table ref="listTable" v-loading="listLoading" :data="list" hasC>

          <!-- 展开行 -->
          <!-- <el-table-column type="expand">
            <template slot-scope="scope">
              <div style="min-height: 0">
                <el-table height="100%" :data="scope.row.materialList" size='mini'>
                  <el-table-column prop="useMaterialNo" label="委外用料清单号" align="center" width="150">
                    <template slot-scope="scope">
                      {{ scope.row.useMaterialNo + '/' + scope.row.useMaterialSeq}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="outOrderNo" label="委外订单号" align="center" width="150">
                    <template slot-scope="scope">
                      {{ scope.row.outOrderNo + '/' + scope.row.outOrderSeq}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="goodsErpCode" label="子件料号" align="center" width="120" />
                  <el-table-column prop="goodsName" label="子件名称" align="center" width="120" />
                  <el-table-column prop="goodsModel" label="子件规格型号" align="center" width="250" />
                  <el-table-column prop="issuMethod" label="发料方式" align="center" width="100">
                    <template slot-scope="scope">
                      {{ scope.row.issuMethod | commonEnumsTurn('order.IssuMethodEnum')}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="subItemType" label="子项类型" align="center" width="100">
                    <template slot-scope="scope">
                      {{ scope.row.issuMethod | commonEnumsTurn('order.SubItemTypeEnum')}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="uomCode" label="单位" align="center" width="80"/>
                  <el-table-column prop="numerator" label="分子" align="center" width="100"/>
                  <el-table-column prop="denominator" label="分母" align="center" width="100"/>
                  <el-table-column prop="sentQty" label="应发数" align="center" width="100"/>
                  <el-table-column prop="actRecQty" label="实发数" align="center" width="100"/>
                  <el-table-column prop="unclaimedQty" label="未发数" align="center" width="100"/>
                  <el-table-column prop="returnQty" label="退料数" align="center" width="100"/>
                  <el-table-column prop="availableStock" label="可用库存数" align="center" width="100"/>
                  <el-table-column prop="remark" label="备注" />
                </el-table>
              </div>
            </template>
          </el-table-column> -->


          <el-table-column prop="deptName" label="采购组织" align="center" width="150"/>
          <el-table-column prop="purNo" label="采购订单号" align="center" width="150"/>
          <el-table-column prop="purName" label="采购员" align="center" show-overflow-tooltip width="90"/>
          <el-table-column prop="vendorCode" label="供应商编码" align="center" width="150"/>
          <el-table-column prop="vendorName" label="供应商名称" align="center" show-overflow-tooltip width="180"/>
          <el-table-column prop="orderType" label="订单类型" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{scope.row.orderType | commonEnumsTurn("common.JinDieOrderTypeEnum")}}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="stat" label="单据状态" align="center" width="90">
            <template slot-scope="scope">
              <span>{{scope.row.stat | commonEnumsTurn("order.PurStatEnum")}}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="orderDate" label="订单日期" align="center" show-overflow-tooltip width="120">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.orderDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="publishDate" label="发布时间" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="currencyName" label="货币名称" align="center" show-overflow-tooltip width="120"/>
          <el-table-column prop="totalAmount" label="含税总金额" align="center" show-overflow-tooltip width="150" v-if="$dian.hasPerBtnP('order:pur:lookPrice')">
            <template slot-scope="scope">
              <span>{{scope.row.orderNum * scope.row.gstPrice}}</span>
            </template>
          </el-table-column>

          <!-- 明细行数据 -->
          <el-table-column prop="itemStat" label="订单状态" width="90">
            <template slot-scope="scope">
              <span>{{ scope.row.itemStat | commonEnumsTurn("order.PurlineStatEnum") }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isClose" label="是否关闭" show-tooltip-when-overflow width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.isClose === 1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column prop="erpChangeType" label="变更类型" show-tooltip-when-overflow width="100"/>
          <el-table-column prop="goodsErpCode" label="物料编码" show-tooltip-when-overflow width="150"/>
          <el-table-column prop="goodsName" label="物料名称" show-tooltip-when-overflow width="150"/>
          <el-table-column prop="goodsModel" label="规格型号" show-tooltip-when-overflow width="150"/>
          <el-table-column prop="deliveryStatus" label="送货状态" width="100"/>
          <el-table-column prop="orderNum" label="订单数量" width="100"/>
          <!-- <el-table-column prop="erpOutPickQty" label="委外领料齐套数" width="120"/> -->
          <!-- <el-table-column prop="matchedPlanNum" label="已下计划数量" width="100"/> -->
          <el-table-column prop="makeNum" label="已制单数量" width="100"/>
          <el-table-column prop="unMakeNum" label="未制单数量" width="100"/>
          <el-table-column prop="fixNum" label="已送数量" width="100"/>
          <el-table-column prop="waitNum" label="待送数量" width="100"/>
          <el-table-column prop="receiveNum" label="暂收数量" width="100"/>
          <el-table-column prop="refundNum" label="暂退补料数量" width="100"/>
          <el-table-column prop="refDedNum" label="暂退扣款数量" width="100"/>
          <el-table-column prop="erpMasterNum" label="入库数量" width="100"/>
          <el-table-column prop="erpRejectNum" label="退货补料数量" width="100"/>
          <el-table-column prop="retDedNum" label="退货扣款数量" width="100"/>
          <el-table-column prop="uomName" label="单位" width="100"/>
          <el-table-column prop="rateName" label="税率" width="100"/>
          <el-table-column prop="deliveryDate" label="交货日期" width="150"/>
  <!--    <el-table-column prop="replyDate" label="答交日期" width="150"/>-->
  <!--    <el-table-column prop="confirmDate" label="确认日期" width="150"/>-->
          <el-table-column prop="taxPrice" label="不含税单价" width="100" v-if="$dian.hasPerBtnP('order:pur:lookPrice')"/>
          <el-table-column prop="gstPrice" label="含税单价" width="100" v-if="$dian.hasPerBtnP('order:pur:lookPrice')"/>

          <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip min-width="180"/>
          <el-table-column label="操作" width="80" fixed="right" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="addEditOrderHandle(scope.row.id)" v-has-per="'order:pur:info'"> {{ $t('common.lookBtn')}} </el-button>
            </template>
          </el-table-column>
        </d-table>
        <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initData"/>
      </div>

      <!-- FORM表单 -->
      <Form ref="form" v-show="formVisible" @callRefreshList="closeForm"></Form>
      <MasterForm ref="master" v-show="masterFormVisible" @callRefreshList="closeForm"></MasterForm>
      <d-export title="导入模板下载" ref="tempDownload" :exports="false"></d-export>
      <d-import ref="upload" @callData="importHandle()" ></d-import>
    </div>
  </div>
</template>

<script>

import {dFlowMixin} from "@dian/dian-ui-vue";
import Form from './Form';
import MasterForm from './masterForm';
import store from "@/store";
import {getTenantSaleOrderList} from "@/api/order/sale";
import {getSubCompanyId} from "@/utils/auth";
import dian from '@/utils/dian'

export default {
  //加载底层公有组件
  mixins: [dFlowMixin],
  name: "order-produce-tenant",
  components: {
    Form,MasterForm
  },
  data() {
    return {
      bsartTypeOptions: store.getters.commonEnums['order.BsartTypeEnum'], // 订单类型
      // orderStatOptions: store.getters.commonEnums['order.PurStatEnum'],
      orderStatOptions:[
        {key:1,value:'待确认'},
        {key:4,value:'已确认'},
      ],
      queryParam: {
        page: 1,
        limit: 20,
        keyword : '', // 订单号/供应商编码|名称
        orderDate : '', // 订单日期
        deptId:'',//采购组织id
        deptName : '', // 采购类型
        stat: ''  , // 单据状态
        purName:'',//采购员名称
        orderNo:'',//采购员名称
        vendorNo:'',//采购员名称
      },
      formVisible: false,
      listLoading: false,
      btnLoading: false,
      masterFormVisible:false,
      subCompanyInfo:getSubCompanyId()||{},//头部选择的采购组织信息
      list: [],
      total: 0,
      showAll: false,
      orderDates: [], // 订单日期区间选择器
    }
  },
  filters: {
    dateFormat(time) {
      if (!time) {
        return ''
      }
      let date = new Date(time)
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return year + "-" + month + "-" + day;
    }
  },
  watch: {
    $route: {
      handler (to, from) {
        debugger
        if (to.path === '/order/produce/tenant' && null != to.query.id) {
          this.addEditOrderHandle(to.query.id);
        }
      },
      immediate: true
    }
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      this.listLoading = true;
      if (this.orderDates && this.orderDates.length === 2) {
        try {
          const startDate = this.$dian.dateFormat(this.orderDates[0], 'YYYY-MM-DD');
          const endDate = this.$dian.dateFormat(this.orderDates[1], 'YYYY-MM-DD');
          this.queryParam.orderDate = startDate + " 至 " + endDate;
        } catch (error) {
          console.error('日期格式化错误:', error);
          this.queryParam.orderDate = '';
        }
      } else {
        this.queryParam.orderDate = '';
      }
      let subCompanyInfoData = dian.storageGet('subCompanyInfo');
      if (subCompanyInfoData){
        this.queryParam.deptId = subCompanyInfoData.id;
      }
      getTenantSaleOrderList(this.queryParam).then(res => {
        this.total = res.page.totalCount;
        this.list = res.page.list;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      })
    },
    // 新增|编辑 项目报备
    addEditOrderHandle(id) {
      this.formVisible = true;
      this.$nextTick(() => {
        this.$refs.form.init(id);
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedDatas = selection.map(item => item)
      //获取所有选中项数组的长度
      this.selectedNum = selection.length
    },
    // 导入模板下载
    downTemplate() {
      this.downExports = false;
      this.$refs.export.init('api/order/pur/exportNew', '采购订单');
    },
    // 订单导入
    uploadInitModel() {
      let url = "/api/order/pur/importNew";
      this.$refs.upload.init(url);
    },
    //下载模板
    downLoadTemplate(){
      this.downExports = false;
      this.$refs.tempDownload.init('api/order/pur/downloadUpdateInitOrderDataVO', '切换上线后在途数据统计');
    },
    //导入更新初始化订单数据
    importUpdateInitOrder(){
      // let url = "/api/order/pur/importNew";
      // this.$refs.upload.init(url);
    },
    importHandle(){
      this.search();
    },
    // 搜索方法，并返回到第一页
    search() {
      this.queryParam.page = 1;
      this.initData();
    },
    // 重置方法
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.orderDates = [];
      this.search();
    },
    // Form表单关闭时回调方法
    closeForm() {
      this.formVisible = false;
      this.masterFormVisible = false;
      this.initData();
    },
    // 打开创建退货单表单页面
    openMasterForm(){
      this.masterFormVisible = true;
      this.$nextTick(() => {
        this.$refs.master.init();
      })
    }
  }
}
</script>

<style scoped>

</style>
