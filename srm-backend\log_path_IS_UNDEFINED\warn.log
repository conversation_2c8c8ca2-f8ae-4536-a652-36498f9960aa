2025-08-06 09:06:55.545 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-06 09:06:55.811 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-06 09:06:55.930 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-06 09:06:56.061 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-06 09:06:56.155 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-06 09:06:56.296 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-06 09:06:56.359 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-06 09:06:56.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-06 09:06:56.656 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-06 09:06:56.734 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-06 09:06:56.796 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-06 09:06:56.885 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-06 09:06:56.959 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-06 09:06:57.033 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-06 09:06:57.402 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-06 09:06:57.496 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-06 09:06:57.582 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-06 09:06:57.677 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-06 09:06:57.744 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-06 09:06:57.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-06 09:06:58.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-06 09:06:58.169 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-06 09:06:58.388 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-06 09:06:58.584 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-06 09:06:58.961 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-06 09:06:59.113 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-06 09:06:59.411 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-06 09:07:00.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-06 09:07:00.494 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-06 09:07:00.630 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-06 09:07:00.975 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-06 09:07:01.187 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-06 09:07:01.300 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-06 09:07:01.408 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-06 09:07:01.555 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-06 09:07:01.662 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-06 09:07:02.981 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-06 09:07:05.607 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-06 09:07:07.251 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-06 09:07:09.338 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-06 09:07:10.455 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-06 09:07:11.482 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-06 09:07:14.576 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-06 09:07:15.684 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-06 09:07:16.105 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-06 09:07:16.785 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-06 09:07:17.128 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-06 09:07:17.313 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-06 09:07:17.634 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-06 09:07:18.030 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-06 09:07:18.243 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-06 09:07:18.336 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-06 09:07:18.420 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-06 09:07:18.692 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-06 09:07:18.785 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-06 09:07:18.860 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-06 09:07:19.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-06 09:07:19.112 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-06 09:07:19.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-06 09:07:19.950 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-06 09:07:20.769 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-06 09:07:22.446 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-06 09:07:25.113 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-06 09:07:25.761 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-06 09:07:26.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-06 09:07:26.505 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-06 09:07:26.588 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-06 09:07:27.048 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-06 09:07:27.185 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-06 09:07:27.518 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-06 09:07:28.013 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-06 09:07:28.666 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-06 09:07:29.830 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-06 09:07:30.161 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-06 09:07:30.368 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-06 09:07:30.601 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-06 09:07:30.824 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-06 09:07:30.989 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-06 09:07:31.101 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-06 09:07:31.591 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-06 09:07:32.361 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-06 09:07:32.799 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-06 09:07:33.098 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-06 09:07:34.439 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-06 09:07:34.977 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-06 09:07:35.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-06 09:07:35.410 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-06 09:07:35.541 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-06 09:07:36.017 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-06 09:07:36.303 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-06 09:07:36.475 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-06 09:07:36.697 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-06 09:07:36.957 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-06 09:07:37.103 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-06 09:07:37.248 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-06 09:07:37.403 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-06 09:07:37.517 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-06 09:07:37.593 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-06 09:07:37.662 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-06 09:07:37.779 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-06 09:07:37.869 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-06 09:07:37.940 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-06 09:07:38.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-06 09:07:38.118 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-06 09:07:38.298 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-06 09:07:38.445 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-06 09:07:38.531 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-06 09:07:38.629 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-06 09:07:38.712 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-06 09:07:38.786 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-06 09:07:38.859 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-06 09:07:38.939 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-06 09:07:39.022 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-06 09:07:39.097 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-06 09:07:39.150 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-06 09:07:39.473 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-06 09:07:39.818 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-06 09:07:40.012 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-06 09:07:40.131 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-06 09:07:40.237 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-06 09:07:40.325 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-06 09:07:40.428 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-06 09:07:40.526 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-06 09:07:40.669 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-06 09:07:40.756 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-06 09:07:40.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-06 09:07:40.945 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-06 09:07:41.050 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-06 09:07:41.144 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-06 09:07:41.289 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-06 09:07:41.354 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-06 09:07:41.420 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-06 09:07:41.479 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-06 09:07:41.560 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-06 09:07:41.647 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-06 09:07:41.717 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-06 09:07:41.775 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-06 09:07:41.843 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-06 09:07:41.920 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-06 09:07:41.987 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-06 09:07:42.191 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-06 09:07:42.320 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-06 09:07:42.437 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-06 09:07:42.523 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-06 09:07:42.598 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-06 09:07:42.672 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-06 09:07:42.730 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-06 09:07:42.801 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-06 09:07:42.867 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-06 09:07:42.948 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-06 09:07:43.023 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-06 09:07:43.100 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-06 09:07:43.211 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-06 09:07:43.263 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-06 09:07:43.335 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-06 09:07:43.402 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-06 09:07:43.476 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-06 09:07:43.540 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-06 09:07:43.607 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-06 09:07:43.691 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-06 09:07:43.757 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-06 09:07:43.815 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-06 09:07:43.891 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-06 09:07:43.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-06 09:07:44.064 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-06 09:07:44.131 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-06 09:07:44.182 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-06 09:07:44.276 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-06 09:07:44.337 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-06 09:07:44.475 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-06 09:07:44.548 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-06 09:07:44.628 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-06 09:07:44.693 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-06 09:07:44.763 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-06 09:07:44.836 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-06 09:07:44.892 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-06 09:07:44.950 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-06 09:07:45.022 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-06 09:07:45.088 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-06 09:07:45.148 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-06 09:07:45.220 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-06 09:07:45.287 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-06 09:07:45.338 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-06 09:07:45.410 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-06 09:07:45.482 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-06 09:07:45.554 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-06 09:07:45.651 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-06 09:07:45.708 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-06 09:07:45.762 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-06 09:07:45.820 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-06 09:07:45.865 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-06 09:07:45.908 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-06 09:07:45.957 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-06 09:07:46.052 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-06 09:07:46.105 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-06 09:07:46.179 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-06 09:07:46.326 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-06 09:07:46.358 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-06 09:07:47.729 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-06 09:07:47.767 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-06 09:07:47.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-06 09:07:47.856 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-06 09:07:47.896 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-06 09:07:47.952 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-06 09:07:48.003 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-06 09:07:48.067 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-06 09:07:48.103 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-06 09:07:48.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-06 09:07:48.192 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-06 09:07:48.235 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-06 09:07:48.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-06 09:07:48.322 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-06 09:07:48.358 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-06 09:07:48.435 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-06 09:07:48.479 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-06 09:07:48.519 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-06 09:07:48.656 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-06 09:07:48.711 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-06 09:07:48.776 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-06 09:07:48.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-06 09:07:48.989 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-06 09:07:49.123 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-06 09:07:49.178 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-06 09:07:49.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-06 09:07:49.303 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-06 09:07:50.061 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-06 09:07:50.563 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-06 09:07:52.766 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-06 09:09:02.443 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-06 09:09:05.125 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40017 mb of usable space. - resetting to maximum available disk space: 40017 mb
2025-08-06 09:09:50.949 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-06 10:03:43.640 [DefaultMessageListenerContainer-1] WARN  o.s.jms.listener.DefaultMessageListenerContainer - Setup of JMS message listener invoker failed for destination 'flow.msg.queue' - trying to recover. Cause: The Session is closed
2025-08-06 10:03:43.825 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-06 10:06:45.717 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-06 10:06:45.976 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-06 10:06:46.080 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-06 10:06:46.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-06 10:06:46.310 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-06 10:06:46.437 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-06 10:06:46.508 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-06 10:06:46.698 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-06 10:06:46.819 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-06 10:06:46.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-06 10:06:47.000 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-06 10:06:47.045 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-06 10:06:47.108 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-06 10:06:47.162 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-06 10:06:47.222 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-06 10:06:47.300 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-06 10:06:47.373 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-06 10:06:47.424 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-06 10:06:47.474 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-06 10:06:47.528 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-06 10:06:47.585 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-06 10:06:47.639 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-06 10:06:47.712 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-06 10:06:47.764 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-06 10:06:47.851 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-06 10:06:47.904 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-06 10:06:47.954 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-06 10:06:48.007 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-06 10:06:48.112 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-06 10:06:48.168 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-06 10:06:48.234 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-06 10:06:48.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-06 10:06:48.384 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-06 10:06:48.469 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-06 10:06:48.524 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-06 10:06:48.572 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-06 10:06:48.635 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-06 10:06:48.687 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-06 10:06:48.731 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-06 10:06:48.783 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-06 10:06:48.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-06 10:06:48.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-06 10:06:49.667 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-06 10:06:50.000 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-06 10:06:50.062 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-06 10:06:50.292 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-06 10:06:50.417 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-06 10:06:50.649 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-06 10:06:50.902 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-06 10:06:51.003 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-06 10:06:51.203 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-06 10:06:51.388 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-06 10:06:51.718 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-06 10:06:52.168 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-06 10:06:52.365 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-06 10:06:52.430 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-06 10:06:52.488 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-06 10:06:52.540 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-06 10:06:52.601 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-06 10:06:52.668 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-06 10:06:52.792 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-06 10:06:52.859 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-06 10:06:53.213 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-06 10:06:53.312 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-06 10:06:53.374 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-06 10:06:53.468 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-06 10:06:53.567 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-06 10:06:53.679 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-06 10:06:53.976 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-06 10:06:54.477 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-06 10:06:54.552 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-06 10:06:55.075 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-06 10:06:55.221 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-06 10:06:55.520 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-06 10:06:56.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-06 10:06:56.645 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-06 10:06:56.871 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-06 10:06:57.060 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-06 10:06:57.144 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-06 10:06:57.419 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-06 10:06:58.757 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-06 10:06:59.019 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-06 10:06:59.235 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-06 10:07:00.568 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-06 10:07:00.806 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-06 10:07:00.919 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-06 10:07:00.998 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-06 10:07:01.062 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-06 10:07:01.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-06 10:07:01.494 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-06 10:07:01.586 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-06 10:07:01.649 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-06 10:07:01.882 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-06 10:07:02.082 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-06 10:07:02.174 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-06 10:07:02.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-06 10:07:02.366 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-06 10:07:02.419 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-06 10:07:02.465 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-06 10:07:02.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-06 10:07:02.602 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-06 10:07:02.658 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-06 10:07:02.701 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-06 10:07:02.745 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-06 10:07:02.873 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-06 10:07:02.990 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-06 10:07:03.081 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-06 10:07:03.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-06 10:07:03.175 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-06 10:07:03.207 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-06 10:07:03.248 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-06 10:07:03.301 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-06 10:07:03.345 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-06 10:07:03.388 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-06 10:07:03.429 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-06 10:07:03.485 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-06 10:07:03.534 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-06 10:07:03.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-06 10:07:03.630 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-06 10:07:03.666 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-06 10:07:03.708 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-06 10:07:03.752 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-06 10:07:03.796 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-06 10:07:03.855 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-06 10:07:03.898 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-06 10:07:03.940 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-06 10:07:03.979 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-06 10:07:04.021 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-06 10:07:04.054 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-06 10:07:04.129 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-06 10:07:04.180 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-06 10:07:04.259 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-06 10:07:04.301 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-06 10:07:04.346 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-06 10:07:04.381 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-06 10:07:04.426 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-06 10:07:04.467 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-06 10:07:04.498 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-06 10:07:04.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-06 10:07:04.584 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-06 10:07:04.731 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-06 10:07:04.796 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-06 10:07:04.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-06 10:07:04.899 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-06 10:07:04.944 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-06 10:07:04.986 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-06 10:07:05.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-06 10:07:05.072 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-06 10:07:05.114 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-06 10:07:05.153 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-06 10:07:05.187 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-06 10:07:05.234 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-06 10:07:05.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-06 10:07:05.328 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-06 10:07:05.396 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-06 10:07:05.445 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-06 10:07:05.509 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-06 10:07:05.559 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-06 10:07:05.609 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-06 10:07:05.677 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-06 10:07:05.723 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-06 10:07:05.786 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-06 10:07:05.837 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-06 10:07:05.905 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-06 10:07:05.965 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-06 10:07:06.027 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-06 10:07:06.079 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-06 10:07:06.138 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-06 10:07:06.182 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-06 10:07:06.291 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-06 10:07:06.335 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-06 10:07:06.381 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-06 10:07:06.439 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-06 10:07:06.491 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-06 10:07:06.528 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-06 10:07:06.572 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-06 10:07:06.606 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-06 10:07:06.649 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-06 10:07:06.683 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-06 10:07:06.717 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-06 10:07:06.780 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-06 10:07:06.823 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-06 10:07:06.870 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-06 10:07:06.928 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-06 10:07:06.987 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-06 10:07:07.043 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-06 10:07:07.128 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-06 10:07:07.179 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-06 10:07:07.222 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-06 10:07:07.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-06 10:07:07.334 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-06 10:07:07.374 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-06 10:07:07.419 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-06 10:07:07.470 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-06 10:07:07.513 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-06 10:07:07.574 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-06 10:07:07.692 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-06 10:07:07.722 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-06 10:07:08.885 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-06 10:07:08.920 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-06 10:07:08.962 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-06 10:07:09.001 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-06 10:07:09.038 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-06 10:07:09.077 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-06 10:07:09.115 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-06 10:07:09.174 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-06 10:07:09.206 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-06 10:07:09.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-06 10:07:09.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-06 10:07:09.320 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-06 10:07:09.365 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-06 10:07:09.403 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-06 10:07:09.436 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-06 10:07:09.496 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-06 10:07:09.529 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-06 10:07:09.562 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-06 10:07:09.629 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-06 10:07:09.667 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-06 10:07:09.718 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-06 10:07:09.813 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-06 10:07:09.912 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-06 10:07:10.040 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-06 10:07:10.096 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-06 10:07:10.147 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-06 10:07:10.193 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-06 10:07:10.239 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-06 10:07:10.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-06 10:07:10.387 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-06 10:08:02.802 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-06 10:08:06.417 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40015 mb of usable space. - resetting to maximum available disk space: 40015 mb
2025-08-06 10:08:48.004 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-06 11:03:35.335 [DefaultMessageListenerContainer-1] WARN  o.s.jms.listener.DefaultMessageListenerContainer - Setup of JMS message listener invoker failed for destination 'flow.msg.queue' - trying to recover. Cause: The Session is closed
2025-08-06 11:03:35.368 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-06 11:03:38.142 [SpringContextShutdownHook] WARN  com.alibaba.druid.pool.DruidDataSource - close connection error
com.mysql.jdbc.exceptions.jdbc4.MySQLNonTransientConnectionException: Communications link failure during rollback(). Transaction resolution unknown.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:403)
	at com.mysql.jdbc.Util.getInstance(Util.java:386)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:919)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:898)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:887)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:861)
	at com.mysql.jdbc.ConnectionImpl.rollback(ConnectionImpl.java:4541)
	at com.mysql.jdbc.ConnectionImpl.realClose(ConnectionImpl.java:4168)
	at com.mysql.jdbc.ConnectionImpl.close(ConnectionImpl.java:1462)
	at com.p6spy.engine.wrapper.ConnectionWrapper.close(ConnectionWrapper.java:236)
	at com.alibaba.druid.filter.FilterChainImpl.connection_close(FilterChainImpl.java:186)
	at com.alibaba.druid.filter.FilterAdapter.connection_close(FilterAdapter.java:777)
	at com.alibaba.druid.filter.FilterChainImpl.connection_close(FilterChainImpl.java:181)
	at com.alibaba.druid.filter.stat.StatFilter.connection_close(StatFilter.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_close(FilterChainImpl.java:181)
	at com.alibaba.druid.filter.FilterAdapter.connection_close(FilterAdapter.java:777)
	at com.alibaba.druid.filter.logging.LogFilter.connection_close(LogFilter.java:450)
	at com.alibaba.druid.filter.FilterChainImpl.connection_close(FilterChainImpl.java:181)
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.close(ConnectionProxyImpl.java:114)
	at com.alibaba.druid.pool.DruidDataSource.close(DruidDataSource.java:2074)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1075)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1068)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
2025-08-06 11:11:13.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-06 11:11:13.235 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-06 11:11:13.313 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-06 11:11:13.374 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-06 11:11:13.429 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-06 11:11:13.525 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-06 11:11:13.573 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-06 11:11:13.698 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-06 11:11:13.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-06 11:11:13.782 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-06 11:11:13.817 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-06 11:11:13.850 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-06 11:11:13.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-06 11:11:13.915 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-06 11:11:13.953 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-06 11:11:14.006 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-06 11:11:14.042 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-06 11:11:14.076 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-06 11:11:14.109 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-06 11:11:14.142 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-06 11:11:14.184 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-06 11:11:14.230 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-06 11:11:14.262 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-06 11:11:14.296 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-06 11:11:14.374 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-06 11:11:14.414 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-06 11:11:14.456 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-06 11:11:14.495 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-06 11:11:14.571 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-06 11:11:14.608 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-06 11:11:14.655 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-06 11:11:14.693 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-06 11:11:14.727 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-06 11:11:14.758 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-06 11:11:14.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-06 11:11:14.828 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-06 11:11:14.860 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-06 11:11:14.906 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-06 11:11:14.945 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-06 11:11:14.991 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-06 11:11:15.033 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-06 11:11:15.075 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-06 11:11:15.122 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-06 11:11:15.155 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-06 11:11:15.193 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-06 11:11:15.305 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-06 11:11:15.373 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-06 11:11:15.406 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-06 11:11:15.444 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-06 11:11:15.476 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-06 11:11:15.509 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-06 11:11:15.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-06 11:11:15.610 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-06 11:11:15.698 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-06 11:11:15.742 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-06 11:11:15.777 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-06 11:11:15.812 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-06 11:11:15.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-06 11:11:15.881 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-06 11:11:15.918 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-06 11:11:15.955 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-06 11:11:15.994 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-06 11:11:16.060 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-06 11:11:16.095 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-06 11:11:16.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-06 11:11:16.170 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-06 11:11:16.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-06 11:11:16.246 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-06 11:11:16.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-06 11:11:16.316 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-06 11:11:16.348 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-06 11:11:16.383 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-06 11:11:16.423 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-06 11:11:16.461 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-06 11:11:16.499 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-06 11:11:16.529 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-06 11:11:16.557 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-06 11:11:16.592 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-06 11:11:16.639 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-06 11:11:16.738 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-06 11:11:16.774 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-06 11:11:16.866 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-06 11:11:16.904 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-06 11:11:17.020 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-06 11:11:17.139 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-06 11:11:17.220 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-06 11:11:17.266 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-06 11:11:17.313 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-06 11:11:17.485 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-06 11:11:17.560 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-06 11:11:17.612 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-06 11:11:17.652 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-06 11:11:17.779 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-06 11:11:17.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-06 11:11:17.926 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-06 11:11:18.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-06 11:11:18.081 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-06 11:11:18.133 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-06 11:11:18.223 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-06 11:11:18.328 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-06 11:11:18.386 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-06 11:11:18.418 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-06 11:11:18.485 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-06 11:11:18.513 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-06 11:11:18.627 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-06 11:11:18.724 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-06 11:11:18.764 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-06 11:11:18.794 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-06 11:11:18.834 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-06 11:11:18.870 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-06 11:11:18.901 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-06 11:11:18.945 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-06 11:11:18.976 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-06 11:11:19.006 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-06 11:11:19.036 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-06 11:11:19.086 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-06 11:11:19.124 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-06 11:11:19.160 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-06 11:11:19.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-06 11:11:19.229 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-06 11:11:19.261 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-06 11:11:19.298 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-06 11:11:19.330 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-06 11:11:19.369 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-06 11:11:19.401 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-06 11:11:19.429 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-06 11:11:19.458 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-06 11:11:19.493 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-06 11:11:19.528 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-06 11:11:19.592 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-06 11:11:19.632 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-06 11:11:19.666 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-06 11:11:19.695 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-06 11:11:19.739 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-06 11:11:19.807 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-06 11:11:19.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-06 11:11:19.893 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-06 11:11:19.938 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-06 11:11:20.023 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-06 11:11:20.053 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-06 11:11:20.177 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-06 11:11:20.234 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-06 11:11:20.272 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-06 11:11:20.306 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-06 11:11:20.337 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-06 11:11:20.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-06 11:11:20.402 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-06 11:11:20.440 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-06 11:11:20.470 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-06 11:11:20.497 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-06 11:11:20.527 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-06 11:11:20.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-06 11:11:20.588 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-06 11:11:20.623 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-06 11:11:20.681 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-06 11:11:20.727 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-06 11:11:20.774 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-06 11:11:20.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-06 11:11:20.848 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-06 11:11:20.905 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-06 11:11:20.942 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-06 11:11:20.979 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-06 11:11:21.032 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-06 11:11:21.082 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-06 11:11:21.124 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-06 11:11:21.183 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-06 11:11:21.227 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-06 11:11:21.279 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-06 11:11:21.315 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-06 11:11:21.382 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-06 11:11:21.416 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-06 11:11:21.454 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-06 11:11:21.490 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-06 11:11:21.570 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-06 11:11:21.605 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-06 11:11:21.637 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-06 11:11:21.667 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-06 11:11:21.698 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-06 11:11:21.731 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-06 11:11:21.762 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-06 11:11:21.809 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-06 11:11:21.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-06 11:11:21.878 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-06 11:11:21.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-06 11:11:21.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-06 11:11:22.076 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-06 11:11:22.783 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-06 11:11:22.944 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-06 11:11:23.085 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-06 11:11:23.498 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-06 11:11:23.581 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-06 11:11:23.670 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-06 11:11:24.185 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-06 11:11:24.315 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-06 11:11:24.389 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-06 11:11:24.948 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-06 11:11:25.336 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-06 11:11:25.399 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-06 11:11:30.643 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-06 11:11:30.688 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-06 11:11:30.751 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-06 11:11:30.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-06 11:11:30.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-06 11:11:30.915 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-06 11:11:30.974 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-06 11:11:31.054 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-06 11:11:31.101 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-06 11:11:31.154 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-06 11:11:31.211 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-06 11:11:31.272 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-06 11:11:31.346 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-06 11:11:31.407 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-06 11:11:31.441 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-06 11:11:31.532 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-06 11:11:31.581 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-06 11:11:31.634 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-06 11:11:31.954 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-06 11:11:32.008 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-06 11:11:32.080 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-06 11:11:32.184 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-06 11:11:32.279 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-06 11:11:32.378 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-06 11:11:32.436 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-06 11:11:32.476 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-06 11:11:32.511 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-06 11:11:32.545 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-06 11:11:32.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-06 11:11:32.647 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-06 11:12:26.176 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-06 11:12:29.775 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40014 mb of usable space. - resetting to maximum available disk space: 40014 mb
2025-08-06 11:13:13.514 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-06 15:34:49.919 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-06 15:34:50.117 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-06 15:34:50.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-06 15:34:50.280 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-06 15:34:50.353 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-06 15:34:50.461 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-06 15:34:50.524 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-06 15:34:50.697 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-06 15:34:50.768 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-06 15:34:50.842 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-06 15:34:50.906 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-06 15:34:50.969 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-06 15:34:51.021 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-06 15:34:51.092 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-06 15:34:51.170 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-06 15:34:51.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-06 15:34:51.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-06 15:34:51.351 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-06 15:34:51.411 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-06 15:34:51.488 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-06 15:34:51.537 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-06 15:34:51.585 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-06 15:34:51.630 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-06 15:34:51.683 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-06 15:34:51.749 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-06 15:34:51.793 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-06 15:34:51.840 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-06 15:34:51.894 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-06 15:34:51.996 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-06 15:34:52.045 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-06 15:34:52.094 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-06 15:34:52.138 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-06 15:34:52.182 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-06 15:34:52.226 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-06 15:34:52.275 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-06 15:34:52.322 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-06 15:34:52.355 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-06 15:34:52.405 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-06 15:34:52.446 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-06 15:34:52.492 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-06 15:34:52.536 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-06 15:34:52.571 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-06 15:34:52.627 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-06 15:34:52.669 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-06 15:34:52.704 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-06 15:34:52.829 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-06 15:34:52.929 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-06 15:34:52.972 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-06 15:34:53.018 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-06 15:34:53.062 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-06 15:34:53.106 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-06 15:34:53.152 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-06 15:34:53.200 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-06 15:34:53.322 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-06 15:34:53.376 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-06 15:34:53.409 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-06 15:34:53.440 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-06 15:34:53.469 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-06 15:34:53.503 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-06 15:34:53.541 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-06 15:34:53.577 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-06 15:34:53.616 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-06 15:34:53.706 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-06 15:34:53.740 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-06 15:34:53.772 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-06 15:34:53.816 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-06 15:34:53.850 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-06 15:34:53.895 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-06 15:34:53.925 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-06 15:34:53.953 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-06 15:34:53.982 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-06 15:34:54.012 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-06 15:34:54.056 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-06 15:34:54.109 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-06 15:34:54.140 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-06 15:34:54.178 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-06 15:34:54.214 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-06 15:34:54.262 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-06 15:34:54.304 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-06 15:34:54.388 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-06 15:34:54.445 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-06 15:34:54.511 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-06 15:34:54.561 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-06 15:34:54.734 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-06 15:34:54.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-06 15:34:54.935 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-06 15:34:54.995 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-06 15:34:55.043 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-06 15:34:55.197 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-06 15:34:55.289 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-06 15:34:55.343 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-06 15:34:55.386 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-06 15:34:55.510 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-06 15:34:55.593 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-06 15:34:55.656 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-06 15:34:55.747 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-06 15:34:55.796 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-06 15:34:55.838 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-06 15:34:55.879 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-06 15:34:55.955 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-06 15:34:56.018 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-06 15:34:56.063 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-06 15:34:56.107 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-06 15:34:56.149 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-06 15:34:56.271 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-06 15:34:56.377 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-06 15:34:56.433 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-06 15:34:56.476 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-06 15:34:56.529 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-06 15:34:56.611 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-06 15:34:56.641 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-06 15:34:56.683 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-06 15:34:56.731 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-06 15:34:56.773 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-06 15:34:56.803 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-06 15:34:56.857 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-06 15:34:56.907 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-06 15:34:56.961 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-06 15:34:57.009 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-06 15:34:57.053 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-06 15:34:57.094 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-06 15:34:57.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-06 15:34:57.188 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-06 15:34:57.238 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-06 15:34:57.281 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-06 15:34:57.321 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-06 15:34:57.360 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-06 15:34:57.405 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-06 15:34:57.445 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-06 15:34:57.516 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-06 15:34:57.567 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-06 15:34:57.622 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-06 15:34:57.654 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-06 15:34:57.697 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-06 15:34:57.744 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-06 15:34:57.778 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-06 15:34:57.813 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-06 15:34:57.844 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-06 15:34:57.888 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-06 15:34:57.927 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-06 15:34:58.076 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-06 15:34:58.143 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-06 15:34:58.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-06 15:34:58.241 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-06 15:34:58.285 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-06 15:34:58.321 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-06 15:34:58.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-06 15:34:58.409 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-06 15:34:58.453 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-06 15:34:58.497 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-06 15:34:58.542 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-06 15:34:58.589 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-06 15:34:58.635 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-06 15:34:58.685 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-06 15:34:58.756 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-06 15:34:58.834 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-06 15:34:58.890 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-06 15:34:58.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-06 15:34:58.962 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-06 15:34:59.024 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-06 15:34:59.066 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-06 15:34:59.108 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-06 15:34:59.172 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-06 15:34:59.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-06 15:34:59.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-06 15:34:59.353 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-06 15:34:59.418 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-06 15:34:59.491 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-06 15:34:59.538 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-06 15:34:59.659 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-06 15:34:59.717 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-06 15:34:59.769 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-06 15:34:59.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-06 15:34:59.907 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-06 15:34:59.960 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-06 15:35:00.014 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-06 15:35:00.077 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-06 15:35:00.160 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-06 15:35:00.225 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-06 15:35:00.276 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-06 15:35:00.334 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-06 15:35:00.387 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-06 15:35:00.440 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-06 15:35:00.507 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-06 15:35:00.564 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-06 15:35:00.621 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-06 15:35:00.703 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-06 15:35:00.740 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-06 15:35:00.787 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-06 15:35:00.831 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-06 15:35:00.881 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-06 15:35:00.923 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-06 15:35:00.968 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-06 15:35:01.032 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-06 15:35:01.083 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-06 15:35:01.145 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-06 15:35:01.256 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-06 15:35:01.285 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-06 15:35:02.410 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-06 15:35:02.474 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-06 15:35:02.517 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-06 15:35:02.560 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-06 15:35:02.595 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-06 15:35:02.639 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-06 15:35:02.680 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-06 15:35:02.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-06 15:35:02.766 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-06 15:35:02.798 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-06 15:35:02.832 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-06 15:35:02.869 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-06 15:35:02.916 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-06 15:35:02.952 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-06 15:35:02.983 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-06 15:35:03.041 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-06 15:35:03.073 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-06 15:35:03.109 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-06 15:35:03.176 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-06 15:35:03.211 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-06 15:35:03.252 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-06 15:35:03.335 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-06 15:35:03.385 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanHisEntity",So @TableField will not work!
2025-08-06 15:35:03.442 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-06 15:35:03.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-06 15:35:03.588 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-06 15:35:03.625 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-06 15:35:03.660 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-06 15:35:03.693 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-06 15:35:03.726 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-06 15:35:03.795 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-06 15:36:03.185 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-06 15:36:06.631 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40019 mb of usable space. - resetting to maximum available disk space: 40019 mb
2025-08-06 15:36:42.335 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-06 17:02:56.868 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-06 17:05:48.354 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-06 17:05:48.620 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-06 17:05:48.721 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-06 17:05:48.843 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-06 17:05:48.933 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-06 17:05:49.036 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-06 17:05:49.112 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-06 17:05:49.275 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-06 17:05:49.321 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-06 17:05:49.382 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-06 17:05:49.424 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-06 17:05:49.466 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-06 17:05:49.507 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-06 17:05:49.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-06 17:05:49.613 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-06 17:05:49.670 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-06 17:05:49.750 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-06 17:05:49.841 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-06 17:05:49.890 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-06 17:05:49.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-06 17:05:49.987 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-06 17:05:50.071 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-06 17:05:50.180 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-06 17:05:50.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-06 17:05:50.524 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-06 17:05:50.655 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-06 17:05:50.894 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-06 17:05:51.001 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-06 17:05:51.255 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-06 17:05:51.380 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-06 17:05:51.525 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-06 17:05:51.638 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-06 17:05:51.728 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-06 17:05:51.803 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-06 17:05:51.919 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-06 17:05:52.038 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-06 17:05:52.137 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-06 17:05:52.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-06 17:05:52.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-06 17:05:52.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-06 17:05:52.602 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-06 17:05:52.702 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-06 17:05:52.850 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-06 17:05:52.949 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-06 17:05:53.056 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-06 17:05:53.454 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-06 17:05:53.638 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-06 17:05:53.741 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-06 17:05:53.838 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-06 17:05:53.912 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-06 17:05:54.017 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-06 17:05:54.086 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-06 17:05:54.155 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-06 17:05:54.450 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-06 17:05:54.548 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-06 17:05:54.704 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-06 17:05:54.804 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-06 17:05:54.886 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-06 17:05:54.969 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-06 17:05:55.043 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-06 17:05:55.120 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-06 17:05:55.188 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-06 17:05:55.324 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-06 17:05:55.383 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-06 17:05:55.440 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-06 17:05:55.501 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-06 17:05:55.536 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-06 17:05:55.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-06 17:05:55.610 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-06 17:05:55.640 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-06 17:05:55.693 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-06 17:05:55.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-06 17:05:55.789 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-06 17:05:55.833 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-06 17:05:55.870 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-06 17:05:55.935 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-06 17:05:55.990 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-06 17:05:56.033 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-06 17:05:56.065 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-06 17:05:56.147 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-06 17:05:56.210 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-06 17:05:56.258 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-06 17:05:56.304 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-06 17:05:56.451 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-06 17:05:56.598 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-06 17:05:56.656 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-06 17:05:56.715 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-06 17:05:56.757 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-06 17:05:56.905 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-06 17:05:56.990 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-06 17:05:57.041 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-06 17:05:57.074 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-06 17:05:57.197 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-06 17:05:57.270 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-06 17:05:57.328 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-06 17:05:57.404 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-06 17:05:57.455 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-06 17:05:57.488 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-06 17:05:57.523 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-06 17:05:57.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-06 17:05:57.633 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-06 17:05:57.672 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-06 17:05:57.703 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-06 17:05:57.733 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-06 17:05:57.837 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-06 17:05:57.949 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-06 17:05:57.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-06 17:05:58.025 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-06 17:05:58.113 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-06 17:05:58.143 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-06 17:05:58.171 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-06 17:05:58.210 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-06 17:05:58.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-06 17:05:58.272 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-06 17:05:58.302 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-06 17:05:58.350 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-06 17:05:58.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-06 17:05:58.436 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-06 17:05:58.471 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-06 17:05:58.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-06 17:05:58.536 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-06 17:05:58.570 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-06 17:05:58.613 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-06 17:05:58.652 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-06 17:05:58.686 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-06 17:05:58.714 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-06 17:05:58.741 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-06 17:05:58.771 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-06 17:05:58.809 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-06 17:05:58.879 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-06 17:05:58.918 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-06 17:05:58.954 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-06 17:05:58.987 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-06 17:05:59.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-06 17:05:59.067 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-06 17:05:59.098 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-06 17:05:59.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-06 17:05:59.156 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-06 17:05:59.187 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-06 17:05:59.222 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-06 17:05:59.359 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-06 17:05:59.412 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-06 17:05:59.459 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-06 17:05:59.504 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-06 17:05:59.546 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-06 17:05:59.581 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-06 17:05:59.618 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-06 17:05:59.698 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-06 17:05:59.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-06 17:05:59.762 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-06 17:05:59.793 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-06 17:05:59.825 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-06 17:05:59.852 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-06 17:05:59.885 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-06 17:05:59.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-06 17:05:59.981 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-06 17:06:00.030 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-06 17:06:00.060 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-06 17:06:00.088 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-06 17:06:00.134 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-06 17:06:00.172 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-06 17:06:00.216 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-06 17:06:00.264 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-06 17:06:00.303 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-06 17:06:00.336 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-06 17:06:00.386 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-06 17:06:00.434 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-06 17:06:00.483 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-06 17:06:00.515 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-06 17:06:00.574 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-06 17:06:00.618 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-06 17:06:00.660 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-06 17:06:00.692 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-06 17:06:00.724 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-06 17:06:00.756 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-06 17:06:00.788 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-06 17:06:00.819 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-06 17:06:00.857 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-06 17:06:00.895 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-06 17:06:00.928 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-06 17:06:00.970 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-06 17:06:01.001 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-06 17:06:01.037 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-06 17:06:01.093 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-06 17:06:01.138 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-06 17:06:01.174 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-06 17:06:01.296 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-06 17:06:01.334 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-06 17:06:01.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-06 17:06:01.396 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-06 17:06:01.423 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-06 17:06:01.452 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-06 17:06:01.486 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-06 17:06:01.544 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-06 17:06:01.585 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-06 17:06:01.636 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-06 17:06:01.738 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-06 17:06:01.770 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-06 17:06:02.917 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-06 17:06:02.951 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-06 17:06:02.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-06 17:06:03.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-06 17:06:03.063 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-06 17:06:03.100 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-06 17:06:03.140 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-06 17:06:03.210 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-06 17:06:03.239 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-06 17:06:03.270 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-06 17:06:03.304 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-06 17:06:03.337 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-06 17:06:03.389 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-06 17:06:03.429 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-06 17:06:03.460 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-06 17:06:03.518 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-06 17:06:03.551 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-06 17:06:03.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-06 17:06:03.679 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-06 17:06:03.716 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-06 17:06:03.759 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-06 17:06:03.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-06 17:06:03.886 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanHisEntity",So @TableField will not work!
2025-08-06 17:06:03.953 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-06 17:06:04.055 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-06 17:06:04.097 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-06 17:06:04.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-06 17:06:04.177 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-06 17:06:04.210 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-06 17:06:04.243 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-06 17:06:04.311 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-06 17:06:54.625 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-06 17:06:57.975 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40018 mb of usable space. - resetting to maximum available disk space: 40018 mb
2025-08-06 17:07:39.759 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-06 17:55:28.155 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-06 17:56:16.746 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-06 17:56:16.934 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-06 17:56:17.008 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-06 17:56:17.071 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-06 17:56:17.124 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-06 17:56:17.207 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-06 17:56:17.259 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-06 17:56:17.389 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-06 17:56:17.424 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-06 17:56:17.463 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-06 17:56:17.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-06 17:56:17.539 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-06 17:56:17.599 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-06 17:56:17.630 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-06 17:56:17.666 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-06 17:56:17.726 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-06 17:56:17.769 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-06 17:56:17.807 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-06 17:56:17.850 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-06 17:56:17.889 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-06 17:56:17.930 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-06 17:56:17.965 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-06 17:56:17.996 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-06 17:56:18.027 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-06 17:56:18.070 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-06 17:56:18.104 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-06 17:56:18.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-06 17:56:18.195 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-06 17:56:18.268 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-06 17:56:18.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-06 17:56:18.335 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-06 17:56:18.371 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-06 17:56:18.437 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-06 17:56:18.469 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-06 17:56:18.504 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-06 17:56:18.534 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-06 17:56:18.569 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-06 17:56:18.611 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-06 17:56:18.642 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-06 17:56:18.677 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-06 17:56:18.711 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-06 17:56:18.744 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-06 17:56:18.780 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-06 17:56:18.820 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-06 17:56:18.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-06 17:56:18.954 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-06 17:56:19.018 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-06 17:56:19.050 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-06 17:56:19.084 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-06 17:56:19.113 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-06 17:56:19.140 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-06 17:56:19.170 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-06 17:56:19.201 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-06 17:56:19.329 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-06 17:56:19.365 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-06 17:56:19.394 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-06 17:56:19.423 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-06 17:56:19.451 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-06 17:56:19.483 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-06 17:56:19.519 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-06 17:56:19.557 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-06 17:56:19.593 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-06 17:56:19.655 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-06 17:56:19.688 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-06 17:56:19.720 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-06 17:56:19.777 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-06 17:56:19.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-06 17:56:19.861 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-06 17:56:19.889 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-06 17:56:19.917 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-06 17:56:19.944 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-06 17:56:19.971 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-06 17:56:20.006 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-06 17:56:20.053 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-06 17:56:20.084 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-06 17:56:20.144 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-06 17:56:20.171 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-06 17:56:20.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-06 17:56:20.266 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-06 17:56:20.343 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-06 17:56:20.375 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-06 17:56:20.411 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-06 17:56:20.449 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-06 17:56:20.560 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-06 17:56:20.653 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-06 17:56:20.701 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-06 17:56:20.743 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-06 17:56:20.781 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-06 17:56:20.913 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-06 17:56:20.984 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-06 17:56:21.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-06 17:56:21.063 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-06 17:56:21.206 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-06 17:56:21.338 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-06 17:56:21.395 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-06 17:56:21.475 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-06 17:56:21.517 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-06 17:56:21.548 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-06 17:56:21.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-06 17:56:21.640 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-06 17:56:21.680 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-06 17:56:21.716 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-06 17:56:21.750 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-06 17:56:21.779 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-06 17:56:21.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-06 17:56:21.988 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-06 17:56:22.032 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-06 17:56:22.064 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-06 17:56:22.104 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-06 17:56:22.138 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-06 17:56:22.166 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-06 17:56:22.206 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-06 17:56:22.235 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-06 17:56:22.265 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-06 17:56:22.295 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-06 17:56:22.348 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-06 17:56:22.442 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-06 17:56:22.481 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-06 17:56:22.521 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-06 17:56:22.556 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-06 17:56:22.595 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-06 17:56:22.628 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-06 17:56:22.659 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-06 17:56:22.698 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-06 17:56:22.730 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-06 17:56:22.759 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-06 17:56:22.792 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-06 17:56:22.825 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-06 17:56:22.856 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-06 17:56:22.919 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-06 17:56:22.957 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-06 17:56:22.998 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-06 17:56:23.035 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-06 17:56:23.082 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-06 17:56:23.117 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-06 17:56:23.148 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-06 17:56:23.176 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-06 17:56:23.205 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-06 17:56:23.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-06 17:56:23.276 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-06 17:56:23.394 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-06 17:56:23.453 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-06 17:56:23.491 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-06 17:56:23.530 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-06 17:56:23.612 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-06 17:56:23.642 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-06 17:56:23.676 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-06 17:56:23.715 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-06 17:56:23.745 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-06 17:56:23.772 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-06 17:56:23.802 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-06 17:56:23.831 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-06 17:56:23.862 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-06 17:56:23.895 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-06 17:56:23.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-06 17:56:23.982 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-06 17:56:24.026 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-06 17:56:24.057 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-06 17:56:24.087 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-06 17:56:24.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-06 17:56:24.186 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-06 17:56:24.225 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-06 17:56:24.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-06 17:56:24.327 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-06 17:56:24.368 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-06 17:56:24.425 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-06 17:56:24.461 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-06 17:56:24.507 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-06 17:56:24.536 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-06 17:56:24.603 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-06 17:56:24.649 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-06 17:56:24.684 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-06 17:56:24.718 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-06 17:56:24.752 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-06 17:56:24.853 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-06 17:56:24.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-06 17:56:24.916 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-06 17:56:24.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-06 17:56:24.981 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-06 17:56:25.013 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-06 17:56:25.062 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-06 17:56:25.114 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-06 17:56:25.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-06 17:56:25.189 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-06 17:56:25.230 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-06 17:56:25.266 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-06 17:56:25.337 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-06 17:56:25.376 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-06 17:56:25.406 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-06 17:56:25.436 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-06 17:56:25.462 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-06 17:56:25.498 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-06 17:56:25.535 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-06 17:56:25.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-06 17:56:25.612 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-06 17:56:25.660 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-06 17:56:25.767 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-06 17:56:25.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-06 17:56:26.955 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-06 17:56:26.990 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-06 17:56:27.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-06 17:56:27.072 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-06 17:56:27.115 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-06 17:56:27.154 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-06 17:56:27.207 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-06 17:56:27.276 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-06 17:56:27.308 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-06 17:56:27.341 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-06 17:56:27.383 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-06 17:56:27.467 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-06 17:56:27.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-06 17:56:27.547 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-06 17:56:27.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-06 17:56:27.636 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-06 17:56:27.668 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-06 17:56:27.704 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-06 17:56:27.773 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-06 17:56:27.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-06 17:56:27.851 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-06 17:56:27.933 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-06 17:56:27.985 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanHisEntity",So @TableField will not work!
2025-08-06 17:56:28.044 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-06 17:56:28.145 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-06 17:56:28.189 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-06 17:56:28.233 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-06 17:56:28.267 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-06 17:56:28.299 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-06 17:56:28.331 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-06 17:56:28.402 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-06 17:57:22.480 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-06 17:57:32.108 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40017 mb of usable space. - resetting to maximum available disk space: 40017 mb
2025-08-06 17:58:08.720 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
