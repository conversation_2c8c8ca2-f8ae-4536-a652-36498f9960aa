/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * <p>http://www.9dyun.cn
 *
 * <p>版权所有，侵权必究！
 */
package com.dian.modules.dm.service.impl;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.client.base.BaseClient;
import com.dian.client.dm.DmClient;
import com.dian.client.im.ImClient;
import com.dian.client.order.OrderClient;
import com.dian.client.sys.SysClient;
import com.dian.common.annotation.HandleDoc;
import com.dian.common.exception.RRException;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.server.CommonService;
import com.dian.common.utils.*;
import com.dian.common.validator.Assert;
import com.dian.common.validator.ValidatorUtils;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.vo.UserSimpleVO;
import com.dian.enums.VendorStatEnum;
import com.dian.enums.WhetherEnum;
import com.dian.k3cloud.vo.purchaseOrder.PurchaseOrderVo;
import com.dian.mbo.sap.zmm009.request.SapInTransitQuantityVO;
import com.dian.mbo.sap.zmm009.response.SapInTransitQuantityReturnInfoVO;
import com.dian.mbo.wms.delivery.request.PushDeliveryItemRequest;
import com.dian.mbo.wms.delivery.request.PushDeliveryRequest;
import com.dian.mbo.wms.delivery.response.WmsDeliveryResponse;
import com.dian.mes.meidiCloud.requset.*;
import com.dian.modules.base.query.GoodsReqVo;
import com.dian.modules.base.service.ConfigService;
import com.dian.modules.base.vo.*;
import com.dian.modules.dm.dao.DeliveryDao;
import com.dian.modules.dm.entity.*;
import com.dian.modules.dm.query.DmCurrencyQuery;
import com.dian.modules.dm.request.DelDeliveryReq;
import com.dian.modules.dm.request.MasterSalveQuery;
import com.dian.modules.dm.service.*;
import com.dian.modules.dm.vo.*;
import com.dian.modules.enums.commom.FlowTaskStatusEnum;
import com.dian.modules.enums.common.*;
import com.dian.modules.enums.common.MasterStatEnum;
import com.dian.modules.enums.dm.*;
import com.dian.common.utils.StringUtils;
import com.dian.modules.enums.dm.SupplyBlackTypeEnum;
import com.dian.modules.enums.im.MaterialApproveType;
import com.dian.modules.enums.sys.Ent_EntTypeEnum;
import com.dian.modules.gve.wms.service.GveWmsService;
import com.dian.modules.im.request.MaterialPriceRequest;
import com.dian.modules.im.request.PriceTranRequest;
import com.dian.modules.im.vo.MaterialPriceDataVO;
import com.dian.modules.im.vo.MaterialPriceItemVO;
import com.dian.modules.im.vo.MaterialPricelVO;
import com.dian.modules.im.vo.PriceTranVO;
import com.dian.modules.k3cloud.service.PurchaseOrderJoggleService;
import com.dian.modules.k3cloud.service.impl.PurchaseOrderJoggleServiceImpl;
import com.dian.modules.mes.service.MesApiService;
import com.dian.modules.order.vo.PurEntityVO;
import com.dian.modules.order.vo.PurItemLineVO;
import com.dian.modules.sap.service.SapApiService;
import com.dian.modules.sys.entity.SysEntEntity;
import com.dian.modules.sys.service.SysEntService;
import com.dian.modules.wms.service.WmsApiService;
import com.dian.util.DateConverter;
import com.dian.vo.DeliveryClosingVO;
import com.dian.vo.EmailMessageVo;
import com.dian.vo.QuerySupplySlaveVO;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 送货单服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-08 11:09:10
 */
@Service("deliveryService")
public class DeliveryServiceImpl extends ServiceImpl<DeliveryDao, DeliveryEntity> implements DeliveryService {
  protected Logger logger = TraceLoggerFactory.getLogger(getClass());

  public static final Object sharedLock = new Object();

  @Autowired
  public CommonService commonService;
  @Autowired
  private DeliveryService deliveryService;
  @Autowired
  private DeliveryItemService deliveryItemService;
  @Autowired
  private ProcurementPlanService procurementPlanService;
  @Autowired
  private ConfigService configService;
  @Autowired
  private OrderClient orderClient;
  @Autowired
  private BaseClient baseClient;
  @Autowired
  private SysClient sysClient;
  @Autowired
  private SysEntService sysEntService;
  @Autowired
  public DmClient dmClient;
  @Autowired
  public ImClient imClient;
  @Autowired
  public SheetHeadService sheetHeadService;
  @Autowired
  private MasterService masterService;
  @Autowired
  private MasterItemService masterItemService;
  @Autowired
  private SapApiService sapApiService;
  @Autowired
  private DeliveryPlanItemService deliveryPlanItemService;
  @Autowired
  private VendorLogicInvService vendorLogicInvService;
  @Autowired
  private DeliveryShoppingCartService deliveryShoppingCartService;
  @Autowired
  private WmsApiService wmsApiService;
  @Autowired
  private MesApiService mesApiService;
  @Autowired
  private VendorWarrantyService vendorWarrantyService;
  @Autowired
  private SupplyBlackArchivesService supplyBlackArchivesService;
  @Autowired
  private InspectionSheetService inspectionSheetService;
  @Autowired
  private InspectionSheetItemService inspectionSheetItemService;
  @Autowired
  private InspectionReviewService inspectionReviewService;
  @Autowired
  private GveWmsService gveWmsService;
  @Autowired
  private PurchaseOrderJoggleService purchaseOrderJoggleService;

    /**
     * 送货单分页
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        IPage<DeliveryEntity> page =
                this.page(new Query<DeliveryEntity>().getPage(params), getQueryWrapper(params));
        return new PageUtils(page);
    }

    /**
     * 送货单新增
     *
     * @param deliveryEntity
     * @return
     */
    @Override
    @HandleDoc
    @GlobalTransactional(rollbackFor = Throwable.class)
    public DeliveryEntity saveInfo(DeliveryEntity deliveryEntity) {
        VendorVO vendorVo = baseClient.getVendorVoBySourceId(commonService.getTenantId());
        deliveryEntity.setTenantId(vendorVo.getTenantId());
        deliveryEntity.setTenantPId(0L);
        deliveryEntity.setVendorId(vendorVo.getSoureId());
        deliveryEntity.setVendorCode(vendorVo.getVendorErpCode());
        deliveryEntity.setVendorName(vendorVo.getVendorName());
        deliveryEntity.setVendorFullName(vendorVo.getVendorFullName());
        if (DeliveryTypeEnum.PLANDELIVERY.getValue().equals(deliveryEntity.getDeliveryType())) {
            //按计划生成送货单据
            return this.asPlanned(deliveryEntity);
        } else {
            DeliveryEntity delivery = this.Delivery(deliveryEntity);
            JSONObject jsonObject =
                    this.saveOperMsg(
                            OperBillEnum.DELIVRY.getValue(),
                            OperTypeEnum.CREATE.getValue(),
                            deliveryEntity.getId(),
                            "创建送货单" + deliveryEntity.getDeNo(),
                            deliveryEntity.getDeNo());
            int i = Integer.valueOf(jsonObject.get("code") + "");
            if (i == 500) {
                throw new RRException(String.format("[%s] " + jsonObject.get("msg"), deliveryEntity.getDeNo()));
            }
            return delivery;
        }
    }

    /**
     * 送货单新增V2
     * @param delivery
     * @return
     */
    @Override
    @HandleDoc
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long saveInfoV2(DeliveryEntity delivery) {
        Assert.isNull(delivery, "保存的送货单数据为空");
        VendorVO vendorVo = baseClient.getVendorVoBySourceId(commonService.getTenantId());
        Assert.isNull(vendorVo, "查询不到对应的供应商数据");
        delivery.setTenantId(vendorVo.getTenantId());
        delivery.setTenantPId(0L);
        delivery.setVendorId(vendorVo.getSoureId());
        delivery.setVendorCode(vendorVo.getVendorErpCode());
        delivery.setVendorName(vendorVo.getVendorName());
        delivery.setVendorFullName(vendorVo.getVendorFullName());
        delivery.setDeNo(sysClient.getBillTenantNo("dm_delivery", delivery.getTenantId()));
        delivery.setDeStat(1);
        if (Objects.nonNull(delivery.getDocumentDate())){
            delivery.setDocumentDate(new Date());
        }
        delivery.setId(null);
        checkDeliveryParams(delivery);
        List<String> warehouseNameList = delivery.getDeliveryItemEntityList().stream().map(DeliveryItemEntity::getWarehouseName).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(warehouseNameList)){
            throw new RRException("请选择仓库");
        }
        if (warehouseNameList.size() > 1){
            throw new RRException("存在不相同的仓库地址");
        }
        delivery.setShippingAddress(warehouseNameList.get(0));
        this.save(delivery);
        deliveryItemService.saveInfoV2(delivery);
        return delivery.getId();
    }

    private DeliveryEntity Delivery(DeliveryEntity deliveryEntity) {
        deliveryEntity.setDeNo(sysClient.getBillTenantNo("dm_delivery", deliveryEntity.getTenantId()));
        deliveryEntity.setDeStat(DeliveryDeStatEnum.TOBEISSUED.getCode());
        // 数据完整性校验
        this.paramsCheck(deliveryEntity, AddGroup.class);
        // 校验仓库信息
        checkWarehouse(deliveryEntity);
        deliveryEntity.setPurchaserName(deliveryEntity.getPurchaserName());
        // 保存
        this.save(deliveryEntity);
        int deliverySeq = 10;

        boolean flag = true; // 送货标识
        String goodsFlag = ""; // 物料标志
        String goodsLog = ""; // 物料标志
        // 送货标识 系统参数 0-关闭 1-开启
        String goodsDeliveryFlag = sysClient.getValueByKeyAndTenantId("goodsDeliveryflag", deliveryEntity.getTenantId());
        if ("0".equals(goodsDeliveryFlag)) {
            flag = false;
        }
        boolean warehouseAlikeFlag = true; // 相同仓库标识
        String warehouseLeft = "";
        String warehouseRight = "";
        // 相同仓库送货标识 系统参数 0-关闭 1-开启
        String warehouseFlag = sysClient.getValueByKeyAndTenantId("warehouseFlag", deliveryEntity.getTenantId());
        if ("0".equals(warehouseFlag)) {
            warehouseAlikeFlag = false;
        }

        // 保存送货明细表
        if (CollectionUtils.isNotEmpty(deliveryEntity.getDeliveryItemEntityList())) {
            List<DeliveryItemEntity> detailList = deliveryEntity.getDeliveryItemEntityList();
            // 查询物料数据
            for (DeliveryItemEntity deliveryItemEntity : detailList) {
                /** 如果开启了送货标志，则校验物料标识是否一致。 */
                if (flag) {
                    JSONObject erpCode = baseClient.getErpCode(deliveryItemEntity.getTenantId(), deliveryItemEntity.getGoodsErpCode());
                    if (erpCode.get("collectFlag") != null) {
                        goodsFlag = erpCode.get("collectFlag").toString();
                    }
                    if (!goodsFlag.equals(goodsLog) && (!goodsLog.equals("") && !goodsFlag.equals(""))) {
                        throw new RRException("物料[ " + deliveryItemEntity.getGoodsErpCode() + " ]的(送货标识)和其它物料不一致，无法创建送货单");
                    }
                    if (!("".equals(goodsFlag))) {
                        goodsLog = goodsFlag;
                    }
                }

                /*重复代码迁移，(查询采购订单)*/
                List<Map<String, Object>> orderBySaleId = orderClient.findOrderBySaleId(deliveryItemEntity.getSaleItemId());
                if (!CollectionUtils.isNotEmpty(orderBySaleId)) {
                    throw new RRException(
                            String.format(
                                    "采购订单"
                                            + deliveryItemEntity.getSaleNo() + "查询不到相关明细数据，请检查数据"));
                }
                if (orderBySaleId.size() != 0) {
                    BigDecimal canMakeNum = new BigDecimal(orderBySaleId.get(0).get("canMakeNum").toString());
                    if (canMakeNum.compareTo(BigDecimal.ZERO) == 0) {
                        throw new RRException(
                                String.format(
                                        "采购订单"
                                                + deliveryItemEntity.getSaleNo()
                                                + "/"
                                                + deliveryItemEntity.getSaleSeq()
                                                + "可制单数量为0,不能再创建送货单"));
                    }
                    String isClose = orderBySaleId.get(0).get("isClose").toString();
                    if ((!"".equals(isClose)) && "1".equals(isClose)) {
                        throw new RRException("采购订单" + deliveryItemEntity.getSaleNo() + "/" + deliveryItemEntity.getSaleSeq() + "已经关闭，不能再创建送货单");
                    }
                    deliveryItemEntity.setOrderNum(new BigDecimal(orderBySaleId.get(0).get("lineOrderSum").toString()));
                    deliveryItemEntity.setSaleSeq(orderBySaleId.get(0).get("seq").toString());
                    deliveryItemEntity.setWerks(orderBySaleId.get(0).get("werks").toString());
                    deliveryItemEntity.setOrderType(Integer.parseInt(orderBySaleId.get(0).get("orderType").toString()));
                    if (!StrUtil.isEmptyIfStr(deliveryEntity.getIsShoppingCart())) {
                        deliveryItemEntity.setUomCode(orderBySaleId.get(0).get("uomCode").toString());
                        deliveryItemEntity.setUomName(orderBySaleId.get(0).get("uomName").toString());
                        deliveryItemEntity.setUomId(Long.valueOf(orderBySaleId.get(0).get("uomId").toString()));
                        deliveryItemEntity.setRateName(orderBySaleId.get(0).get("rateName").toString());
                        deliveryItemEntity.setCurrencyId(Long.valueOf(orderBySaleId.get(0).get("currencyId").toString()));
                        deliveryItemEntity.setCurrencyName(orderBySaleId.get(0).get("currencyName").toString());
                        deliveryItemEntity.setGstPrice(new BigDecimal(orderBySaleId.get(0).get("gstPrice").toString()));
                        deliveryItemEntity.setTaxPrice(new BigDecimal(orderBySaleId.get(0).get("taxPrice").toString()));
                        deliveryItemEntity.setGoodsClassCode(orderBySaleId.get(0).get("goodsClassCode").toString());
                        deliveryItemEntity.setGoodsClassName(orderBySaleId.get(0).get("goodsClassName").toString());
                    }
                    deliveryItemEntity.setGoodsModel(orderBySaleId.get(0).get("goodsModel").toString());
                    deliveryItemEntity.setWarehouseName(deliveryItemEntity.getWarehouseCode());
                    BigDecimal gstPrice = deliveryItemEntity.getGstPrice().setScale(2, BigDecimal.ROUND_HALF_UP);
                    deliveryItemEntity.setGstPrice(gstPrice);
                    BigDecimal gstAmount = deliveryItemEntity.getDevNum().multiply(gstPrice)
                            .divide(new BigDecimal(orderBySaleId.get(0).get("priceUom") + ""), 2, BigDecimal.ROUND_HALF_UP);
                    deliveryItemEntity.setGstAmount(gstAmount);
                    deliveryItemEntity.setTaxAmount(new BigDecimal("0.000000"));
                } else {
                    throw new RRException("查询不到该采购订单，如该订单存在，请联系管理员。");
                }

                // 相同仓库送货标识
                if (warehouseAlikeFlag) {
                    String warehouse = orderBySaleId.get(0).get("warehouseCode").toString();
                    warehouseLeft = warehouse;
                    if (!warehouseLeft.equals(warehouseRight) && (!warehouseLeft.equals("") && !warehouseRight.equals(""))) {
                        throw new RRException("采购订单[ " + deliveryItemEntity.getSaleNo() + " / "
                                + deliveryItemEntity.getSaleSeq() + " ]的(仓库编码)不一致，无法创建送货单");
                    }
                    if (!("".equals(warehouseLeft))) {
                        warehouseRight = warehouseLeft;
                    }
                }
                RateVO rateVO = baseClient.queryByRateCode(deliveryEntity.getTenantId(), deliveryItemEntity.getRateName());
                deliveryItemEntity.setRateId(rateVO.getId());
                deliveryItemEntity.setRateVal(rateVO.getRateVal());
                deliveryItemEntity.setDeId(deliveryEntity.getId());
                deliveryItemEntity.setPlanSrmLineId(null);
                // 根据订单明细id获取已发布的送货计划明细数量之和
                HashMap<String, Object> map =
                        getBaseMapper().getPlanNum(deliveryItemEntity.getSaleItemId() + "");
                deliveryItemEntity.setSeq(deliverySeq + ""); // 暂收单明细序号
                deliverySeq = deliverySeq + 10;
                // 将创建的送货数量回写到订单中的制单数量 -- dlw
                BigDecimal planNum = new BigDecimal(map.get("planNum") + "");
                orderClient.updateMakenum(
                        deliveryItemEntity.getSaleItemId(), deliveryItemEntity.getDevNum(), planNum);
                if (!StrUtil.isEmptyIfStr(deliveryEntity.getIsShoppingCart())) {
                    deliveryShoppingCartService.deleteCart(deliveryItemEntity.getShopCartId());
                }

            }
            deliveryItemService.saveBatch(detailList);


        }
        return deliveryEntity;
    }

    private DeliveryEntity asPlanned(DeliveryEntity deliveryEntity) {
        DeliveryEntity delivery = new DeliveryEntity();
        List<DeliveryItemEntity> list = new ArrayList<>();
        int seq = 0;
        Long tenantId = null;

        // 校验送货单明细行中对应物料的生产日期是否小于历史送货单中最大的生产日期
//        checkProdDate(deliveryEntity);
        // 校验仓库信息
        checkWarehouse(deliveryEntity);
        int idx = 0;
        for (DeliveryItemEntity deliveryItemEntity : deliveryEntity.getDeliveryItemEntityList()) {
            idx = idx+1;
//            Assert.isNull(deliveryItemEntity.getProdDate(), String.format("第%s行数据生产日期不能为空！",idx));
//            Assert.isNull(deliveryItemEntity.getProdBatchNo(), String.format("第%s行数据批次号不能为空！",idx));
//            Assert.isNull(deliveryItemEntity.getWarrantyPeriod(), StrUtil.format("第%s行数据质保期不能为空！",idx));
            DeliveryPlanItemEntity planItemEntity = deliveryPlanItemService.getById(deliveryItemEntity.getListId());
            if (planItemEntity.getDeleteFlag() == 2) {
                throw new RRException("当前送货计划已关闭，不能创建送货单，请检查数据。");
            }
            deliveryItemEntity.setGoodsModel(planItemEntity.getGoodsModel());
            tenantId=planItemEntity.getTenantId();
            BigDecimal bigDecimal = null;
            if (!StrUtil.isEmptyIfStr(planItemEntity.getMakeNum())) {
                bigDecimal = planItemEntity.getMakeNum().add(deliveryItemEntity.getDevNum());
            } else {
                bigDecimal = deliveryItemEntity.getDevNum();
            }
            planItemEntity.setMakeNum(bigDecimal);
            planItemEntity.setDeleteFlag(1);//已生成送货单
            deliveryPlanItemService.updateById(planItemEntity);
            planItemEntity.setMatchNum(deliveryItemEntity.getDevNum());
            delivery.setTenantId(planItemEntity.getTenantId());
            delivery.setPurchaserName(planItemEntity.getPurchaserName());
            delivery.setTenantPId(0L);
            delivery.setOrderType(planItemEntity.getOrderType());
            delivery.setDeliveryDate(new Date());//默认以前端选择的送货时间
            delivery.setDocumentDate(deliveryEntity.getDocumentDate());
            delivery.setWarehouseCode(deliveryEntity.getWarehouseCode());
            delivery.setVendorId(deliveryEntity.getVendorId());
            delivery.setVendorCode(deliveryEntity.getVendorCode());
            delivery.setVendorName(deliveryEntity.getVendorName());
            delivery.setDeptId(deliveryEntity.getDeptId());
            delivery.setDeptCode(deliveryEntity.getDeptCode());
            delivery.setDeptName(deliveryEntity.getDeptName());
            delivery.setAddress(deliveryEntity.getAddress());
            delivery.setShippingAddress(deliveryEntity.getShippingAddress());
            delivery.setPurchaserId(deliveryEntity.getPurchaserId());
            SysEntEntity sysEntEntity = sysEntService.queryByEntId(tenantId);
            if (sysEntEntity != null) {
                delivery.setTenantName(sysEntEntity.getEntName());
            } else {
                delivery.setTenantName("");
            }
            delivery.setReceivingControl(planItemEntity.getReceivingControl());
            seq++;
            deliveryItemEntity.setSeq(seq + "");
            DeliveryPlanItemEntity planEntity = deliveryPlanItemService.getById(deliveryItemEntity.getListId());
            deliveryItemEntity.setPlanId(planEntity.getPlanId());
            deliveryItemEntity.setPlanLineId(planEntity.getPlanLineId());
            deliveryItemEntity.setPlanSrmLineId(deliveryItemEntity.getListId());
            deliveryItemEntity.setSaleId(planItemEntity.getSaleId());
            deliveryItemEntity.setSaleItemId(planItemEntity.getSaleItemId());
            PurItemLineVO purItemVo = orderClient.getPurItemVoById(planItemEntity.getSaleItemId());
            if (Objects.nonNull(purItemVo)) {
                deliveryItemEntity.setWarehouseCode(purItemVo.getWarehouseCode());
            }
            deliveryItemEntity.setSaleNo(planItemEntity.getSaleNo());
            deliveryItemEntity.setGoodsId(planItemEntity.getGoodsId());
            deliveryItemEntity.setGoodsCode(planItemEntity.getGoodsCode());
            deliveryItemEntity.setGoodsErpCode(planItemEntity.getGoodsErpCode());
            deliveryItemEntity.setGoodsName(planItemEntity.getGoodsName());
            deliveryItemEntity.setGoodsModel(planItemEntity.getGoodsModel());
            deliveryItemEntity.setDevNum(planItemEntity.getMatchNum());//送货数量
            deliveryItemEntity.setDrawingNo(planItemEntity.getDrawingNo());
            deliveryItemEntity.setMrpRegion(planItemEntity.getMrpRegion());
            deliveryItemEntity.setTemNum(BigDecimal.ZERO);
            deliveryItemEntity.setUnInvNum(BigDecimal.ZERO);
            deliveryItemEntity.setUnNum(BigDecimal.ZERO);
            deliveryItemEntity.setInvNum(BigDecimal.ZERO);
            deliveryItemEntity.setBarcodeType(0);
            PurItemLineVO purItemLineVO = orderClient.queryBySaleItemId(planItemEntity.getSaleItemId());
            if (purItemLineVO == null) {
                throw new RRException("查询不到采购订单" + planItemEntity.getSaleNo() + "，如该订单不存在，请联系管理员。");
            }
            deliveryItemEntity.setUomId(purItemLineVO.getUomId());
            deliveryItemEntity.setUomCode(purItemLineVO.getUomCode());
            deliveryItemEntity.setUomName(purItemLineVO.getUomName());
            deliveryItemEntity.setAuxUomId(purItemLineVO.getAuxUomId());
            deliveryItemEntity.setAuxUomCode(purItemLineVO.getAuxUomCode());
            deliveryItemEntity.setAuxUomName(purItemLineVO.getAuxUomName());
            if (!StrUtil.isEmptyIfStr(purItemLineVO.getRateId())) {
                deliveryItemEntity.setRateId(purItemLineVO.getRateId());
            } else {
                deliveryItemEntity.setRateId(0L);
            }
            deliveryItemEntity.setReplyDate(purItemLineVO.getReplyDate());
            deliveryItemEntity.setRateName(purItemLineVO.getRateName());
            deliveryItemEntity.setRateVal(purItemLineVO.getRateVal());
            deliveryItemEntity.setCurrencyId(purItemLineVO.getCurrencyId());
            deliveryItemEntity.setCurrencyName(purItemLineVO.getCurrencyName());
            deliveryItemEntity.setTaxesType(purItemLineVO.getTaxesType());
            deliveryItemEntity.setInvoiceType(purItemLineVO.getInvoiceType());
            deliveryItemEntity.setGstPrice(purItemLineVO.getGstPrice());
            deliveryItemEntity.setTaxPrice(purItemLineVO.getTaxPrice());
            deliveryItemEntity.setDeliveryDate(planItemEntity.getDeliveryDate());
            //获取系统参数 - 允许提前送货天数,默认为3,只按计划送货时才生效
            String deliveryHomeDays = sysClient.getValueByKeyAndTenantId("deliveryHomeDays", deliveryEntity.getTenantId());
            //系统参数不为空，检验当前创建送货单日期是否超出提前送货日期
            if (!StrUtil.isEmptyIfStr(deliveryHomeDays)) {
                //计划需要送货日期
                Date planDate = planItemEntity.getPlanDate();
                //需要扣减的天数
                Integer sub = -Integer.parseInt(deliveryHomeDays);
                //计算提前可送货日期,送货日期+(-需要扣减的天数)
                Date inAdvanceDeliveryDate = DateUtil.offsetDay(planDate, sub);
                //当前日期
                Date nowDate = new Date();
                if (nowDate.compareTo(inAdvanceDeliveryDate) == -1) {
                    throw new RRException(String.format("当前创建送货单日期[%s]不能超出最早可提前送货日期[%s]"
                            , DateUtil.format(nowDate, "yyyy-MM-dd"), DateUtil.format(inAdvanceDeliveryDate, "yyyy-MM-dd")));
                }
            }
            deliveryItemEntity.setPlanDate(planItemEntity.getPlanDate());
            deliveryItemEntity.setRemark(deliveryItemEntity.getRemark());
            deliveryItemEntity.setSaleSeq(purItemLineVO.getSeq() + "");//采购订单行号
            list.add(deliveryItemEntity);
        }
        delivery.setDeliveryItemEntityList(list);
        delivery.setIs(2);

        delivery.setTenantId(tenantId);

        delivery.setDeNo(sysClient.getBillTenantNo("dm_delivery", tenantId));
        delivery.setDeStat(1);
        // 数据完整性校验
        this.paramsCheck(delivery, AddGroup.class);
        PurEntityVO purchaseVO = orderClient.queryById(delivery.getDeliveryItemEntityList().get(0).getSaleId());
        if (purchaseVO.getOrderType() == 1) {
            delivery.setOrderType(purchaseVO.getOrderType());
        } else {
            delivery.setOrderType(2);
        }
        // 校验供应商是否为冻结
        checkVendor(delivery);
        // 查询是否存在物料异常档案单据状态为已审核且为停用和禁止下单的数据
        checkSupplyBlackArchives(delivery);
        // 保存
        this.save(delivery);
        // 保存或者新增质保信息
        this.vendorWarranty(delivery);
        // 保存送货明细表
        if (CollectionUtils.isNotEmpty(delivery.getDeliveryItemEntityList())) {
            List<DeliveryItemEntity> detailList = delivery.getDeliveryItemEntityList();
            // 查询物料数据
            for (DeliveryItemEntity deliveryItemEntity : detailList) {
                deliveryItemEntity.setDeId(delivery.getId());
                List<Map<String, Object>> orderBySaleId = orderClient.findOrderBySaleId(deliveryItemEntity.getSaleItemId());
                if (orderBySaleId.size() != 0) {
                    BigDecimal canMakeNum = new BigDecimal(orderBySaleId.get(0).get("canMakeNum").toString());
                    BigDecimal orderNum = new BigDecimal(orderBySaleId.get(0).get("lineOrderSum").toString());
                    BigDecimal erpMasterNum = new BigDecimal(orderBySaleId.get(0).get("erpMasterNum").toString());
                    BigDecimal erpRejectNum = new BigDecimal(orderBySaleId.get(0).get("erpRejectNum").toString());
                    if (canMakeNum.compareTo(BigDecimal.ZERO) == 0) {
                        throw new RRException(String.format("采购订单" + deliveryItemEntity.getSaleNo() + "/"+ deliveryItemEntity.getSaleSeq()+ "可制单数量为0,不能再创建送货单"));
                    }
                    BigDecimal checkNum = orderNum.subtract(erpMasterNum).add(erpRejectNum);
                    if (deliveryItemEntity.getDevNum().compareTo(checkNum) > 0) {
                        throw new RRException(String.format("采购订单" + deliveryItemEntity.getSaleNo() + "/" + deliveryItemEntity.getSaleSeq() + "可制单数量" + checkNum + "小于送货数,不能再创建送货单"));
                    }
//                    String isClose = orderBySaleId.get(0).get("isClose").toString();
//                    if ((!"".equals(isClose)) && "1".equals(isClose)) {
//                        throw new RRException("采购订单" + deliveryItemEntity.getSaleNo() + "/" + deliveryItemEntity.getSaleSeq() + "已经关闭，不能再创建送货单");
//                    }
                    // 如果是sap的退货PO，则不能创建送货单 isReturnPo(为sap传过来的退货订单-对应字段 retpo)
                    String isReturnPo = orderBySaleId.get(0).get("isReturnPo").toString();
                    if ((!"".equals(isReturnPo)) && "1".equals(isReturnPo)) {
                        throw new RRException("采购订单" + deliveryItemEntity.getSaleNo() + "/" + deliveryItemEntity.getSaleSeq() + "为退货PO，不能再创建送货单");
                    }

                    deliveryItemEntity.setOrderNum(new BigDecimal(orderBySaleId.get(0).get("lineOrderSum").toString()));
                } else {
                    throw new RRException("查询不到该采购订单明细行数据，如该订单明细行数据存在，请联系管理员 采购订单明细id为" + deliveryItemEntity.getSaleItemId());
                }
                // 根据订单明细id获取已发布的送货计划明细数量之和
                HashMap<String, Object> map = getBaseMapper().getPlanNum(deliveryItemEntity.getSaleItemId() + "");
                BigDecimal planNum = new BigDecimal(map.get("planNum") + "");
                orderClient.updateMakenum(deliveryItemEntity.getSaleItemId(), deliveryItemEntity.getDevNum(), planNum);
                deliveryItemService.save(deliveryItemEntity);
            }

        }
        JSONObject jsonObject = this.saveOperMsg(OperBillEnum.DELIVRY.getValue(), OperTypeEnum.CREATE.getValue(), delivery.getId(),
                "创建送货单" + delivery.getDeNo(), delivery.getDeNo());
        int i = Integer.valueOf(jsonObject.get("code") + "");
        if (i == 500) {
            throw new RRException(
                    String.format("[%s] " + jsonObject.get("msg"), delivery.getDeNo()));
        }
        return delivery;
    }

    /**
     * 新增或者更新质保单
     *
     * @param delivery
     */
    private void vendorWarranty(DeliveryEntity delivery) {
        List<DeliveryItemEntity> deliveryItemEntityList = delivery.getDeliveryItemEntityList();
        // 新增情况
        Map<String, List<DeliveryItemEntity>> vendorWarrantyList = new HashMap<>();
        deliveryItemEntityList.forEach(r -> {
            String key = r.getGoodsCode() + "-" + delivery.getVendorCode();
            if (!vendorWarrantyList.containsKey(key)) {
                List<DeliveryItemEntity> list = new ArrayList<>();
                list.add(r);
                vendorWarrantyList.put(key, list);
            } else {
                List<DeliveryItemEntity> value = vendorWarrantyList.get(key);
                value.add(r);
            }
        });
        //直接新增
        for (String key : vendorWarrantyList.keySet()) {
            List<DeliveryItemEntity> value = vendorWarrantyList.get(key);
            // 对键和值进行所需的操作
            System.out.println("Key: " + key + ", Value: " + value);
            List<VendorWarrantyEntity> warrantyEntityList = vendorWarrantyService.list(new QueryWrapper<VendorWarrantyEntity>().eq("goods_code", key.split("-")[0]).eq("vendor_code", key.split("-")[1]));
            VendorWarrantyEntity vendorWarrantyEntity = new VendorWarrantyEntity();
            BeanUtils.copyProperties(delivery, vendorWarrantyEntity);
            BeanUtils.copyProperties(value.get(0), vendorWarrantyEntity);
            if (warrantyEntityList.isEmpty()) {
                vendorWarrantyService.saveVendorWarrantyEntity(vendorWarrantyEntity);
            } else {
                vendorWarrantyService.update().eq("goods_code", key.split("-")[0]).eq("vendor_code", key.split("-")[1]).update(vendorWarrantyEntity);
            }
        }
    }

    /**
     * 送货单更新 meng
     *
     * @param deliveryEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long updateInfo(DeliveryEntity deliveryEntity) {
        if (deliveryEntity.getIsEle() == 0) {
            deliveryEntity.setLogisticsName(null);
            deliveryEntity.setEleNo(null);
        }
        this.checkDeliveryParams(deliveryEntity);
        // 未删除的数据
        List<DeliveryItemEntity> validItemList = deliveryEntity.getDeliveryItemEntityList().stream().filter(item -> WhetherEnum.NO.getCode().equals(item.getDeleteFlag())).collect(Collectors.toList());
        // 已删除的数据且已在数据库存在的数据
        List<DeliveryItemEntity> needDelItemList = deliveryEntity.getDeliveryItemEntityList().stream().filter(item -> WhetherEnum.YES.getCode().equals(item.getDeleteFlag()) && ObjectUtil.isNotEmpty(item.getId())).collect(Collectors.toList());
        deliveryItemService.batchDelete(deliveryEntity, needDelItemList);
        for (DeliveryItemEntity deliveryItemEntity : validItemList) {
            if (ObjectUtil.isEmpty(deliveryItemEntity.getId())){
                deliveryItemEntity.setDeId(deliveryEntity.getId());
                deliveryItemService.save(deliveryItemEntity);
            } else {
                deliveryItemService.updateInfo(deliveryItemEntity);
            }
            SaveGoodsBarVo saveGoodsBarVo = new SaveGoodsBarVo();

            saveGoodsBarVo.setTenantId(deliveryEntity.getTenantId());
            saveGoodsBarVo.setSourceId(deliveryEntity.getId());
            saveGoodsBarVo.setGoodsId(deliveryItemEntity.getGoodsId());
            saveGoodsBarVo.setSourceType(2);

            baseClient.batchExpireBarCode(saveGoodsBarVo);
        }
        List<Long> orderItemIds = validItemList.stream().map(item -> item.getSaleItemId()).distinct().collect(Collectors.toList());
        List<PurItemLineVO> orderItemList = new ArrayList<>();
        orderItemIds.forEach(orderItemId -> {
            PurItemLineVO orderLine = new PurItemLineVO();
            BigDecimal devNum = deliveryItemService.countDevNumByOrderItem(orderItemId);
            orderLine.setId(orderItemId);
            orderLine.setMakeNum(devNum);
            orderItemList.add(orderLine);
        });
        orderClient.againtCountDevNum(orderItemList);
        List<String> warehouseNameList = deliveryEntity.getDeliveryItemEntityList().stream().map(DeliveryItemEntity::getWarehouseName).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(warehouseNameList)){
            throw new RRException("请选择仓库");
        }
        if (warehouseNameList.size() > 1){
            throw new RRException("存在不相同的仓库地址");
        }
        deliveryEntity.setShippingAddress(warehouseNameList.get(0));
        // 更新主表
        this.updateById(deliveryEntity);
        return deliveryEntity.getId();
    }

    /**
     * 送货单删除
     *
     * @param ids
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean deleteInfo(Long[] ids) {
        synchronized (sharedLock){
            for (Long id : ids) {
                DeliveryEntity deliveryEntity = this.getInfo(id);
                if (deliveryEntity == null) {
                    throw new RRException(String.format("当前id为[%s]的送货单数据不存在或已被删除,删除失败！", id));
                }
                if (WhetherEnum.YES.getCode().equals(deliveryEntity.getDeleteFlag())){
                    throw new RRException(String.format("当前id为[%s]的送货单数据已被删除,删除失败！", id));
                }
                List<DeliveryItemEntity> deliveryItemList = deliveryItemService.queryItemById(id);
                // 校验删除数据
                this.deleteCheck(deliveryEntity);
                // 根据送货明细回写订单明细相关数量字段
                List<DeliveryItemEntity> deliveryItemEntities = deliveryItemService.list(new QueryWrapper<DeliveryItemEntity>().in("de_id", id));
                List<PurItemLineVO> orderItemList = new ArrayList<>();
                for (DeliveryItemEntity deliveryItemEntity : deliveryItemEntities) {
                    if (!StrUtil.isBlankIfStr(deliveryItemEntity.getPlanSrmLineId())) {//判断送货计划明细表id不为空时，需要修改送货计划的已制单数量
                        DeliveryPlanItemEntity deliveryPlanItemEntity = deliveryPlanItemService.getById(deliveryItemEntity.getPlanSrmLineId());
                        BigDecimal primaryMakeNum = deliveryPlanItemEntity.getMakeNum();//获取送货计划明细的原已制单数
                        if (primaryMakeNum.subtract(deliveryItemEntity.getDevNum()).compareTo(BigDecimal.ZERO) < 1) {
                            deliveryPlanItemEntity.setMakeNum(BigDecimal.ZERO);
                        } else {
                            deliveryPlanItemEntity.setMakeNum(primaryMakeNum.subtract(deliveryItemEntity.getDevNum()));//删除送货单后送货计划明细的已制单数 = 原送货计划明细已制单数-送货单送货数量
                        }
                        deliveryPlanItemEntity.setDeleteFlag(0);
                        deliveryPlanItemService.updateById(deliveryPlanItemEntity);
                    }
                    // 减少采购订单行的已制送货单数量make_num
                    orderClient.updateOrderItemMakeNum(deliveryItemEntity.getSaleItemId(), deliveryItemEntity.getDevNum().negate());
                    if (DeStatEnum.ISSUED.getCode().equals(deliveryEntity.getDeStat())) {
                        PurItemLineVO purItemLineVO = orderClient.updateFixNum(deliveryItemEntity.getSaleItemId(), deliveryItemEntity.getDevNum().negate());
                        orderItemList.add(purItemLineVO);
                    }
                }
                deliveryEntity.setDeliveryItemEntityList(deliveryItemList);
                deliveryItemService.remove(new QueryWrapper<DeliveryItemEntity>().in("de_id", ids));
                // 更新主表
                this.remove(new QueryWrapper<DeliveryEntity>().in("id", ids));
                if (DeStatEnum.ISSUED.getCode().equals(deliveryEntity.getDeStat())){
                    closeDeliveryToGveWms(deliveryEntity,  OperTypeEnum.DELETE.getValue());
                    if (CollectionUtil.isNotEmpty(orderItemList)){
                        purchaseOrderJoggleService.batchUpdateOrder(orderItemList,deliveryEntity.getTenantId());
                    }
                }
                this.saveOperMsg(OperBillEnum.DELIVRY.getValue(), OperTypeEnum.DELETE.getValue(), deliveryEntity.getId(), "删除送货单" + deliveryEntity.getDeNo(), deliveryEntity.getDeNo());
            }
            return true;
        }
    }

    /**
     * 删除未确认的送货明细行数据
     * @param delDeliveryLineVo
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean deleteLineByUnconfirmed(DelDeliveryLineVo delDeliveryLineVo) {
        Assert.isNull(delDeliveryLineVo.getDeId(),"送货id不能为空");
        if (CollectionUtil.isEmpty(delDeliveryLineVo.getDeLineIds())){
            throw new RRException("删除行数据不能为空");
        }
        DeliveryEntity deliveryEntity = this.getById(delDeliveryLineVo.getDeId());
        Assert.isNull(deliveryEntity,String.format("当前id为[%s]的送货单数据不存在或已被删除,删除失败！", delDeliveryLineVo.getDeId()));
        if (!DeStatEnum.TOBEISSUED.getCode().equals(deliveryEntity.getDeStat())){
            throw new RRException("当前送货单状态不是待发，不能删除行数据！");
        }
        delDeliveryLineVo.getDeLineIds().forEach(deLineId -> {
            DeliveryItemEntity deliveryItemEntity = deliveryItemService.getById(deLineId);
            Assert.isNull(deliveryItemEntity,String.format("当前id为[%s]的送货单行数据不存在或已被删除,删除失败！", deLineId));
            deliveryItemEntity.setDeleteFlag(WhetherEnum.YES.getCode());
            if (DeliveryTypeEnum.PLANDELIVERY.getValue().equals(deliveryEntity.getDeliveryType())){
                deliveryPlanItemService.updateByMakeNumV2(deliveryItemEntity.getPlanSrmLineId(),deliveryItemEntity.getDevNum().negate());
            }
            orderClient.updateMakenum(deliveryItemEntity.getSaleItemId(), deliveryItemEntity.getDevNum().negate(), null);
            deliveryItemService.updateById(deliveryItemEntity);
        });
        return true;
    }

    /**
     * 送货单详情
     *
     * @param id
     * @return
     */
    @Override
    public DeliveryEntity getInfo(Long id) {
        DeliveryEntity deliveryEntity = getById(id);
        if (deliveryEntity == null) {
            throw new RRException(String.format("查找不到送货单数据,id为[%s]", id));
        }
        String ifQuality = sysClient.getValueByKeyAndTenantId("ifQuality", deliveryEntity.getTenantId());
        if (StringUtils.isNotBlank(ifQuality)) {
            deliveryEntity.setIfQuality(ifQuality);
        }
        if (StrUtil.isEmptyIfStr(deliveryEntity.getAddress()) || deliveryEntity.getAddress().equals("null")) {
            deliveryEntity.setAddress("");
        }
        List<DeliveryItemEntity> deliveryItemList = deliveryItemService.queryItemById(deliveryEntity.getId());
        deliveryEntity.setDeliveryItemEntityList(deliveryItemList);
        return deliveryEntity;
    }

    /**
     * 送货单打印模板详情
     *
     * @param id
     * @return
     */
    @Override
    public DeliveryEntity getPrintInfo(Long id) {

        DeliveryEntity deliveryEntity = getById(id);
        Assert.isNull(deliveryEntity,String.format("当前id为[%s]的送货单数据不存在或已被删除,打印失败！", id));
        List<DeliveryItemEntity> deliveryItemList = deliveryItemService.queryByList(deliveryEntity.getId());
        if (CollectionUtil.isEmpty(deliveryItemList)){
            throw new RRException("当前送货单没有行数据，打印失败！");
        }
        Map<String, List<DeliveryItemEntity>> printItemMap = deliveryItemList.stream().collect(Collectors.groupingBy(item -> item.getSaleId() + "&" + item.getGoodsId()));
        List<DeliveryItemEntity> deliveryItemPrintList = new ArrayList<>();
        printItemMap.forEach((key, list) -> {
            // 获取明细行打印的主数据
            DeliveryItemEntity deliveryItemMainData = list.get(0);
            // 获取分组后明细行打印的总数量
            deliveryItemMainData.setDevNum(list.stream().map(DeliveryItemEntity::getDevNum).reduce(BigDecimal.ZERO, BigDecimal::add));
            deliveryItemPrintList.add(deliveryItemMainData);
        });
        BigDecimal totalNum = deliveryItemPrintList.stream().map(DeliveryItemEntity::getDevNum).reduce(BigDecimal.ZERO, BigDecimal::add);
        deliveryEntity.setTotal(totalNum);
        deliveryEntity.setDeliveryItemPrintList(deliveryItemPrintList);
        return deliveryEntity;
    }

    /**
     * 送货单审核
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean checkInfo(Long id) {
        DeliveryEntity deliveryEntity = this.getById(id);
        return this.updateById(deliveryEntity);
    }

    @Override
    public PageUtils findDeliveryHomePage(Map<String, Object> params) {
        Long tenantId = commonService.getTenantId();
        Integer isOut = StrUtil.isEmptyIfStr(params.get("isOut")) ? null : Integer.parseInt(params.get("isOut").toString());
        params.remove("tenantId");
        params.remove("vendorId");
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(commonService.getTenantInfo().getEntType())){
            VendorVO vendor = baseClient.getVendorInfoById(tenantId);
            tenantId = ObjectUtil.isNotEmpty(vendor) ? vendor.getTenantId() : 0L;
            params.put("vendorId", vendor.getSoureId());
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        this.convertDeliveryKanBanParams(params);
        List<HashMap<String, Object>> list = getBaseMapper().getDeliveryHomeList(page, params);
        String deliveryHomeDays = sysClient.getValueByKeyAndTenantId("deliveryHomeDays", tenantId);
        if (CollectionUtil.isNotEmpty(list)){
            // 从list中单独取出goodsId字段值并做去重 字段类型为long list的类型为List<HashMap<String, Object>>
            List<Long> goodsIdList = list.stream().map(map -> map.get("goodsId").toString()).distinct().map(Long::parseLong).collect(Collectors.toList());
            List<GoodsVO> goodsList = this.queryGoodsList(goodsIdList);
            list.forEach(map -> {
                //系统参数不为空，数组最早可提前送货日期
                if (!StrUtil.isEmptyIfStr(deliveryHomeDays)) {
                    if (!StrUtil.isEmptyIfStr(map.get("deliveryDate"))){
                        // 将字符串转换成Date对象
                        Date planDate = DateUtil.parse(map.get("deliveryDate") + "", "yyyy-MM-dd");
                        //需要扣减的天数
                        Integer sub = -Integer.parseInt(deliveryHomeDays);
                        //计算提前可送货日期,送货日期+(-需要扣减的天数)
                        Date inAdvanceDeliveryDate = DateUtil.offsetDay(planDate, sub);
                        // 当前送货计划明细行最早可送货日期
                        map.put("inAdvanceDeliveryDate", DateUtil.format(inAdvanceDeliveryDate, "yyyy-MM-dd"));
                    }
                }
                if (!StrUtil.isEmptyIfStr(map.get("orderType"))){
                    JinDieOrderTypeEnum orderType = JinDieOrderTypeEnum.getEnum(Integer.parseInt(map.get("orderType").toString()));
                    map.put("orderBusType",orderType.getType());
                }
                if (!StrUtil.isEmptyIfStr(map.get("materialListNum"))){
                    BigDecimal materialListNum = new BigDecimal(map.get("materialListNum").toString());
                    // 是否委外
                    map.put("isOut", materialListNum.compareTo(BigDecimal.ZERO) > 0 ? WhetherEnum.YES.getValue() : WhetherEnum.NO.getValue());
                }
                if (CollectionUtil.isNotEmpty(goodsList)){
                    GoodsVO goodsVo = goodsList.stream()
                            .filter(goods -> goods.getId().equals(Long.parseLong(map.get("goodsId").toString())))
                            .findFirst().orElse(null);
                    if (ObjectUtil.isNotEmpty(goodsVo)){
                        map.put("warehouseCode", goodsVo.getWarehouseCode());
                        map.put("warehouseName", goodsVo.getWarehouseName());
                    }
                }
            });
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 送货看板导出
     * @param params
     * @return
     */
    @Override
    public List<DeliveryKanbanExportVO> queryExportDeliveryKanBanList(Map<String, Object> params) {
        List<HashMap<String, Object>> list = new ArrayList<>();
        this.convertDeliveryKanBanParams(params);
        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {
            IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
            list = getBaseMapper().getDeliveryHomeList(page,params);
        } else {
            list = getBaseMapper().getDeliveryHomeList(params);
        }
        if (CollectionUtil.isNotEmpty(list)){
            String deliveryHomeDays = sysClient.getValueByKeyAndTenantId("deliveryHomeDays", 3332L);
            list.forEach(map -> {
                //系统参数不为空，数组最早可提前送货日期
                if (!StrUtil.isEmptyIfStr(deliveryHomeDays)) {
                    if (!StrUtil.isEmptyIfStr(map.get("deliveryDate"))){
                        // 将字符串转换成Date对象
                        Date planDate = DateUtil.parse(map.get("deliveryDate") + "", "yyyy-MM-dd");
                        //需要扣减的天数
                        Integer sub = -Integer.parseInt(deliveryHomeDays);
                        //计算提前可送货日期,送货日期+(-需要扣减的天数)
                        Date inAdvanceDeliveryDate = DateUtil.offsetDay(planDate, sub);
                        // 当前送货计划明细行最早可送货日期
                        map.put("inAdvanceDeliveryDate", DateUtil.format(inAdvanceDeliveryDate, "yyyy-MM-dd"));
                    }
                }
            });
            return BeanConverter.convertList(list, DeliveryKanbanExportVO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 送货单当前页or全部导出
     *
     * @param params
     * @return
     */
    @Override
    public List<DeliveryExportVO> exportList(Map<String, Object> params) {
        List<DeliveryEntity> list;
        if (!StrUtil.isBlankIfStr(params.get("exportType"))
                && "0".equals(params.get("exportType") + "")) {
            list =
                    this.page(new Query<DeliveryEntity>().getPage(params), getQueryWrapper(params))
                            .getRecords();
        } else {
            list = this.list(getQueryWrapper(params));
        }

        List<DeliveryExportVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, DeliveryExportVO.class);
        }
        return resultList;
    }

    /**
     * 更改送货单状态并校验制单数量 meng 2021-4-9
     *
     * @param params
     */
    @Override
    //    @Transactional(rollbackFor = Throwable.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public R saveCreateDelivery(Map<String, Object> params) {
        if (params.containsKey("vendorIds") && !StrUtil.isBlankIfStr(params.get("vendorIds"))) {
            DeliveryEntity deliveryEntity = this.getById(Long.parseLong(params.get("id").toString()));
            if (deliveryEntity != null) {
                if (deliveryEntity.getDeStat() == 1) {
                    // 获取送货明细的送货总量
                    List<HashMap<String, Object>> deliveryItemListHashMap =
                            getBaseMapper().getDeliveryItemTotalQuantity(params);

                    // 根据送货明细和销售订单明细关联,在根据送货单id去筛选

                    // 存储送货明细Id数据
                    List<Long> deliveryItemIdList = new ArrayList<>();

                    // 验证部分
                    for (HashMap<String, Object> deliveryItemList : deliveryItemListHashMap) {

                        Long saleItem = Long.parseLong(deliveryItemList.get("saleItemId").toString()); // 销售明细id
                        BigDecimal devNum = (BigDecimal) deliveryItemList.get("deliveryQuantity"); // 送货数量

                        if (devNum.compareTo(BigDecimal.ZERO) == 1) { // 送货数量大于0就校验送货数量是否超出范围

                        } else { // 送货数量等于0就把送货明细删除(存储送货明细Id数据)
                            deliveryItemIdList.add(
                                    Long.parseLong(deliveryItemList.get("deliveryItemId").toString()));
                        }
                    }

                    for (Long deliveryItemId : deliveryItemIdList) { // 送货数量等于0就把送货明细删除
                        DeliveryItemEntity deliveryItemEntity = deliveryItemService.getById(deliveryItemId);
                        if (deliveryItemEntity != null) {
                            deliveryItemEntity.setDeleteFlag(1);
                            deliveryItemService.updateInfo(deliveryItemEntity);
                            // 更新送货数量
                        }
                    }
                    // 更新送货主表订单状态
                    deliveryEntity.setIsEle(Integer.parseInt(params.get("isEle").toString()));
                    deliveryEntity.setLogisticsName(params.get("logisticsName").toString());
                    deliveryEntity.setEleNo(params.get("eleNo").toString());
                    deliveryEntity.setRemark(params.get("remark").toString());
                    if (deliveryEntity.getIsEle() == 0) {
                        deliveryEntity.setLogisticsName(null);
                        deliveryEntity.setEleNo(null);
                    }
                    deliveryEntity.setDeStat(
                            DeliveryDeStatEnum.TOBEISSUED
                                    .getCode()); // 设置DeStat送货状态为1 既DeliveryDeStatEnum.TOBEISSUED.getCode()
                    this.updateById(deliveryEntity);

                    // 保存日志
                    JSONObject saveOperMsg = new JSONObject();
                    saveOperMsg.put("operBill", OperBillEnum.DELIVRY.getValue()); // 类型是送货单
                    saveOperMsg.put("operType", OperTypeEnum.UPDATE.getValue());
                    saveOperMsg.put("soureHeadId", deliveryEntity.getId());
                    saveOperMsg.put("remark", "单据状态为待送货:" + deliveryEntity.getDeNo());
                    saveOperMsg.put("attr1", deliveryEntity.getDeNo());
                    saveOperMsg.put("attr2", commonService.getUserName());
                    saveOperMsg.put("tenantId", commonService.getTenantId());
                    saveOperMsg.put("vendorId", deliveryEntity.getVendorId());
                    baseClient.saveOperMsg(saveOperMsg);

                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("code", 200);
                    resultMap.put("result", deliveryEntity);
                    return R.ok();
                }
            }
        }
        return R.error(500, "修改送货单异常");
    }

    /**
     * 修改送货制单数量 meng 2021-4-12
     *
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public R updateDevNum(Map<String, Object> params) {
        if (params.containsKey("vendorIds") && !StrUtil.isBlankIfStr(params.get("vendorIds"))) {
            DeliveryItemEntity deliveryItemEntity =
                    deliveryItemService.getById(Long.parseLong(params.get("deliveryId").toString()));
            DeliveryEntity deliveryEntity = this.getById(deliveryItemEntity.getDeId());
            if (deliveryEntity.getDeStat() == 0) {
                BigDecimal oldDevNum = deliveryItemEntity.getDevNum();
                BigDecimal newDevNum = new BigDecimal(params.get("devNum") + "");

                // 根据订单明细id获取已发布的送货计划明细数量之和
                HashMap<String, Object> map =
                        getBaseMapper().getPlanNum(deliveryItemEntity.getSaleItemId() + "");
                BigDecimal planNum = new BigDecimal(map.get("planNum") + "");
                // 修改送货单的送货数量时：回写订单的制单数量
                JSONObject jsonObject =
                        orderClient.updateMakenum(
                                deliveryItemEntity.getSaleItemId(), newDevNum.subtract(oldDevNum), planNum);
                if ("500".equals(jsonObject.get("code") + "")) {
                    throw new RRException(jsonObject.get("msg") + "");
                }
                deliveryItemEntity.setDevNum(newDevNum);
                deliveryItemService.updateInfo(deliveryItemEntity);
            }
        }

        return R.ok("修改成功");
    }

    @Override
    public Map<String, Object> getDeliveryCount(Map<String, Object> params) {
        Map<String, Object> map = new HashMap<>();
        boolean isPur = true;
        if (params.containsKey("tenantId") && !StrUtil.isEmptyIfStr(params.get("tenantId"))) { // 采购方进入送货列表
            params.remove("tenantId");
            params.put("tenantId", commonService.getTenantId());
        } else if (params.containsKey("vendorId")
                && !StrUtil.isEmptyIfStr(params.get("vendorId"))) { // 供应商进入送货列表
            params.remove("vendorId");
            params.put("vendorId", commonService.getTenantId());
            isPur = false;
        }
        // 如果传入的
        List<String> dataList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar c = Calendar.getInstance();
        if (null == params.get("startDate") || "".equals(params.get("startDate"))) {
            HashMap<String, Object> startDateAndWaitNum = getBaseMapper().getStartDateAndWaitNum(params);
            try {
                Date sd = sdf.parse(startDateAndWaitNum.get("startDate") + "");
                c.setTime(sd);
                c.add(Calendar.DATE, 1);
//        dataList.add(sdf.format(c.getTime()));
                params.put("startDate", sdf.format(c.getTime()));
            } catch (Exception e) {
                logger.info("转换查询日期失败");
            }
        }
        try {
            Date sd;
            if (params.containsKey("startDate")) {
                sd = sdf.parse(params.get("startDate") + "");
            } else {
                sd = new Date();
            }

            c.setTime(sd);

        } catch (Exception e) {
            logger.info("转换查询日期失败");
        }

        try {
            if (params.get("requiredDeliveryTime") != null && !params.get("requiredDeliveryTime").equals("")) {
                String[] split = params.get("requiredDeliveryTime").toString().split(" 至 ");
                params.put("startRequiredDeliveryTime", split[0] + " 00:00:00");
                params.put("endRequiredDeliveryTime", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("检验搜索日期有误");
            throw new RRException("检验日期输入有误");
        }
        params.put("deliveryType", "1");
        int deliveryHomeCount1 = Integer.parseInt(
                getBaseMapper().getDeliveryHomeCount(params).get(0).get("deliveryHomeCount") + "");
        map.put("deliveryOrderCount", deliveryHomeCount1);
        params.put("deliveryType", "2");
        int deliveryHomeCount = Integer.parseInt(
                getBaseMapper().getDeliveryCount(params).get(0).get("deliveryHomeCount") + "");
        map.put("deliveryPlanCount", deliveryHomeCount);
        params.put("vendorId", commonService.getTenantId());
        params.put("userId", commonService.getUserId());
        map.put("deliveryCartList", deliveryHomeCount1 + deliveryHomeCount);


        return map;
    }

    @Override
    public Map<String, Object> DeliveryCount(Map<String, Object> params) {
        Map<String, Object> map = new HashMap<>();
        boolean isPur = true;
        if (params.containsKey("tenantId") && !StrUtil.isEmptyIfStr(params.get("tenantId"))) { // 采购方进入送货列表
            params.remove("tenantId");
            params.put("tenantId", commonService.getTenantId());
        } else if (params.containsKey("vendorId")
                && !StrUtil.isEmptyIfStr(params.get("vendorId"))) { // 供应商进入送货列表
            params.remove("vendorId");
            params.put("vendorId", commonService.getTenantId());
            isPur = false;
        }
        // 如果传入的
        List<String> dataList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar c = Calendar.getInstance();
        if (null == params.get("startDate") || "".equals(params.get("startDate"))) {
            HashMap<String, Object> startDateAndWaitNum = getBaseMapper().getStartDateAndWaitNum(params);
            try {
                Date sd = sdf.parse(startDateAndWaitNum.get("startDate") + "");
                c.setTime(sd);
                c.add(Calendar.DATE, 1);
//        dataList.add(sdf.format(c.getTime()));
                params.put("startDate", sdf.format(c.getTime()));
            } catch (Exception e) {
                logger.info("转换查询日期失败");
            }
        }
        try {
            Date sd;
            if (params.containsKey("startDate")) {
                sd = sdf.parse(params.get("startDate") + "");
            } else {
                sd = new Date();
            }

            c.setTime(sd);

        } catch (Exception e) {
            logger.info("转换查询日期失败");
        }

        try {
            if (params.get("requiredDeliveryTime") != null && !params.get("requiredDeliveryTime").equals("")) {
                String[] split = params.get("requiredDeliveryTime").toString().split(" 至 ");
                params.put("startRequiredDeliveryTime", split[0] + " 00:00:00");
                params.put("endRequiredDeliveryTime", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("检验搜索日期有误");
            throw new RRException("检验日期输入有误");
        }

        params.put("deliveryType", "1");
        int deliveryHomeCount = Integer.parseInt(
                getBaseMapper().getDeliveryHomeCount(params).get(0).get("deliveryHomeCount") + "");
        map.put("deliveryOrderCount", deliveryHomeCount);
        params.put("deliveryType", "2");
        int deliveryHomeCount1 = Integer.parseInt(
                getBaseMapper().getDeliveryCount(params).get(0).get("deliveryHomeCount") + "");
        map.put("deliveryPlanCount", deliveryHomeCount1);
        params.put("vendorId", commonService.getTenantId());
        params.put("userId", commonService.getUserId());
        int all = deliveryHomeCount + deliveryHomeCount1;
        map.put("deliveryCartList", all);
        return map;
    }

    /***
     * 送货订单关闭
     * @param deliveryClosingVO 送货订单数据
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void deliveryClosing(DeliveryClosingVO deliveryClosingVO) {
        DeliveryEntity deliveryEntity = this.getById(deliveryClosingVO.getDeliveryId());

        deliveryEntity.setRemark("ERP关闭送货数据");
        deliveryEntity.setDeStat(DeliveryDeStatEnum.CLOSE.getValue());
        this.updateById(deliveryEntity);
        List<DeliveryItemEntity> list = deliveryItemService.queryByList(deliveryEntity.getId());
        for (DeliveryItemEntity itemEntity : list) {
            masterItemService.queryDeliveryId(itemEntity.getId());
            //回写计划数据
            DeliveryPlanItemEntity planItemEntity = deliveryPlanItemService.getById(itemEntity.getPlanSrmLineId());
            if (planItemEntity == null) {
                throw new RRException("非法数据，未查询到对应计划的数据 计划ID为" + itemEntity.getPlanSrmLineId());
            }
            if (planItemEntity.getDeleteFlag() == 2) {
                throw new RRException("计划已关闭不能关闭送货！");
            }
            /*处理计划数量回写问题*/
            deliveryPlanItemService.updateByMakeNum(planItemEntity.getId(), itemEntity.getDevNum());
            /*处理库存逻辑问题*/
            String isReduceStock = configService.getValueByKeyAndTenantId("isReduceStock", deliveryEntity.getTenantId().toString());
            if (isReduceStock != null) {
                if ("1".equals(isReduceStock)) {
                    vendorLogicInvService.queryByDefaultNum(deliveryClosingVO.getVendorCode(), itemEntity.getDevNum().negate());
                }
            }
            // 更新回写订单已送货数量
            orderClient.updateNum(itemEntity.getSaleItemId(), itemEntity.getDevNum().negate());
        }
    }

    /**
     * 采购方删除供应商送货单据
     *
     * @param id 送货单id
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean closeDeliveryByTenant(Long id) {
        synchronized (sharedLock){
            DeliveryEntity delivery = this.getById(id);
            if (delivery != null) {
                if (WhetherEnum.YES.getCode().equals(delivery.getDeleteFlag())){
                    throw new RRException("当前送货单已被删除过，无法进行关闭送货单操作");
                }
                List<DeliveryItemEntity> deliveryItemList = deliveryItemService.queryItemById(id);
                if (CollectionUtil.isNotEmpty(deliveryItemList)){
                    deliveryItemList.forEach(deliveryItem -> {
                        int count = inspectionReviewService.count(new LambdaQueryWrapper<InspectionReviewEntity>()
                                .eq(InspectionReviewEntity::getDeliveryNo, delivery.getDeNo())
                                .eq(InspectionReviewEntity::getOrderItemId, deliveryItem.getSaleItemId())
                                .eq(InspectionReviewEntity::getSourceItemType, ReceiptInspectionStatEnum.UNQUALIFIED.getValue())
                                .eq(InspectionReviewEntity::getUnQualifiedNum, deliveryItem.getDevNum())
                        );
                        if (count > 0){
                            throw new RRException("存在检验不合格的数据，不能整单关闭");
                        }
                    });
                }

                delivery.setDeliveryItemEntityList(deliveryItemList);
                delivery.setDeleteFlag(WhetherEnum.YES.getCode());
                deliveryItemService.closeDeliveryItemByTenant(delivery.getId(), delivery.getDeStat());
                this.updateById(delivery);
                if (DeStatEnum.ISSUED.getCode().equals(delivery.getDeStat())) {
                    if (delivery.getTenantId().equals(3332L)){
                        // 将送货单信息传至WMS
                        pushDeliveryToWms(delivery, OperTypeEnum.DELETE.getValue());
                        // 删除送货明细
                        try {
                            uploadSapInTransitQuantity(delivery, 2);
                        } catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                    if (delivery.getTenantId().equals(24739L)){
                        closeDeliveryToGveWms(delivery,  OperTypeEnum.DELETE.getValue());
                    }
                }

                String remark = commonService.getUserName() + "关闭删除了送货单" + delivery.getDeNo();
                this.saveOperMsg(OperBillEnum.DELIVRY.getValue(), OperTypeEnum.DELETE.getValue(), delivery.getId(), remark, delivery.getDeNo());
            }
            return true;
        }
    }

    /**
     * 特定送货单关闭接口方法
     * @param delDeliveryReq
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean specifyCloseDelivery(DelDeliveryReq delDeliveryReq) {
        boolean isTenant = true;
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(commonService.getTenantInfo().getEntType())){
            isTenant = false;
        }
        Map<String,Object> queryDeliveryParam = new HashMap<>();
        if (Objects.isNull(delDeliveryReq.getOpType())){
            throw new RRException("删除操作类型不能为空");
        }
        if (Objects.isNull(delDeliveryReq.getDockSysType())){
            throw new RRException("对接外部接收送货单系统类型不能为空");
        }
        // 删除类型为1-整单删除
        if (DelDeliveryTypeEnum.ALL_DELETED.getValue().equals(delDeliveryReq.getOpType())){
            queryDeliveryParam.put("mainIds", delDeliveryReq.getIds());
        }
        // 删除类型为2-明细行删除
        if (DelDeliveryTypeEnum.LINE_DELETED.getValue().equals(delDeliveryReq.getOpType())){
            if (!isTenant){
                throw new RRException("供应商端不能进行送货单明细行删除操作");
            }
            queryDeliveryParam.put("lineIds", delDeliveryReq.getIds());
        }
        DelDeliveryTypeEnum delDeliveryTypeEnum = DelDeliveryTypeEnum.getEnum(delDeliveryReq.getOpType());
        // 根据以上条件查询出对应的送货单主从表信息
        List<DeliverySlaveVO> deliverySlaveList = deliveryItemService.findDeliverySlaveList(queryDeliveryParam);
        if (CollectionUtil.isEmpty(deliverySlaveList)){
            throw new RRException(String.format("当前删除操作类型为%s,查询不到对应的送货单数据,查询的ID数据为{%s}",delDeliveryTypeEnum.getDesc(),delDeliveryReq.getIds()));
        }
        // 按照送货单号进行分组
        Map<String, List<DeliverySlaveVO>> deliverySlaveMap = deliverySlaveList.stream().collect(Collectors.groupingBy(DeliverySlaveVO::getDeNo));
        List<DeliveryEntity> needUpdateDeliveryNumToSapList = new ArrayList<>();
        deliverySlaveMap.forEach((deNo,deleteList) -> {
            // 获取第一条数据
            DeliverySlaveVO deliverySlaveVO = deleteList.get(0);
            DeliveryEntity delivery = this.getById(deliverySlaveVO.getId());
            // 校验删除数据
            this.deleteCheck(delivery);
            List<DeliveryItemEntity> deliveryItemEntityList = new ArrayList<>();
            // 删除类型为1-整单删除
            if (DelDeliveryTypeEnum.ALL_DELETED.getValue().equals(delDeliveryReq.getOpType())){
                delivery.setDeleteFlag(WhetherEnum.YES.getCode());
            }
            deleteList.forEach(item -> {
                deliveryItemService.closeDeliveryItemById(item.getDeItemId());
                DeliveryItemEntity deliveryItem = deliveryItemService.getById(item.getDeItemId());
                if (Objects.isNull(deliveryItem)){
                    throw new RRException(String.format("查找不到对应的送货单明细信息,明细行id：%s",item.getDeItemId()));
                }
                if (WhetherEnum.YES.getCode().equals(deliveryItem.getDeleteFlag())){
                    throw new RRException(String.format("送货单号第%s行已删除，不能进行二次删除操作",item.getSeq()));
                }
                // 采购订单1000 计划500 送货单500 入库200 退货100（退货时已触发回写） 实际入库100 此时此刻关闭送货单只需要回写300到送货计划和订单上
                BigDecimal devNum = deliveryItem.getDevNum() == null? BigDecimal.ZERO:deliveryItem.getDevNum();
                BigDecimal invNum = deliveryItem.getInvNum() == null? BigDecimal.ZERO:deliveryItem.getInvNum();
                BigDecimal retQty = deliveryItem.getRetQty() == null? BigDecimal.ZERO:deliveryItem.getRetQty();
                BigDecimal unCompetentNum = deliveryItem.getUnCompetentNum() == null?BigDecimal.ZERO:deliveryItem.getUnCompetentNum();
                BigDecimal totalInvNum = invNum.add(retQty);
                // devNum500-invNum100+retQty100=300
                BigDecimal needUpdateNum = devNum.subtract(totalInvNum).subtract(retQty).subtract(unCompetentNum);
                deliveryItem.setDeleteFlag(WhetherEnum.YES.getCode());
                deliveryItemService.updateById(deliveryItem);
                // 回写送货计划
                deliveryPlanItemService.updateByMakeNum(deliveryItem.getPlanSrmLineId(),needUpdateNum);
                // 回写采购订单
                orderClient.updateOrderItemMakeNum(deliveryItem.getSaleItemId(),needUpdateNum,delivery.getDeStat());
                deliveryItem.setDevNum(needUpdateNum);
                deliveryItemEntityList.add(deliveryItem);
            });
            this.updateById(delivery);
            delivery.setDeliveryItemEntityList(deliveryItemEntityList);
            needUpdateDeliveryNumToSapList.add(delivery);
        });
        // 更改其他外围系统数据
        // 更改MES数据
        if (DockSystemTypeEnum.MES_MEICLOUND.getValue().equals(delDeliveryReq.getDockSysType())){
            this.pushDelDeliveryToMes(deliverySlaveMap, delDeliveryReq.getOpType(),3332L);
        }
        // 更改SAP数据
        if (CollectionUtil.isNotEmpty(needUpdateDeliveryNumToSapList)){
            needUpdateDeliveryNumToSapList.forEach(deliveryData -> {
                uploadSapInTransitQuantity(deliveryData, 2);
            });
        }
        return true;
    }

    /**
     * 接口-送货单查询
     *
     * @param deNo
     * @return
     */
    @Override
    public DeliveryResultVO queryDeliveryNo(String deNo) {
        DeliveryEntity delivery = this.getOne(new LambdaQueryWrapper<DeliveryEntity>().eq(DeliveryEntity::getDeNo, deNo).eq(DeliveryEntity::getDeleteFlag, WhetherEnum.NO.getCode()));
        if (Objects.nonNull(delivery)) {
            DeliveryResultVO deliveryResultVO = BeanConverter.convert(delivery, DeliveryResultVO.class);
            deliveryResultVO.setDeliveryNo(delivery.getDeNo());
            deliveryResultVO.setFactory(delivery.getDeptCode());
            List<DeliveryItemEntity> deliveryItemList = deliveryItemService.queryByList(delivery.getId());
            if (CollectionUtil.isNotEmpty(deliveryItemList)) {
                List<DeliveryItemResultVO> deliveryItemVOList = BeanConverter.convertList(deliveryItemList, DeliveryItemResultVO.class);
                deliveryResultVO.setDeliveryItemList(deliveryItemVOList);
            }
            return deliveryResultVO;
        }
        return null;
    }

    /**
     * 下载期初在途送货单数据
     *
     * @return
     */
    @Override
    public List<InTransitDeliveryImportVO> downloadInTransitTemplate() {
        List<InTransitDeliveryImportVO> list = new ArrayList<>();
        return list;
    }

    /**
     * 导入期初在途送货单数据
     *
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean importInTransitDelivery(List<InTransitDeliveryImportVO> list) {
        Long tenantId = commonService.getTenantId();
        if (CollectionUtil.isNotEmpty(list)) {
            Map<String, List<InTransitDeliveryImportVO>> listMap = list.stream().collect(Collectors.groupingBy(delivery -> delivery.getDeptCode() + "-" + delivery.getVendorCode()));
            listMap.forEach((k, v) -> {
                InTransitDeliveryImportVO inTransitDeliveryImportVO = v.get(0);
                DeliveryEntity delivery = new DeliveryEntity();
                delivery.setDeNo(sysClient.getBillNo("dm_delivery"));
                delivery.setTenantId(tenantId);
                delivery.setTenantPId(0L);
                delivery.setIsCall(WhetherEnum.NO.getCode());
                delivery.setDeleteFlag(WhetherEnum.NO.getCode());
                delivery.setDeStat(DeStatEnum.ISSUED.getValue());
                delivery.setDeliveryDate(inTransitDeliveryImportVO.getDeliveryDate());
                delivery.setDeptId(inTransitDeliveryImportVO.getDeptId());
                delivery.setDeptCode(inTransitDeliveryImportVO.getDeptCode());
                delivery.setDeptName(inTransitDeliveryImportVO.getDeptName());
                delivery.setVendorId(inTransitDeliveryImportVO.getVendorId());
                delivery.setVendorCode(inTransitDeliveryImportVO.getVendorCode());
                delivery.setVendorName(inTransitDeliveryImportVO.getVendorName());
                delivery.setVendorFullName(inTransitDeliveryImportVO.getVendorName());
                delivery.setRemark("期初数据");
                delivery.setReserved06(inTransitDeliveryImportVO.getOriginalDeNo());
                this.save(delivery);
                deliveryItemService.importInTransitDeliveryItem(delivery, v);
            });
        }
        return Boolean.TRUE;
    }

    /**
     * 将期初在途送货单数据推送至WMS
     *
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean timedPushDeliveryToWms() {
        Map<String, Object> params = new HashMap<>();
        params.put("limit", 30);
        IPage<DeliveryEntity> page = this.page(new Query<DeliveryEntity>().getPage(params), new LambdaQueryWrapper<DeliveryEntity>()
                .eq(DeliveryEntity::getRemark, "期初数据")
                .eq(DeliveryEntity::getIsCall, WhetherEnum.NO.getCode()));
        if (Objects.nonNull(page)) {
            if (CollectionUtil.isNotEmpty(page.getRecords())) {
                page.getRecords().forEach(data -> {
                    List<DeliveryItemEntity> deliveryItemList = deliveryItemService.queryItemById(data.getId());
                    data.setDeliveryItemEntityList(deliveryItemList);
                    this.pushDeliveryToWms(data, OperTypeEnum.CREATE.getValue());
                });
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 根据物料编码供应商编码获取质保信息
     *
     * @param list
     * @return
     */
    @Override
    public List<VendorWarrantyEntity> findVendorWarranty(List<String> list) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("goodsCode", item.split("-")[0]);
                map.put("vendorCode", item.split("-")[1]);
                mapList.add(map);
            });
        }
        System.out.println(mapList);
        List<VendorWarrantyEntity> resList = new ArrayList<>();
        mapList.forEach(item -> {
            List<VendorWarrantyEntity> warrantyEntityList = vendorWarrantyService.list(new QueryWrapper<VendorWarrantyEntity>().eq("goods_code", item.get("goodsCode")).eq("vendor_code", item.get("vendorCode")));
            if (CollectionUtil.isNotEmpty(warrantyEntityList)) {
                resList.add(warrantyEntityList.get(0));
            }
        });
        return resList;
    }

    /**
     * 分页查询供应商送货达标率报表
     * @param params
     * @return
     */
    @Override
    public PageUtils queryDeCompRateReportPage(Map<String, Object> params) {
        if (StrUtil.isEmptyIfStr(params.get("startDate")) || StrUtil.isEmptyIfStr(params.get("endDate")) ){
            throw new RRException("请选择查询时间范围");
        }
        IPage<DeliveryCompRateReportVO> page = new Query<DeliveryCompRateReportVO>().getPage(params);
        page.setRecords(this.queryDeCompRateReportPageList(page,params));
        return new PageUtils(page);
    }

    /**
     * 导出供应商送货达标率报表
     * @param params
     * @return
     */
    @Override
    public List<DeliveryCompRateReportVO> exportDeliveryCompRate(Map<String, Object> params) {
        if (StrUtil.isEmptyIfStr(params.get("startDate")) || StrUtil.isEmptyIfStr(params.get("endDate")) ){
            throw new RRException("请选择查询时间范围");
        }
        params.put("isExport",WhetherEnum.YES.getValue());
        if ("0".equals(params.get("exportType"))) {
            params.put("isExportPage",WhetherEnum.YES.getValue());
        } else {
            params.put("isExportPage",WhetherEnum.NO.getValue());
        }
        IPage<DeliveryCompRateReportVO> page = new Query<DeliveryCompRateReportVO>().getPage(params);
        if (!StrUtil.isEmptyIfStr(params.get("deptIdListStr"))){
            List<Long> deptIdList = new ArrayList<>();
            String[] deptIdStrArr = params.get("deptIdListStr").toString().split(",");
            if (deptIdStrArr.length > 0){
                for (int i = 0; i < deptIdStrArr.length; i++) {
                    deptIdList.add(Long.parseLong(deptIdStrArr[i]));
                }
            }
            params.remove("deptIdList");
            params.put("deptIdList",deptIdList);
        }
        List<DeliveryCompRateReportVO> list = queryDeCompRateReportPageList(page, params);
        return list;
    }


    /** **************************************** 私有方法 ******************************************* */
    /**
     * ********************************************************************************************
     */

    /**
     * 查询送货单报表
     * @return
     */
    private List<DeliveryCompRateReportVO> queryDeCompRateReportPageList(IPage<DeliveryCompRateReportVO> page,Map<String, Object> params){
        DmCurrencyQuery dmCurrencyQuery = BeanConverter.convert(params, DmCurrencyQuery.class);
        List<DeliveryCompRateReportVO> list = new ArrayList<>();
        // 按送货单主表维度查询供应商送货达标率
        if (DeliveryCompRateTypeEnum.WHOLE_ORDER.getValue().equals(dmCurrencyQuery.getDeliveryCompRateType())){
            if (Objects.nonNull(dmCurrencyQuery.getIsExport())){
                if (Objects.nonNull(dmCurrencyQuery.getIsExportPage()) && WhetherEnum.YES.getCode().equals(dmCurrencyQuery.getIsExportPage())){
                    list = this.getBaseMapper().queryDeCompRateBaseDataList(page, dmCurrencyQuery);
                } else {
                    list = this.getBaseMapper().queryDeCompRateBaseDataList(dmCurrencyQuery);
                }
            } else {
                list = this.getBaseMapper().queryDeCompRateBaseDataList(page, dmCurrencyQuery);
            }
            if (CollectionUtil.isNotEmpty(list)){
                List<DeliveryQuaCountVO> deliveryQuaCountList = this.queryDeliveryInspectionRate(list,dmCurrencyQuery);
                list = calculateDeliveryCompRate(list,deliveryQuaCountList,dmCurrencyQuery);
            }
        }
        // 按明细表维度查询供应商送货达标率
        if (DeliveryCompRateTypeEnum.DETAIL_LINE.getValue().equals(dmCurrencyQuery.getDeliveryCompRateType())){
            if (Objects.nonNull(dmCurrencyQuery.getIsExport())){
                if (Objects.nonNull(dmCurrencyQuery.getIsExportPage()) && WhetherEnum.YES.getCode().equals(dmCurrencyQuery.getIsExportPage())){
                    list = this.getBaseMapper().queryDeItemCompRateBaseDataList(page, dmCurrencyQuery);
                } else {
                    list = this.getBaseMapper().queryDeItemCompRateBaseDataList(dmCurrencyQuery);
                }
            } else {
                list = this.getBaseMapper().queryDeItemCompRateBaseDataList(page, dmCurrencyQuery);
            }
            if (CollectionUtil.isNotEmpty(list)){
                List<DeliveryQuaCountVO> deliveryQuaCountList = this.queryDeliveryInspectionRate(list,dmCurrencyQuery);
                list = calculateDeliveryCompRate(list,deliveryQuaCountList,dmCurrencyQuery);
            }
        }
        return list;
    }

    /**
     * 查询出送货单检验合格不合格数据
     * @return
     */
    private List<DeliveryQuaCountVO> queryDeliveryInspectionRate(List<DeliveryCompRateReportVO> list,DmCurrencyQuery dmCurrencyQuery){
        List<Long> vendorIdList = list.stream().map(DeliveryCompRateReportVO::getVendorId).distinct().collect(Collectors.toList());
        // 每次执行条数
        int batchSize = 10;
        // 原集合开始执行下标节点
        int currentIndex = 0;
        List<DeliveryQuaCountVO> deliveryQuaCountList = new ArrayList<>();
        while (currentIndex < vendorIdList.size()) {
            List<Long> batchList = vendorIdList.subList(currentIndex, Math.min(currentIndex + batchSize, vendorIdList.size()));
            currentIndex += batchSize;
            dmCurrencyQuery.setVendorIdList(batchList);
            List<DeliveryQuaCountVO> nowList = new ArrayList<>();
            if (DeliveryCompRateTypeEnum.WHOLE_ORDER.getValue().equals(dmCurrencyQuery.getDeliveryCompRateType())){
                nowList = inspectionSheetService.queryDeliveryQuaCountList(dmCurrencyQuery);
            } else {
                nowList = inspectionSheetItemService.queryDeliveryItemQuaCountList(dmCurrencyQuery);
            }
            if (CollectionUtil.isNotEmpty(nowList)){
                deliveryQuaCountList.addAll(nowList);
            }
        }
        return deliveryQuaCountList;
    }

    /**
     * 计算供应商送货达标率相关合格率/不合格率数据
     * @param list
     * @param deliveryQuaCountList
     * @param dmCurrencyQuery
     * @return
     */
    private List<DeliveryCompRateReportVO> calculateDeliveryCompRate(List<DeliveryCompRateReportVO> list,List<DeliveryQuaCountVO> deliveryQuaCountList,DmCurrencyQuery dmCurrencyQuery){
        if (CollectionUtil.isNotEmpty(list)){
            if (CollectionUtil.isNotEmpty(deliveryQuaCountList)){
                list.forEach(data -> {
                    List<DeliveryQuaCountVO> calculateDataList = deliveryQuaCountList.stream().filter(qua -> qua.getVendorId().equals(data.getVendorId())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(calculateDataList)){
                        // 送货单不合格集合
                        List<DeliveryQuaCountVO> unQuaList = new ArrayList<>();
                        // 送货单合格集合
                        List<DeliveryQuaCountVO> quaList = new ArrayList<>();
                        if (DeliveryCompRateTypeEnum.WHOLE_ORDER.getValue().equals(dmCurrencyQuery.getDeliveryCompRateType())){
                            // 送货单不合格集合
                            unQuaList = calculateDataList.stream().filter(item -> item.getItemQuaCount().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
                            // 送货单合格集合
                            quaList = calculateDataList.stream().filter(item -> item.getItemQuaCount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                        } else {
                            // 送货单合格集合
                            quaList = calculateDataList.stream().filter(item -> ReceiptInspectionStatEnum.QUALIFIED.getValue().equals(item.getInspectionResults())).collect(Collectors.toList());
                            // 送货单不合格集合
                            unQuaList = calculateDataList.stream().filter(item -> !ReceiptInspectionStatEnum.QUALIFIED.getValue().equals(item.getInspectionResults())).collect(Collectors.toList());
                        }
                        // 某一时间范围供应商送货单整单合格次数
                        data.setQuaCount(new BigDecimal(quaList.size()+""));
                        // 某一时间范围供应商送货单整单不合格次数
                        data.setUnQuaCount(new BigDecimal(unQuaList.size()+""));
                        // 合格率=合格次数/送货次数*100%
                        data.setQuaRate(data.getQuaCount().divide(data.getDeliveryCount(), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
                        // 不合格率=不合格次数/送货次数*100%
                        data.setUnQuaRate(data.getUnQuaCount().divide(data.getDeliveryCount(), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                });
            }
        }
        return list;
    }

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        DeliveryEntity deliveryEntity = this.getById(id);
    }

    /**
     * 审核状态校验
     *
     * @param
     */
    private void checkDeliveryParams(DeliveryEntity deliveryEntity) {
        Assert.isNull(deliveryEntity, "送货单为空");
        if (ObjectUtil.isNotEmpty(deliveryEntity.getId())){
            DeliveryEntity delivery = this.getInfo(deliveryEntity.getId());
            Assert.isNull(delivery, "未找到送货单数据");
        }
        Assert.isNull(deliveryEntity.getDeliveryType(),String.format("送货单来源类型为空"));
        if (CollectionUtil.isEmpty(deliveryEntity.getDeliveryItemEntityList())){
            throw new RRException("送货单明细数据不能为空");
        }
        // 筛选出不为删除状态的数据
        List<DeliveryItemEntity> validItemList = deliveryEntity.getDeliveryItemEntityList().stream().filter(item -> !WhetherEnum.YES.getCode().equals(item.getDeleteFlag())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(validItemList)){
            throw new RRException("送货单明细数据不能为空");
        }
        List<Long> deptIds = validItemList.stream().filter(item -> ObjectUtil.isNotEmpty(item.getDeptId())).map(DeliveryItemEntity::getDeptId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(deptIds)){
            throw new RRException("采购组织不能为空");
        }
        if (deptIds.size() > 1){
            throw new RRException("存在不同采购组织数据");
        }
        DeliveryItemEntity deptInfo = validItemList.stream().filter(item -> item.getDeptId().equals(deptIds.get(0))).findFirst().orElse(null);
        deliveryEntity.setDeptId(deptInfo.getDeptId());
        deliveryEntity.setDeptCode(deptInfo.getDeptCode());
        deliveryEntity.setDeptName(deptInfo.getDeptName());
        deliveryEntity.setValidItemList(validItemList);
        AtomicReference<Integer> idx = new AtomicReference<>(0);

        List<Long> purOrderLineIds = validItemList.stream().filter(item -> JinDieOrderTypeEnum.OUTSOURCING.getValue().equals(item.getOrderType())).map(DeliveryItemEntity::getSaleItemId).distinct().collect(Collectors.toList());
        List<PurItemLineVO> purOrderPickQtyList = orderClient.queryPurOrderPickQtyList(purOrderLineIds);
        Map<Long, PurItemLineVO> purOrderPickQtyMap = purOrderPickQtyList.stream()
                .filter(vo -> ObjectUtil.isNotEmpty(vo.getId()))
                .collect(Collectors.toMap(
                        PurItemLineVO::getId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        validItemList.forEach(item -> {
            idx.set(idx.get() + 1);
            Assert.isNull(item.getDevNum(),String.format("送货单明细中的第%s行的送货数量为空",idx.get()));
            if (StrUtil.isEmptyIfStr(item.getProdBatchNo())){
                throw new RRException(String.format("第%s行的批次号为空",idx.get()));
            }
            Assert.isNull(item.getProdDate(),String.format("送货单明细中的第%s行的生产日期为空",idx.get()));
            if (CollectionUtil.isNotEmpty(purOrderPickQtyMap)){
                PurItemLineVO purItemLineVO = purOrderPickQtyMap.get(item.getSaleItemId());
                if (ObjectUtil.isNotEmpty(purItemLineVO)
                        && ObjectUtil.isNotEmpty(purItemLineVO.getErpOutPickQty())
                        && item.getDevNum().compareTo(purItemLineVO.getErpOutPickQty()) > 0
                ){
                    throw new RRException(StrUtil.format("第{}行的送货数量不能大于订单的委外领料套数{}",idx.get(),purItemLineVO.getErpOutPickQty()));
                }
            }
        });
        checkHasPrice(deliveryEntity,"SAVE");
        checkWarehouse(deliveryEntity);

    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(DeliveryEntity record, Class<?> cls) {
        ValidatorUtils.validateEntity(record, cls);
        if (CollectionUtils.isEmpty(record.getDeliveryItemEntityList())) {
            throw new RRException("送货明细数据不能为空");
        }
    }

    /**
     * 获取查询条件
     *
     * @param
     */
    private QueryWrapper<DeliveryEntity> getQueryWrapper(Map<String, Object> params) {
        QueryWrapper<DeliveryEntity> queryWrapper = new QueryWrapper<>();
        if (!StrUtil.isEmptyIfStr(params.get("deNo"))) {
            queryWrapper.eq("de_no", params.get("deNo"));
        }

        queryWrapper.orderByAsc("id");
        return queryWrapper;
    }

    @Override
    public JSONObject saveOperMsg(
            int operBill, int operType, Long soureHeadId, String remark, String attr1) {
        /**
         * 保存业务操作日志 oper_bill int 生成类型 1-询价单；2-报价单;3-采购订单;4-销售订单;5-送货单;6-收货单;7-退货单;8-对账单;9-发票单 oper_type
         * 操作类型 1-创建；2-发布;3-接受;4-提交;具体以单据类型确定 soure_head_id 来源单据主id remark :操作说明 查看采购订单PO20210323000003
         */
        JSONObject jsonObject1 = null;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tenantId", commonService.getTenantId());
            jsonObject.put("operBill", operBill);
            jsonObject.put("operType", operType);
            jsonObject.put("soureHeadId", soureHeadId);
            jsonObject.put("remark", remark);
            jsonObject.put("attr1", attr1);
            jsonObject.put("attr2", commonService.getUserName());
            jsonObject1 = baseClient.saveOperMsg(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonObject1;
    }

    /**
   * 确认送货并且更改销售明细送货状态 meng
   */
  @Override
  @GlobalTransactional(rollbackFor = Exception.class)
  public synchronized Long  updateDeliveryDeStat(Long id) {

    // 从params中获取到delivery对象并通过JSONobject进行转换赋值到DeliveryEntity delivery对象中
      DeliveryEntity delivery = this.getById(id);
      if (Objects.isNull(delivery)) {
          throw new RRException(String.format("数据为空"));
      }
      if (delivery.getDeStat().equals(2)){
          throw new RRException(String.format("%s送货单号已确认送货",delivery.getDeNo()));
      }
      if (ObjectUtil.isEmpty(delivery.getDeliveryDate())){
          delivery.setDeliveryDate(new Date());
      }
      // 根据送货Id查询其中所有的送货单明细
      List<DeliveryItemEntity> deliveryItemList = deliveryItemService.list(new QueryWrapper<DeliveryItemEntity>()
              .eq("de_id", delivery.getId())
              .lambda().eq(DeliveryItemEntity::getDeleteFlag,WhetherEnum.NO.getCode())
      );
      delivery.setDeliveryItemEntityList(deliveryItemList);
      delivery.setValidItemList(deliveryItemList);
      // 检查价格
//      checkHasPrice(delivery,"CONFIRM");
      // 将送货单的状态改为已发出状态（2） ---> DeStatEnum.ISSUED.getValue()
      delivery.setDeStat(DeStatEnum.ISSUED.getCode());
      delivery.setAuditTime(new Date());
      for (DeliveryItemEntity itemEntity : deliveryItemList) {
          itemEntity.setUnInvNum(itemEntity.getDevNum());
      }
      List<PurItemLineVO> purItemLineList = new ArrayList<>();
      // 通过deliveryItemList.forEach遍历更新回写订单的已送货数量
      deliveryItemList.forEach(deliveryItem -> {
          PurItemLineVO purItemVo = orderClient.getNotClosedPurItemVoById(deliveryItem.getSaleItemId());
          if (purItemVo == null){
              throw new RRException(String.format("采购订单[%s]-[%s]明细行物料[%s]数据为空",deliveryItem.getSaleNo(),deliveryItem.getSaleSeq(),deliveryItem.getGoodsErpCode()));
          }
          // 更新回写订单已送货数量
          PurItemLineVO purItemLineVO = orderClient.updateFixNum(deliveryItem.getSaleItemId(), deliveryItem.getDevNum());
          deliveryItem.setErpOrderItemId(purItemVo.getSourceItemId());
          purItemLineList.add(purItemLineVO);
      });
      this.updateById(delivery);
      // 推送送货单数据给WMS
      pushDeliveryToGveWms(delivery, OperTypeEnum.CREATE.getValue());
      try {
          purchaseOrderJoggleService.batchUpdateOrder(purItemLineList,delivery.getTenantId());
      } catch (Exception e) {
          e.printStackTrace();
          logger.error("修改ERP采购订单供应商已确认发货数量异常："+e.getMessage());
      }
      // 发送站内信息及邮件通知采购员
      this.sendConfirmDeliveryMsgToPur(delivery);
      return delivery.getId();
  }

    /**
     * 供应商确认送货后发送站内信及邮件通知采购员
     * @param delivery
     */
    private void sendConfirmDeliveryMsgToPur(DeliveryEntity delivery) {
        // 发送站内信
        JSONObject mainJson = new JSONObject();
        mainJson.put("tenantId",delivery.getTenantId());
        mainJson.put("userId",0L);
        mainJson.put("content",delivery.getVendorName()+"确认送货了送货单"+delivery.getDeNo());
        mainJson.put("menuTitle","送货单详情");
        mainJson.put("url","dm/delivery/tenant?id=" + delivery.getId());
        sysClient.sendMail(mainJson);
        // 发送邮件通知
        try {
            List<UserSimpleVO> sysUserVOS = sysClient.queryUserByNames(delivery.getTenantId(), delivery.getPurchaserName());
            if (CollectionUtil.isNotEmpty(sysUserVOS) && !StrUtil.isEmptyIfStr(sysUserVOS.get(0).getUserEmail())) {
                EmailMessageVo emailMessageVo = new EmailMessageVo();
                emailMessageVo.setTenantId(delivery.getTenantId());
                emailMessageVo.setTitle("您有一封新的邮件信息");
                String content = delivery.getPurchaserName() + ",您好：\n" +
                        "<br>\n" +
                        "<br>供应商:" + delivery.getVendorName() + "确认送货了送货单(" + delivery.getDeNo() + ")，请知悉！ \n" +
                        "<br>\n" +
                        "<br>请您及时查阅、处理！ 如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n" +
                        "<br>\n";
                emailMessageVo.setContent(content);
                emailMessageVo.setEmail(sysUserVOS.get(0).getUserEmail());
                baseClient.customSendEmail(emailMessageVo);
            }
        } catch (Exception e) {
            logger.error("发送邮件失败：{}",e.getMessage(),e);
        }
    }

    public void sendCreateEmail(DeliveryEntity deliveryEntity) {
        JSONObject tenantJson = baseClient.getCompanyInMail(deliveryEntity.getTenantId());
        String tenantNames = "";
        if (!tenantJson.isEmpty()) {
            tenantNames = tenantJson.get("company").toString();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", deliveryEntity.getTenantId()); // 采购方租户Id
        jsonObject.put("title", "送货单-发布-" + deliveryEntity.getVendorName()); // 发送方租户名称
        jsonObject.put("receiver", tenantNames); // 收件企业
        jsonObject.put("receiverEntId", deliveryEntity.getTenantId()); // 收件企业租户ID
        jsonObject.put("sender", deliveryEntity.getVendorName()); // 发件企业
        jsonObject.put("orderType", "送货单"); // 单据类型
        jsonObject.put("orderStat", "已发出"); // 单据状态
        jsonObject.put("orderNo", deliveryEntity.getDeNo()); // 单据号
        jsonObject.put("roleCode", "");
        jsonObject.put("setingCode", "newDelivery"); // 通知编码(业务节点标识)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        jsonObject.put("publishDate", sdf.format(new Date()));
        // 在base_conf表根据采购方tenant_id查询 邮箱服务信息
//      String emailConf = sysClient.getValueByKeyAndTenantId("emailConf", deliveryEntity.getTenantId());
//      Map<String,Object> params = new HashMap<>();
//      params.put("tenantId",deliveryEntity.getTenantId());
//      params.put("vendorCode",deliveryEntity.getVendorCode());
//      Map<String, Object> vendorByCode = getBaseMapper().findVendorByCode(params);
//      Map<String,Object> findUserCondition = new HashMap<>();
//      findUserCondition.put("tenantId",deliveryEntity.getTenantId());
//      if(!StrUtil.isBlankIfStr(vendorByCode.get("purId"))) {
//          findUserCondition.put("purId", vendorByCode.get("purId").toString());
//          Map<String, Object> userEmail = getBaseMapper().findUserEmail(findUserCondition);
//          jsonObject.put("receiveEmail", userEmail.get("userEmail"));
//          baseClient.sendCreateEmail(jsonObject);
//      }
    }

    /**
     * 根据单号返回数据
     *
     * @param params
     * @return
     */
    @Override
    public JSONObject deliveryNoSearch(Map<String, Object> params) {
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonList = new JSONObject();
        List<HashMap<String, Object>> deliveryList = getBaseMapper().findByDeNoInfo(params);
        if (deliveryList.size() != 0) {
            HashMap<String, Object> hashMap = deliveryList.get(0);
            jsonList.put("tenantPId", hashMap.get("deliveryTenantPId"));
            jsonList.put("tenantId", hashMap.get("deliveryTenantId"));
            jsonList.put("id", hashMap.get("deliveryId"));
            jsonList.put("deNo", hashMap.get("deNo")); // 送货单号
            jsonList.put("vendorId", hashMap.get("deliveryVendorId")); // 供应商
            jsonList.put("vendorCode", hashMap.get("deliveryVendorCode"));
            jsonList.put("vendorName", hashMap.get("deliveryVendorName"));
            jsonList.put("deptName", hashMap.get("deptName")); // 业务部门
            jsonList.put("isPrint", hashMap.get("isPrint")); // 是否打印
            jsonList.put("printCount", hashMap.get("printCount")); // 是否打印
            jsonList.put("deStat", hashMap.get("deStat")); // 订单状态
            jsonList.put("isEle", hashMap.get("isEle"));
            jsonList.put("logisticsName", hashMap.get("logisticsName"));
            jsonList.put("eleNo", hashMap.get("eleNo")); // 快递单号
            jsonList.put("remark", hashMap.get("remark"));
            jsonList.put("deleteFlag", hashMap.get("deleteFlag"));
            jsonList.put("see", hashMap.get("see"));
            jsonList.put("tenantName", hashMap.get("tenantName")); // 组织名称
            jsonList.put("createId", hashMap.get("createId"));
            jsonList.put("creater", hashMap.get("creater"));
            jsonList.put("createDate", hashMap.get("createDate"));
            jsonList.put("modifiId", hashMap.get("modifiId"));
            jsonList.put("modifier", hashMap.get("modifier"));
            jsonList.put("modifyDate", hashMap.get("modifyDate"));
            jsonList.put("deliveryDate", hashMap.get("deliveryDate"));
            jsonList.put("documentDate", hashMap.get("documentDate"));
            jsonList.put("sapDeNo", hashMap.get("sapDeNo"));
            for (HashMap<String, Object> delivery : deliveryList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("tenantPId", delivery.get("deliveryItemTenantPId"));
                jsonObject.put("tenantId", delivery.get("deliveryItemTenantId"));
                jsonObject.put("deId", delivery.get("deId"));
                jsonObject.put("saleId", delivery.get("saleId"));
                jsonObject.put("saleItemId", delivery.get("saleItemId"));
                jsonObject.put("saleNo", delivery.get("saleNo")); // 来源单号
                jsonObject.put("id", delivery.get("deliveryItemId"));
                jsonObject.put("seq", delivery.get("seq")); // 序号
                jsonObject.put("deliveryDate", delivery.get("deliveryDate"));
                jsonObject.put("taxesType", delivery.get("taxesType"));
                jsonObject.put("invoiceType", delivery.get("invoiceType"));
                jsonObject.put("temDate", delivery.get("temDate")); // 暂收时间
                jsonObject.put("goodsId", delivery.get("goodsId"));
                jsonObject.put("goodsErpCode", delivery.get("goodsErpCode"));
                jsonObject.put("goodsCode", delivery.get("goodsCode"));
                jsonObject.put("goodsName", delivery.get("goodsName"));
                jsonObject.put("goodsModel", delivery.get("goodsModel"));
                jsonObject.put("goodsClassName", delivery.get("goodsClassName"));
                jsonObject.put("uomId", delivery.get("uomId"));
                jsonObject.put("uomCode", delivery.get("uomCode"));
                jsonObject.put("uomName", delivery.get("uomName"));
                jsonObject.put("rateId", delivery.get("rateId"));
                jsonObject.put("rateName", delivery.get("rateName"));
                jsonObject.put("rateVal", delivery.get("rateVal"));
                jsonObject.put("currencyId", delivery.get("currencyId"));
                jsonObject.put("currencyName", delivery.get("currencyName")); // 币别
                jsonObject.put("purEmployeeName", delivery.get("purEmployee")); // 采购员
                jsonObject.put("saleEmployeeName", delivery.get("saleEmployee")); // 销售员
                jsonObject.put("sourceNo", delivery.get("sourceNo")); // 来源单号
                jsonObject.put("gstPrice", delivery.get("gstPrice"));
                jsonObject.put("taxPrice", delivery.get("taxPrice"));
                jsonObject.put("docName", delivery.get("docName"));
                jsonObject.put("docUrl", delivery.get("docUrl"));
                jsonObject.put("isPrint", delivery.get("deliveryItemIsPrint"));
                jsonObject.put("printCount", delivery.get("deliveryItemPrintCount"));
                jsonObject.put("remark", delivery.get("deliveryItemRemark"));
                jsonObject.put("deleteFlag", delivery.get("deliveryItemDeleteFlag"));
                jsonObject.put(
                        "bigPackStandardNum",
                        delivery.get("bigPackStandardNum") == null ? 0 : delivery.get("bigPackStandardNum"));
                jsonObject.put(
                        "smallPackStandardNum",
                        delivery.get("smallPackStandardNum") == null
                                ? 0
                                : delivery.get("smallPackStandardNum"));
                jsonObject.put(
                        "bigPackLabelNum",
                        delivery.get("bigPackLabelNum") == null ? 0 : delivery.get("bigPackLabelNum"));
                jsonObject.put(
                        "smallPackLabelNum",
                        delivery.get("smallPackLabelNum") == null ? 0 : delivery.get("smallPackLabelNum"));
                jsonObject.put(
                        "bigPackMantissa",
                        delivery.get("bigPackMantissa") == null ? 0 : delivery.get("bigPackMantissa"));
                jsonObject.put(
                        "smallPackMantissa",
                        delivery.get("smallPackMantissa") == null ? 0 : delivery.get("smallPackMantissa"));
                jsonObject.put(
                        "purchaseConfirm",
                        delivery.get("purchaseConfirm") == null ? 0 : delivery.get("purchaseConfirm"));
                jsonObject.put("vendorDocUrl", delivery.get("vendorDocUrl"));
                jsonObject.put("vendorDocName", delivery.get("vendorDocName"));
                jsonObject.put("gstAmount", delivery.get("gstAmount")); // 税收总金额
                jsonObject.put(
                        "taxAmount", delivery.get("taxAmount") == null ? 0 : delivery.get("taxAmount"));
                jsonObject.put("orderType", delivery.get("orderType"));
                jsonObject.put("qualityType", delivery.get("qualityType"));
                jsonObject.put("saleSeq", delivery.get("saleSeq"));
                jsonObject.put("goodsClassCode", delivery.get("goodsClassCode"));
                jsonObject.put("werks", delivery.get("werks"));
                jsonObject.put("orderPriceUom", delivery.get("orderPriceUom"));
                jsonObject.put("priceUom", delivery.get("priceUom"));
                jsonObject.put("devNum", delivery.get("devNum")); // 送货数量
                jsonObject.put("temNum", delivery.get("temNum")); // 暂收数量
                jsonObject.put("unNum", delivery.get("unNum")); // 未收数量
                jsonObject.put("orderNum", delivery.get("orderNum")); // 订单总数量
                jsonObject.put("invNum", delivery.get("invNum")); // 验收入库数
                jsonObject.put("unInvNum", delivery.get("unInvNum")); // 未验收数
                jsonObject.put("barcodeType", delivery.get("barcodeType")); // 答交日期
                jsonObject.put("warehouseCode", delivery.get("warehouseCode")); // 仓库编码
                jsonObject.put("warehouseName", delivery.get("warehouseName")); // 仓库地址
                list.add(jsonObject);
            }
        }
        jsonList.put("list", list);
        return jsonList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean purchasePlanInsert(Map<String, Object> params) {
        ProcurementPlanEntity procurementPlanEntity = new ProcurementPlanEntity();
        procurementPlanEntity.setTenantId(Long.valueOf(params.get("tenantId") + ""));
        // 计划订单号
        procurementPlanEntity.setProcurementNo(params.get("procurementNo") + "");
        // 供应商数据
        if (params.get("vendorId") != null) {
            procurementPlanEntity.setVendorId(Long.valueOf(params.get("vendorId") + ""));
            procurementPlanEntity.setVendorCode(params.get("vendorCode") + "");
            procurementPlanEntity.setVendorName(params.get("vendorName") + "");
        }
        // 物料信息
        procurementPlanEntity.setGoodsId(Long.valueOf(params.get("goodsId") + ""));
        procurementPlanEntity.setGoodsErpCode(params.get("goodsErpCode") + "");
        procurementPlanEntity.setGoodsName(params.get("goodsName") + "");
        // 计划数量
        procurementPlanEntity.setOrderNum(new BigDecimal(params.get("orderNum") + ""));
        // 计划工厂
        procurementPlanEntity.setWerks(params.get("werks") + "");
        // 计量单位
        procurementPlanEntity.setUomId(Long.valueOf(params.get("uomId") + ""));
        procurementPlanEntity.setUomCode(params.get("uomCode") + "");
        procurementPlanEntity.setUomName(params.get("uomName") + "");
        // 日期转换
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date deliveryDate = dateFormat.parse(params.get("deliveryDate") + "");
            procurementPlanEntity.setDeliveryDate(deliveryDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (params.get("receivingDay") != null) {
            procurementPlanEntity.setReceivingDay(Integer.valueOf(params.get("receivingDay") + ""));
        } else {
            procurementPlanEntity.setReceivingDay(0);
        }
        if (params.get("deliveryDay") != null) {
            procurementPlanEntity.setDeliveryDay(Integer.valueOf(params.get("deliveryDay") + ""));
        } else {
            procurementPlanEntity.setDeliveryDay(0);
        }
        return procurementPlanService.save(procurementPlanEntity);
    }

  /**
   * SAP采购计划根据单号查询
   *
   * @param params
   * @return
   */
  @Override
  public String purchasePlanNo(Map<String, Object> params) {
    List<ProcurementPlanEntity> list =
            procurementPlanService.list(
                    new QueryWrapper<ProcurementPlanEntity>()
                            .eq("tenant_id", params.get("tenantId"))
                            .eq("procurement_no", params.get("procurementNo") + ""));
    //        logger.info(list+"---------lists-------");
    if (list.size() != 0) {
      return list.get(0).getProcurementNo();
    }
    return "";
  }


  @Override
  public String deliveryMasterNo(Map<String, Object> params) {
    CommonService commonService = (CommonService) SpringContextUtils.getBean("commonBizService");
    commonService.setTaskUser(Long.valueOf(params.get("tenantId") + ""));
    List<MasterEntity> list =
            masterService.list(
                    new QueryWrapper<MasterEntity>()
                            .eq("tenant_id", commonService.getTenantId())
                            .eq("master_no", params.get("masterNo") + ""));
    if (list.size() != 0) {
      return list.get(0).getMasterNo();
    }
    return "";
  }

  /**
   * 通过deNo来查找送货单相关信息，来添加让步接收单明细
   *
   * @param params
   * @return
   */
  @Override
  @Transactional(rollbackFor = Throwable.class)
  public List<HashMap<String, Object>> findByDeNoAddCompromise(Map<String, Object> params) {
    return getBaseMapper().findByDeNoAddCompromise(params);
  }

  /**
   * MES通过送货单号查询，并传一个判断是否需要让步接收的判断值
   * @param params
   * @return
   */
  @Override
  @Transactional(rollbackFor = Throwable.class)
  public JSONObject findByDeNoJudge(Map<String, Object> params) {
    JSONObject jsonObject = new JSONObject();
    List<JSONObject> list = new ArrayList<>();
    List<HashMap<String, Object>> judgeList = getBaseMapper().findByDeNoJudge(params);
    if (params.get("isCompromise").equals(1)) {
      HashMap<String, Object> updateParam = judgeList.get(0);
      // 通过this.getById获取updateParam参数中的送货单id并返回到实体类中
      DeliveryEntity deliveryEntity =
              this.getById(Long.parseLong(updateParam.get("deliveryId").toString()));
      deliveryEntity.setIsCompromise(
              IsCompromiseEnum.YES.getCode()); // 假如传入params.get("isCompromise")值为1 就将该送货单主表设置为需要让步接收的状态
      DeliveryItemEntity deliveryItemEntity =
              deliveryItemService.getById(Long.parseLong(updateParam.get("deliveryItemId").toString()));
      deliveryItemEntity.setIsCompromise(
              IsCompromiseEnum.YES
                      .getCode()); // 假如传入params.get("isCompromise")值为1 就将该送货单明细表设置为需要让步接收的状态
      this.updateById(deliveryEntity);
      deliveryItemService.updateById(deliveryItemEntity);
    }
    if (judgeList.size() != 0) {
      HashMap<String, Object> hashMap = judgeList.get(0);
      jsonObject.put("tenantPId", hashMap.get("deliveryTenantPId"));
      jsonObject.put("tenantId", hashMap.get("deliveryTenantId"));
      jsonObject.put("id", hashMap.get("deliveryId"));
      jsonObject.put("deNo", hashMap.get("deNo"));
      jsonObject.put("vendorId", hashMap.get("deliveryVendorId"));
      jsonObject.put("vendorCode", hashMap.get("deliveryVendorCode"));
      jsonObject.put("vendorName", hashMap.get("deliveryVendorName"));
      jsonObject.put("vendorCode", hashMap.get("deliveryVendorCode"));
      if (params.get("isCompromise").equals(1)) {
        jsonObject.put("isCompromise", IsCompromiseEnum.YES.getCode());
      }
      if (params.get("isCompromise").equals(0)) {
        jsonObject.put("isCompromise", IsCompromiseEnum.NO.getCode());
      }
      for (HashMap<String, Object> delivery : judgeList) {
        JSONObject object = new JSONObject();
        object.put("tenantPId", Long.parseLong(delivery.get("deliveryTenantPId").toString()));
        object.put("tenantId", Long.parseLong(delivery.get("deliveryTenantId").toString()));
        object.put("deId", Long.parseLong(delivery.get("deId").toString()));
        object.put("saleId", Long.parseLong(delivery.get("saleId").toString()));
        object.put("saleNo", delivery.get("saleNo").toString());
        object.put("saleSeq", delivery.get("saleSeq").toString());
        object.put("Id", Long.parseLong(delivery.get("deliveryItemId").toString()));
        object.put("goodsId", Long.parseLong(delivery.get("goodsId").toString()));
        object.put("goodsErpCode", delivery.get("goodsErpCode").toString());
        object.put("goodsName", delivery.get("goodsName").toString());
        object.put("goodsModel", delivery.get("goodsModel").toString());
        if (params.get("isCompromise").equals(1)) {
          object.put("isCompromise", IsCompromiseEnum.YES.getCode());
        }
        if (params.get("isCompromise").equals(0)) {
          object.put("isCompromise", IsCompromiseEnum.NO.getCode());
        }
        list.add(object);
      }
    }
    jsonObject.put("list", list);
    jsonObject.put("judgeList", judgeList);
    return jsonObject;
  }

  /**
   * 从MES暂收时根据清点数量赋值到SRM送货单中的暂收数量
   *
   * @param params
   * @return
   */
  @Override
  @GlobalTransactional(rollbackFor = Exception.class)
  public JSONObject saveReceiptNum(Map<String, Object> params) {
    JSONObject jsonObject = new JSONObject();
    List<JSONObject> list = new ArrayList<>();
    Map<String, Object> map = new HashMap<>();
    //查询需要暂收的送货单单据
    List<HashMap<String, Object>> judgeList = getBaseMapper().findByDeNoJudge(params);
    //数据不为空
    if (CollectionUtil.isNotEmpty(judgeList)) {
      HashMap<String, Object> hashMap = judgeList.get(0);
      jsonObject.put("tenantPId", hashMap.get("deliveryTenantPId"));
      jsonObject.put("tenantId", hashMap.get("deliveryTenantId"));
      jsonObject.put("id", hashMap.get("deliveryId"));
      jsonObject.put("deNo", hashMap.get("deNo"));
      jsonObject.put("vendorId", hashMap.get("deliveryVendorId"));
      jsonObject.put("vendorCode", hashMap.get("deliveryVendorCode"));
      jsonObject.put("vendorName", hashMap.get("deliveryVendorName"));
      for (HashMap<String, Object> delivery : judgeList) {
        JSONObject object = new JSONObject();
        object.put("tenantPId", Long.parseLong(delivery.get("deliveryTenantPId").toString()));
        object.put("tenantId", Long.parseLong(delivery.get("deliveryTenantId").toString()));
        object.put("deId", Long.parseLong(delivery.get("deId").toString()));//送货单id
        object.put("saleId", Long.parseLong(delivery.get("saleId").toString()));//采购订单id
        object.put("saleNo", delivery.get("saleNo").toString());//采购订单号
        object.put("saleSeq", delivery.get("saleSeq").toString());//采购订单序号
        object.put("Id", Long.parseLong(delivery.get("deliveryItemId").toString()));//送货单明细行id
        object.put("goodsId", Long.parseLong(delivery.get("goodsId").toString()));//物料id
        object.put("goodsErpCode", delivery.get("goodsErpCode").toString());//ERP物料编码
        object.put("goodsName", delivery.get("goodsName").toString());//物料名称
        object.put("goodsModel", delivery.get("goodsModel").toString());//物料型号
        object.put("temNum", params.get("temNum").toString()); // 暂收数量
        object.put("unInvNum", params.get("temNum").toString()); // 未验收数量 = 暂收数量
        BigDecimal devNum = new BigDecimal(delivery.get("devNum").toString()); // 送货数量
        BigDecimal temNum = new BigDecimal(params.get("temNum").toString()); // 本次暂收数量
        BigDecimal unInvNum = new BigDecimal(params.get("temNum").toString()); // 本次未验收数量
        BigDecimal retNum = devNum.subtract(temNum);//暂退数量 = 送货数量-本次暂收数量
        object.put("retNum", retNum.toString());
        list.add(object);
        BigDecimal invNum = new BigDecimal(delivery.get("invNum").toString()); // 验收已入库
        BigDecimal alreadyTemNum = new BigDecimal(delivery.get("temNum").toString()); // 送货单已暂收数量
        BigDecimal alreadyRetNum = new BigDecimal(delivery.get("retNum").toString()); // 送货单已暂退数量
        // 还剩可暂收数量=送货数量-验收入库数量-已暂收数量-已暂退数量
        BigDecimal canTemNum = devNum.subtract(invNum)
                        .subtract(alreadyTemNum)
                        .subtract(alreadyRetNum);
        if (temNum.compareTo(canTemNum) == 1) { // 当前暂收中传值的清点数量(暂收数量)不能  大于 送货单可暂收数量
          throw new RRException("本次暂收数量不能大于送货单目前的可暂收数量,目前可暂收数量为:" + canTemNum);
        }
        MasterEntity masterEntity = new MasterEntity();
        masterEntity.setMasterNo(sysClient.getBillNo("dm_master"));
        masterEntity.setMasterStat(MasterStatEnum.REVIEWED.getCode()); // 默认为已审核
        masterEntity.setTenantId(Long.parseLong(delivery.get("deliveryTenantId").toString()));
        masterEntity.setTenantPId(0L);
        masterEntity.setTenantName(delivery.get("tenantName").toString());
        masterEntity.setVendorId(Long.parseLong(delivery.get("deliveryVendorId").toString()));
        masterEntity.setVendorCode(delivery.get("deliveryVendorCode").toString());
        masterEntity.setVendorName(delivery.get("deliveryVendorName").toString());
        masterEntity.setMasterType(MasterTypeEnum.PROREC.getCode()); // 暂收单据
        masterEntity.setDeleteFlag(WhetherEnum.NO.getCode());
        masterService.save(masterEntity);
        MasterItemEntity masterItemEntity = new MasterItemEntity();
        masterItemEntity.setMasterId(masterEntity.getId());
        masterItemEntity.setTenantPId(0L);
        masterItemEntity.setTenantId(Long.parseLong(delivery.get("deliveryTenantId").toString()));
        masterItemEntity.setSaleId(Long.parseLong(delivery.get("saleId").toString()));
        masterItemEntity.setSaleItemId(Long.parseLong(delivery.get("saleItemId").toString()));
        masterItemEntity.setSaleNo(delivery.get("saleNo").toString());
        masterItemEntity.setSaleSeq(delivery.get("saleSeq").toString());
        masterItemEntity.setDeItemId(Long.parseLong(delivery.get("deliveryItemId").toString()));
        masterItemEntity.setDeNo(delivery.get("deNo").toString());
        masterItemEntity.setDeSeq(delivery.get("seq").toString());
        masterItemEntity.setSeq("1");
        masterItemEntity.setGoodsId(Long.parseLong(delivery.get("goodsId").toString()));
        masterItemEntity.setGoodsErpCode(delivery.get("goodsErpCode").toString());
        masterItemEntity.setGoodsCode(delivery.get("goodsErpCode").toString());
        masterItemEntity.setGoodsName(delivery.get("goodsName").toString());
        masterItemEntity.setGoodsModel(delivery.get("goodsModel").toString());
        masterItemEntity.setMasterNum(temNum);
        masterItemEntity.setMasterDate(new Date()); // 暂收日期
        masterItemEntity.setTaxPrice(new BigDecimal("0.000000")); // 不含税价格
        masterItemEntity.setGstPrice(new BigDecimal("0.000000")); // 含税价格
        masterItemService.save(masterItemEntity);
        if (retNum.compareTo(BigDecimal.ZERO) == 0) {
        } else {
            // 暂退数量不为0时
            MasterEntity masterEntityTwo = new MasterEntity();
            masterEntityTwo.setMasterNo(sysClient.getBillNo("dm_reject"));
            masterEntityTwo.setMasterStat(MasterStatEnum.REVIEWED.getCode()); // 默认为已审核
            masterEntityTwo.setTenantId(Long.parseLong(delivery.get("deliveryTenantId").toString()));
            masterEntityTwo.setTenantPId(0L);
            masterEntityTwo.setTenantName(delivery.get("tenantName").toString());
            masterEntityTwo.setVendorId(Long.parseLong(delivery.get("deliveryVendorId").toString()));
            masterEntityTwo.setVendorCode(delivery.get("deliveryVendorCode").toString());
            masterEntityTwo.setVendorName(delivery.get("deliveryVendorName").toString());
            masterEntityTwo.setMasterType(MasterTypeEnum.RETTEM.getCode()); // 暂退单据
            masterEntity.setDeleteFlag(WhetherEnum.NO.getCode());
            masterService.save(masterEntityTwo);
            MasterItemEntity masterItemEntityTwo = new MasterItemEntity();
            masterItemEntityTwo.setMasterId(masterEntityTwo.getId());
            masterItemEntityTwo.setTenantPId(0L);
            masterItemEntityTwo.setTenantId(
                    Long.parseLong(delivery.get("deliveryTenantId").toString()));
            masterItemEntityTwo.setSaleId(Long.parseLong(delivery.get("saleId").toString()));
            masterItemEntityTwo.setSaleItemId(Long.parseLong(delivery.get("saleItemId").toString()));
            masterItemEntityTwo.setSaleNo(delivery.get("saleNo").toString());
            masterItemEntityTwo.setSaleSeq(delivery.get("saleSeq").toString());
            masterItemEntityTwo.setDeItemId(
                    Long.parseLong(delivery.get("deliveryItemId").toString()));
            masterItemEntityTwo.setDeNo(delivery.get("deNo").toString());
            masterItemEntityTwo.setDeSeq(delivery.get("seq").toString());
            masterItemEntityTwo.setSeq("1");
            masterItemEntityTwo.setGoodsId(Long.parseLong(delivery.get("goodsId").toString()));
            masterItemEntityTwo.setGoodsErpCode(delivery.get("goodsErpCode").toString());
            masterItemEntityTwo.setGoodsCode(delivery.get("goodsErpCode").toString());
            masterItemEntityTwo.setGoodsName(delivery.get("goodsName").toString());
            masterItemEntityTwo.setGoodsModel(delivery.get("goodsModel").toString());
            masterItemEntityTwo.setMasterNum(retNum);
            masterItemEntityTwo.setMasterDate(new Date()); // 暂收日期
            masterItemEntity.setTaxPrice(new BigDecimal("0.000000")); // 不含税价格
            masterItemEntity.setGstPrice(new BigDecimal("0.000000")); // 含税价格
            masterItemService.save(masterItemEntityTwo);
        }
        DeliveryItemEntity deliveryItemEntity =
                  deliveryItemService.getById(Long.parseLong(delivery.get("deliveryItemId").toString()));
        deliveryItemEntity.setTemNum(temNum); // 设置暂收数量
        deliveryItemEntity.setUnInvNum(unInvNum); // 设置未验收数量
        deliveryItemEntity.setRetNum(retNum); // 设置暂退数量
        deliveryItemEntity.setArrivalDate(new Date());//当MES扫描清点数量后默认以当天时间为到货日期
        deliveryItemService.updateById(deliveryItemEntity);
        map.put("saleItemId", Long.parseLong(delivery.get("saleItemId").toString()));
        map.put("receiveNum", deliveryItemEntity.getTemNum());
        map.put("refundNum", deliveryItemEntity.getRetNum());
        //更新采购订单相关数量 - 暂收数量/暂退数量/暂退后的订单已送货数量/暂退后的订单已制单数量/暂退后的订单待送货数量
        Map<String, Object> data = orderClient.updateOrderReceiveAndRefundNum(map);
      }
    }
    jsonObject.put("list", list);
    jsonObject.put("judgeList", judgeList);
    return jsonObject;
  }

  /**
   * 判断MES只能操作已发出的单据
   *
   * @param jsonObject
   * @return
   */
  @Override
  public boolean findByDeNoVerifyStat(JSONObject jsonObject) {
    List<DeliveryEntity> list =
            deliveryService.list(
                    new QueryWrapper<DeliveryEntity>()
                            .eq("tenant_id", jsonObject.get("tenantId"))
                            .eq("de_no", jsonObject.get("deNo")));
    if (CollectionUtils.isEmpty(list)) {
      throw new RRException(jsonObject.get("deNo") + "该送货单号不存在");
    }
    // 如果是已发出，则返回 true
    if (DeliveryDeStatEnum.ISSUED.getValue().equals(list.get(0).getDeStat())) {
      return true;
    }
    return false;
  }

    @Override
    public  HashMap<String, Object> getByData(Map<String, Object> params) {
        if (params.containsKey("vendorId") && !StrUtil.isEmptyIfStr(params.get("vendorId"))) { // 供应商进入送货列表
            params.remove("vendorId");
            params.put("vendorId", commonService.getTenantId());
        } else if (params.containsKey("tenantId") && !StrUtil.isEmptyIfStr(params.get("tenantId"))){
            params.remove("tenantId");
            params.put("tenantId", commonService.getTenantId());
        }
        // 供应商进入送货列表,如果是订单号，就使用订单号查询
        if (params.containsKey("goodsErpCode") && !StrUtil.isEmptyIfStr(params.get("goodsErpCode"))) {
            if (params.get("goodsErpCode").toString().matches("^[0-9]*$")) {
                params.put("purNo", params.get("goodsErpCode").toString());
                params.remove("goodsErpCode");
            }
        }
        HashMap<String, Object> startDateAndWaitNum = this.getBaseMapper().getStartDateAndWaitNum(params);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        if (startDateAndWaitNum == null) {
            startDateAndWaitNum= new HashMap<>();
            String format = formatter.format(new Date());
            startDateAndWaitNum.put("startDate", format);
//            startDateAndWaitNum.put("startDate", DateUtils.format(new Date(), "yyyyMMdd"));
        }else {
            String format = formatter.format(startDateAndWaitNum.get("startDate"));
            startDateAndWaitNum.put("startDate", format);
        }
        return  startDateAndWaitNum;
    }

    @Override
    public DeliveryEntity queryByPurId(Long purId) {
        List<DeliveryItemEntity> deliveryItemEntity= deliveryItemService.queryByPurId(purId);
        if (!CollectionUtils.isEmpty(deliveryItemEntity)) {
                return  this.getOne(this.getQueryWrapper(new MapUtils().put("id",deliveryItemEntity.get(0).getDeId())));
            }else {
                return null;
            }
    }

    @Override
    public List<DeliveryVO> standardLastUpdateTimeDeliverySearch(String lastUpdateTime) {
        QueryWrapper<DeliveryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",commonService.getTenantId());
        queryWrapper.eq("de_stat",2);
        if(!StrUtil.isEmptyIfStr(lastUpdateTime)) {
            queryWrapper.ge("audit_time",lastUpdateTime);
        }
        List<DeliveryVO> list=new ArrayList<>();
        List<DeliveryEntity> deliveryEntities = this.list(queryWrapper);
        if (!CollectionUtils.isEmpty(deliveryEntities)) {
            for (DeliveryEntity deliveryEntity : deliveryEntities) {
                List<DeliveryItemEntity> itemEntityList = deliveryItemService.queryByList(deliveryEntity.getId());
                DeliveryVO  deliveryVO =new DeliveryVO();
                deliveryVO.setCollectFlag(deliveryEntity.getCollectFlag());
                deliveryVO.setVendorCode(deliveryEntity.getVendorCode());
                deliveryVO.setVendorName(deliveryEntity.getVendorName());
                deliveryVO.setPurchaserName(deliveryEntity.getPurchaserName());
                deliveryVO.setId(deliveryEntity.getId());
                deliveryVO.setAddress(deliveryEntity.getAddress());
                String incomingInterface = configService.getValueByKeyAndTenantId("systemTenant",deliveryEntity.getTenantId().toString());
                if(!StrUtil.isEmptyIfStr(incomingInterface)) {
                    deliveryVO.setTenantId(Long.valueOf(incomingInterface));
                }else {
                    deliveryVO.setTenantId(1030L);
                }
                deliveryVO.setWarehouseCode(deliveryEntity.getWarehouseCode());
                deliveryVO.setRemark(deliveryEntity.getRemark());
                deliveryVO.setReceivingControl(deliveryEntity.getReceivingControl());
                deliveryVO.setDeliveryDate(deliveryEntity.getDeliveryDate());
                deliveryVO.setDocumentDate(deliveryEntity.getDocumentDate());
                deliveryVO.setTenantName(deliveryEntity.getTenantName());
                deliveryVO.setOrderType(deliveryEntity.getOrderType());
                deliveryVO.setDeNo(deliveryEntity.getDeNo());
                List<DeliveryItemVO> deliveryItemVOS = new ArrayList<>();
                for (DeliveryItemEntity deliveryItemEntity : itemEntityList) {
                    DeliveryItemVO  deliveryItem = new DeliveryItemVO();
                  deliveryItem.setDeId(deliveryItemEntity.getDeId());
                  deliveryItem.setPlanId(deliveryItemEntity.getPlanId());
                  deliveryItem.setPlanLineId(deliveryItemEntity.getPlanLineId());
                  deliveryItem.setPlanSrmLineId(deliveryItemEntity.getPlanSrmLineId());
                    PurEntityVO purchaseVO = orderClient.queryById(deliveryItemEntity.getSaleId());
                    if(purchaseVO==null){
                        throw new RRException("查询不到当前订单的采购订单，请检查数据！");
                    }
                    deliveryItem.setSaleId(purchaseVO.getSourceId());
                    PurItemLineVO purItemLineVO =orderClient.queryBySaleItemId(deliveryItemEntity.getSaleItemId());
                    if(purItemLineVO==null){
                        throw new RRException("无法查询到当前订单关联的采购订单明细，请检查数据！");
                    }
                  deliveryItem.setSaleLineId(purItemLineVO.getSourceItemId());
                  deliveryItem.setSaleNo(deliveryItemEntity.getSaleNo());
                  deliveryItem.setGoodsId(deliveryItemEntity.getGoodsId());
                  deliveryItem.setGoodsErpCode(deliveryItemEntity.getGoodsErpCode());
                  deliveryItem.setGoodsCode(deliveryItemEntity.getGoodsCode());
                  deliveryItem.setGoodsName(deliveryItemEntity.getGoodsName());
                  deliveryItem.setGoodsModel(deliveryItemEntity.getGoodsModel());
                  deliveryItem.setDeliveryDate(deliveryItemEntity.getDeliveryDate());
                  deliveryItem.setPlanDate(deliveryItemEntity.getPlanDate());
                  deliveryItem.setTemDate(deliveryItemEntity.getTemDate());
                  deliveryItem.setReplyDate(deliveryItemEntity.getReplyDate());
                  deliveryItem.setDevNum(deliveryItemEntity.getDevNum());
                  deliveryItem.setTemNum(deliveryItemEntity.getTemNum());
                  deliveryItem.setUnNum(deliveryItemEntity.getUnNum());
                  deliveryItem.setInvNum(deliveryItemEntity.getInvNum());
                  deliveryItem.setUnInvNum(deliveryItemEntity.getUnInvNum());
                  deliveryItem.setOrderNum(deliveryItemEntity.getOrderNum());
                  deliveryItem.setLineId(deliveryItemEntity.getId());
                  deliveryItem.setSeq(deliveryItemEntity.getSeq());
                    deliveryItemVOS.add(deliveryItem);
                }
                deliveryVO.setDeliveryItemVOList(deliveryItemVOS);
                list.add(deliveryVO);
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }else {
            return null;
        }
    }

    @Override
    public  List<DeliveryEntity> getByPlanId(Long planLineId, Long planId) {
        List<DeliveryEntity> delivery= new ArrayList<>();
        List<DeliveryItemEntity>  list=  deliveryItemService.queryByPlanId(planLineId,planId);
        for (DeliveryItemEntity itemEntity : list) {
            DeliveryEntity deliveryEntity = this.getById(itemEntity.getDeId());
            if((deliveryEntity.getDeStat()!=2&& deliveryEntity.getDeStat()!=5)||deliveryEntity.getIsCall()!=1){
                throw new RRException("当前计划单据未完成送货不能关闭，请检查数据！");
            }
            delivery.add(deliveryEntity);
        }

        return delivery;
    }

    @Override
    public boolean queryById(Long saleId, Long saleItemId) {
        List<DeliveryItemVO> deliveryItemVOS = deliveryItemService.queryBySaleItemId(saleId, saleItemId);
        if (CollectionUtils.isNotEmpty(deliveryItemVOS)) {
            for (DeliveryItemVO deliveryItemVO : deliveryItemVOS) {
                DeliveryEntity deliveryEntity = this.getById(deliveryItemVO.getId());
                if(deliveryEntity.getDeStat()!=2){
                    throw new RRException("当前订单单据未完成送货不能关闭，请检查数据！");
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateByTenantId(Long tenantId, Long id) {
        DeliveryEntity deliveryEntity = this.getById(id);
        deliveryEntity.setIsCall(1);
        return this.updateById(deliveryEntity);
    }

    /**
     * 送货计划报表 - 查看送货明细
     *
     * @param params
     * @return
     */
    @Override
    public List<HashMap<String, Object>> findVendorGoodsDeliveryByGoodsErpCode(Map<String, Object> params) {
        return this.getBaseMapper().findVendorGoodsDeliveryByGoodsErpCode(params);
    }

    @Override
    public PageUtils dataList(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list=this.getBaseMapper().dataList(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils data(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list=this.getBaseMapper().data(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils listDeli(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list=this.getBaseMapper().listDeli(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils listOrder(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list=this.getBaseMapper().listOrder(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils dataOrder(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = this.getBaseMapper().dataOrder(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryDeliveryTracking(Map<String, Object> params) {
        if (params.containsKey("tenantId") && !StrUtil.isEmptyIfStr(params.get("tenantId"))) { // 采购方进入送货列表
            params.remove("tenantId");
            params.put("tenantId", commonService.getTenantId());
        } else if (params.containsKey("vendorId") && !StrUtil.isEmptyIfStr(params.get("vendorId"))) { // 供应商进入送货列表
            params.remove("vendorId");
            params.put("vendorId", commonService.getTenantId());
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list=this.getBaseMapper().queryDeliveryTracking(page,params);
        page.setRecords(list);
        return new PageUtils(page);

    }

    /**
     * 根据送货单号查询送货单
     *
     * @param deNo
     * @return
     */
    @Override
    public DeliveryEntity queryByDeNoInfo(String deNo) {
        return this.getOne(this.getQueryWrapper(new MapUtils().put("deNo", deNo)));
    }

    /**
     * 根据送货单号查询送货单
     *
     * @param deNo
     * @return
     */
    @Override
    public Integer queryByDeNoCount(String deNo) {
        return this.count(this.getQueryWrapper(new MapUtils().put("deNo", deNo)));
    }

    /**
     * 校验供应商状态
     * @param delivery
     */
    private void checkVendor(DeliveryEntity delivery){
        VendorVO vendorVo = baseClient.getVendorVoBySourceId(delivery.getTenantId(), delivery.getVendorId());
        if (Objects.isNull(vendorVo)){
            throw new RRException("供应商不存在");
        }
        // 校验供应商状态是否为冻结
        if (VendorStatEnum.STAT17.getValue().equals(vendorVo.getVendorStat()) && WhetherEnum.NO.getCode().equals(vendorVo.getIsAllowPlaceOrder())){
            throw new RRException("当前您以被采购方冻结，无法进行创建送货单操作");
        }

        // 供应商的有效结束日期不为空时
        if (Objects.nonNull(vendorVo.getEffEndDate())){
            // 获取供应商有效结束日期+23:59:59
            Date effEndDate = DateUtil.parse(DateUtil.format(vendorVo.getEffEndDate(), "yyyy-MM-dd") + " 23:59:59");
            // 根据effEndDate来判断是否小于当前时间，如果小于当前时间，则抛出异常
            if (DateUtil.compare(effEndDate, new Date()) < 0){
                throw new RRException(String.format("当前您的供应商档案中的有效结束日期为%s且已失效，无法进行创建送货单，若需要创建送货请联系相关人员延长有效日期",DateUtil.format(vendorVo.getEffEndDate(), "yyyy-MM-dd") + " 23:59:59"));
            }
        }
    }

    /**
     * 查询是否存在物料异常档案单据状态为已审核且为停用和禁止下单的数据
     * @param delivery
     */
    private void checkSupplyBlackArchives(DeliveryEntity delivery) {
        // 获取送货单明细中去重后的物料id
        List<Long> goodsIdList = delivery.getDeliveryItemEntityList().stream().map(item -> item.getGoodsId()).distinct().collect(Collectors.toList());
        // 查询是否存在物料异常档案单据状态为已审核且为停用和禁止下单的数据
        QuerySupplySlaveVO querySupplySlaveVO = new QuerySupplySlaveVO();
        querySupplySlaveVO.setVendorId(delivery.getVendorId());
        querySupplySlaveVO.setDeptId(delivery.getDeptId());
        querySupplySlaveVO.setGoodsIdList(goodsIdList);
        querySupplySlaveVO.setSupplyType(SupplyBlackTypeEnum.DEACTIVATE.getValue());
        querySupplySlaveVO.setIsAllowPlaceOrder(WhetherEnum.NO.getCode());
        List<SupplyBlackArchivesEntity> supplyBlackArchivesList = supplyBlackArchivesService.queryList(querySupplySlaveVO);
        if (CollectionUtil.isNotEmpty(supplyBlackArchivesList)) {
            List<String> goodsErpList = supplyBlackArchivesList.stream().map(SupplyBlackArchivesEntity::getGoodsErpCode).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(goodsErpList)) {
                throw new RRException(String.format("以下物料%s存在物料异常停用禁止下单情况", goodsErpList.toString()));
            }
        }
    }

    /**
     * 转换送货单看板查询参数
     * @param params
     */
    private void convertDeliveryKanBanParams(Map<String,Object> params){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar c = Calendar.getInstance();
        if (!StrUtil.isEmptyIfStr(params.get("goodsList"))){
            // 将params.get("goodsList")转为List<String>
            List<String> goodsList = JSON.parseArray(params.get("goodsList").toString(), String.class);
            if (CollectionUtil.isNotEmpty(goodsList)){
                params.remove("goods");
                goodsList = goodsList.stream().distinct().collect(Collectors.toList());
                // 去除goodsList中的空格
                goodsList.replaceAll(String::trim);
                if (goodsList.size() == 1){
                    params.remove("goodsList");
                    params.put("goods", goodsList.get(0));
                } else {
                    params.replace("goodsList", goodsList);
                }
            }
        }
        // 如果传入的startDate为空，则使用最早待送货>0的日期单据
        if (null == params.get("startDate") || "".equals(params.get("startDate"))) {
            HashMap<String, Object> startDateAndWaitNum = getBaseMapper().getStartDateAndWaitNum(params);
            try {
                Date sd = sdf.parse(startDateAndWaitNum.get("startDate") + "");
                c.setTime(sd);
                c.add(Calendar.DATE, 1);
                params.put("startDate", sdf.format(c.getTime()));
            } catch (Exception e) {
                throw new RRException("转换查询日期失败");
            }
        }
        try {
            if (params.get("requiredDeliveryTime") != null && !params.get("requiredDeliveryTime").equals("")) {
                String[] split = params.get("requiredDeliveryTime").toString().split(" 至 ");
                params.put("startRequiredDeliveryTime", split[0] + " 00:00:00");
                params.put("endRequiredDeliveryTime", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            throw new RRException("检验日期输入有误");
        }
    }

    /**
     * 删除校验
     * @param deliveryEntity
     */
    private void deleteCheck(DeliveryEntity deliveryEntity){
        SysEntEntity tenantInfo = commonService.getTenantInfo();
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(tenantInfo.getEntType()) && deliveryEntity.getTenantId().equals(3332L)){
            //获取系统参数 - 是否控制供应商关闭送货单时间
            String ifControlDeliveryShutdownTimeByVendor = sysClient.getValueByKeyAndTenantId("ifControlDeliveryShutdownTimeByVendor", deliveryEntity.getTenantId());
            //参数不为空
            if (!StrUtil.isEmptyIfStr(ifControlDeliveryShutdownTimeByVendor)) {
                if (DeStatEnum.ISSUED.getCode().equals(deliveryEntity.getDeStat())) {
                    //计算供应商可最后关闭送货单时间 = 送货单创建日期+是否控制供应商关闭送货单时间参数
                    Date PrescribedShutdownTime = DateUtil.offsetMinute(deliveryEntity.getAuditTime(), Integer.parseInt(ifControlDeliveryShutdownTimeByVendor));
                    //获取当前系统时间
                    Date nowTime = new Date();
                    //若当前时间大于供应商可最后关闭送货单时间时
                    if (nowTime.after(PrescribedShutdownTime)) {
                        throw new RRException(String.format("当前[%s]时间超出了[%s]送货单的最后可关闭时间[%s],请及时联系采购员申请删除关闭此送货单"
                                , DateUtil.formatDateTime(nowTime), deliveryEntity.getDeNo(), DateUtil.formatDateTime(PrescribedShutdownTime)));
                    }
                }
            } else {
                //送货单状态不为待发出
                if (!DeStatEnum.TOBEISSUED.getCode().equals(deliveryEntity.getDeStat())) {
                    throw new RRException(String.format("当前[%s]的送货单数据不为待发出状态,删除失败！", deliveryEntity.getDeNo()));
                }
            }
        }
        if (deliveryEntity.getTenantId().equals(24739L)){
            MasterSalveQuery query = new MasterSalveQuery();
            query.setMasterType(com.dian.enums.MasterTypeEnum.PROREC.getCode());
            query.setDeId(deliveryEntity.getId());
            query.setDeleteFlag(WhetherEnum.NO.getCode());
            List<MasterItemVO> masterItemVOList = masterItemService.queryMasterSalveList(query);
            if (CollectionUtil.isNotEmpty(masterItemVOList)){
                throw new RRException(String.format("当前送货单%s存在有效的收料单，不允许删除关闭！",  deliveryEntity.getDeNo()));
            }
        }
    }

    /**
     * 检测是否有价格
     * @param delivery
     */
    private void checkHasPrice(DeliveryEntity delivery, String type){
        if (CollectionUtil.isEmpty(delivery.getValidItemList())){
            throw new RRException("请先选择需要送货的明细数据");
        }
        List<Long> goodsIds = delivery.getValidItemList().stream().map(DeliveryItemEntity::getGoodsId).distinct().collect(Collectors.toList());
        PriceTranRequest priceTranRequest = new PriceTranRequest();
        priceTranRequest.setVendorId(delivery.getVendorId());
        priceTranRequest.setDeptId(delivery.getDeptId());
        priceTranRequest.setGoodsIds(goodsIds);
        priceTranRequest.setIsValid(WhetherEnum.YES.getCode());
        priceTranRequest.setSpecifyDate(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        List<PriceTranVO> priceTranVOS = imClient.queryPriceTranList(priceTranRequest);
        MaterialPriceRequest priceRequest = new MaterialPriceRequest();
        priceRequest.setStatus(FlowTaskStatusEnum.Draft.getValue());
        priceRequest.setVendorId(delivery.getVendorId());
        priceRequest.setTenantPId(delivery.getDeptId());
        priceRequest.setManualSelectionApproveType(MaterialApproveType.NEWGOODSESTIMATED.getValue());
        // 暂估价数据
        List<MaterialPriceDataVO> estPriceApplyList = imClient.queryEstimatePriceApplyList(priceRequest);
        List<MaterialPriceItemVO> materialPriceItemVOList = new ArrayList<>();
        // 获取当前北京时间
        ZoneId beijingZone = ZoneId.of("Asia/Shanghai");
        ZonedDateTime nowInBeijing = ZonedDateTime.now(beijingZone);

        // 有效开始日期：当前月份第一天 00:00:00
        LocalDateTime invalidDate = nowInBeijing.toLocalDate().withDayOfMonth(1).atStartOfDay();

        Map<Long, DeliveryItemEntity> deliveryItemMap = delivery.getValidItemList().stream().filter(vo -> ObjectUtil.isNotEmpty(vo.getGoodsId()))
                .collect(Collectors.toMap(
                        DeliveryItemEntity::getGoodsId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        Map<Long, PriceTranVO> erpOrderMap = priceTranVOS.stream()
                .filter(vo -> ObjectUtil.isNotEmpty(vo.getGoodsId()))
                .collect(Collectors.toMap(
                        PriceTranVO::getGoodsId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        goodsIds.forEach(goodsId -> {
            DeliveryItemEntity deliveryItem = deliveryItemMap.get(goodsId);
            PriceTranVO priceVo = erpOrderMap.get(goodsId);
            if (!"SAVE".equals(type)){
                Assert.isNull(priceVo, StrUtil.format("当前送货单{}的物料{}没有对应的价格数据，请先维护价格！", delivery.getDeNo(), deliveryItem.getGoodsCode()));
            } else {
                MaterialPriceDataVO estPriceApplyVo = estPriceApplyList.stream().filter(priceTranVO -> priceTranVO.getGoodsId().equals(goodsId)).findFirst().orElse(null);
                if (ObjectUtil.isEmpty(priceVo) && ObjectUtil.isEmpty(estPriceApplyVo)){
                    MaterialPriceItemVO materialPriceItemVO = new MaterialPriceItemVO();
                    materialPriceItemVO.setTenantId(delivery.getTenantId());
                    materialPriceItemVO.setTenantPId(delivery.getDeptId());
                    materialPriceItemVO.setTenantPName(delivery.getDeptName());
                    materialPriceItemVO.setVendorId(delivery.getVendorId());
                    materialPriceItemVO.setVendorCode(delivery.getVendorCode());
                    materialPriceItemVO.setVendorName(delivery.getVendorName());
                    materialPriceItemVO.setGoodsId(deliveryItem.getGoodsId());
                    materialPriceItemVO.setGoodsErpCode(deliveryItem.getGoodsErpCode());
                    materialPriceItemVO.setGoodsCode(deliveryItem.getGoodsCode());
                    materialPriceItemVO.setGoodsName(deliveryItem.getGoodsName());
                    materialPriceItemVO.setGoodsModel(deliveryItem.getGoodsModel());
                    materialPriceItemVO.setUomId(deliveryItem.getUomId());
                    materialPriceItemVO.setUomName(deliveryItem.getUomName());
                    materialPriceItemVO.setRateId(deliveryItem.getRateId());
                    materialPriceItemVO.setRateName(deliveryItem.getRateName());
                    materialPriceItemVO.setRateVal(deliveryItem.getRateVal());
                    // 设置为当前月份的第一天
                    materialPriceItemVO.setInvalidDate(DateConverter.convertLocalDateToDate(invalidDate.toLocalDate()));
                    materialPriceItemVO.setExpireDate(DateUtil.parseDate("2100-01-01 23:59:59"));
                    materialPriceItemVOList.add(materialPriceItemVO);
                }
            }
        });
        logger.info("需要生成暂估价的数据 =>"+JSONObject.toJSONString(materialPriceItemVOList));
        if ("SAVE".equals(type) && CollectionUtil.isNotEmpty(materialPriceItemVOList)){
            imClient.saveEstPriceApply(materialPriceItemVOList);
        }
    }

    /**
     * 校验仓库信息
     * @param delivery
     */
    private void checkWarehouse(DeliveryEntity delivery){
        if (CollectionUtil.isEmpty(delivery.getValidItemList())){
            throw new RRException("请先选择需要送货的明细数据");
        }
        List<Long> goodsIds = delivery.getValidItemList().stream().map(DeliveryItemEntity::getGoodsId).distinct().collect(Collectors.toList());
        GoodsReqVo goodsReqVo = new GoodsReqVo();
        goodsReqVo.setGoodsIdList(goodsIds);
        List<GoodsVO> goodsVOList = baseClient.queryGoodsList(goodsReqVo);
        List<String> warehouseCodeList = goodsVOList.stream().map(GoodsVO::getWarehouseCode).distinct().collect(Collectors.toList());
        if (warehouseCodeList.size() > 1){
            throw new RRException("当前送货单的物料存在不同的仓库，请先选择相同仓库的物料！");
        }
    }

    /**
     * 将送货单推送至WMS(美博)
     * @param delivery
     * @param operType
     * @return
     */
    private WmsDeliveryResponse pushDeliveryToWms(DeliveryEntity delivery,Integer operType){
        List<PushDeliveryRequest> pushDeliveryList = new ArrayList<>();
        //送货单主表信息
        PushDeliveryRequest pushDelivery = new PushDeliveryRequest();
        //SAP工厂(SAP公司代码)
        pushDelivery.setSite(delivery.getDeptCode());
        //SRM送货单号
        pushDelivery.setBillNo(delivery.getDeNo());
        //对应业务单据类型 TODO 默认A000
        pushDelivery.setBillType("A000");
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        String vendorCode = pattern.matcher(delivery.getVendorCode()).matches() ? new BigDecimal(delivery.getVendorCode())+"" : delivery.getVendorCode();
        //SAP供应商编码
//      Long vendorCode = Long.parseLong(delivery.getVendorCode());
        pushDelivery.setPartnerNumber(vendorCode.toString());
        pushDelivery.setPartnerName(delivery.getVendorName());
        //创建时生成,默认创建日期,精确到时分秒
        pushDelivery.setBillDate(DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        //备注
        pushDelivery.setRemark(delivery.getRemark());
        //创建
        if (OperTypeEnum.CREATE.getValue().equals(operType)){
            //状态 10=刚生成
            pushDelivery.setStatus(10);
        }
        //删除
        if (OperTypeEnum.DELETE.getValue().equals(operType)){
            //状态 0=取消/作废
            pushDelivery.setStatus(0);
        }
        if (CollectionUtil.isEmpty(delivery.getDeliveryItemEntityList())){
            throw new RRException(String.format("%s送货单明细数据不能为空",delivery.getDeNo()));
        }
        List<PushDeliveryItemRequest> pushDeliveryItemList = new ArrayList<>();
        for (DeliveryItemEntity deliveryItem:delivery.getDeliveryItemEntityList()) {
            PushDeliveryItemRequest pushDeliveryItem = new PushDeliveryItemRequest();
            //送货单行项目号
            pushDeliveryItem.setDetailNo(deliveryItem.getSeq());
            //SAP物料编码
            pushDeliveryItem.setItemCode(deliveryItem.getGoodsErpCode());
            //库存类型标识 0：普通工厂库存；1：供应商库存；2：订单库存
            pushDeliveryItem.setCnsgFlg(0);
            GoodsVO goodsVo = baseClient.getGoodsVoByCode(delivery.getTenantId(), deliveryItem.getGoodsErpCode());
            pushDeliveryItem.setStockUnit(goodsVo.getUomName());
            //送货数量
            pushDeliveryItem.setQty(deliveryItem.getDevNum());
            pushDeliveryItem.setRcvWarehouse(deliveryItem.getWarehouseCode());
            pushDeliveryItem.setRefBillNo(deliveryItem.getSaleNo());
            pushDeliveryItem.setRefBillSeqNo(deliveryItem.getSaleSeq());
            pushDeliveryItem.setRemark(deliveryItem.getRemark());
            pushDeliveryItemList.add(pushDeliveryItem);
        }
        pushDelivery.setDetailList(pushDeliveryItemList);
        pushDeliveryList.add(pushDelivery);
        String jsonData = JSON.toJSONString(pushDeliveryList);
        logger.info("SRM推送送货单至WMS入库单接口传参信息="+jsonData);
        // WMS的IP地址
        String wmsUrl = sysClient.getValueByKeyAndTenantId("WMSUrl", delivery.getTenantId());
        if (StrUtil.isEmptyIfStr(wmsUrl)){
            throw new RRException("没有维护Wms接口访问IP,请求接口失败,请联系IT进行维护数据");
        }
//      String url = "http://*************:19400/wmsAsnBill/saveBatch";
        wmsUrl = wmsUrl + "/wmsAsnBill/saveBatch";
        logger.info("请求WMS地址="+wmsUrl);
        String result = wmsApiService.sendWms(wmsUrl,jsonData,"送货单推至WMS接口",commonService.getTenantId());
        logger.info("SRM推送送货单至WMS入库单接口返回信息="+result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        WmsDeliveryResponse wmsDelivery = BeanConverter.convert(jsonObject, WmsDeliveryResponse.class);
        if (Objects.isNull(wmsDelivery)){
            throw new RRException(String.format("推送送货单至WMS接口无返回信息,请求接口失败"));
        }
        if (StrUtil.isEmptyIfStr(wmsDelivery.getCode()) || !"200".equals(wmsDelivery.getCode())){
            if (StrUtil.isEmptyIfStr(wmsDelivery.getMsg())){
                throw new RRException(String.format("WMS返回的CODE信息不为200"));
            } else {
                throw new RRException(String.format("WMS返回的CODE信息不为200,%s",wmsDelivery.getMsg()));
            }
        }
        if (CollectionUtil.isNotEmpty(wmsDelivery.getData())){
            if (!StrUtil.isEmptyIfStr(wmsDelivery.getData().get(0).getDataStatus()) && !"S".equals(wmsDelivery.getData().get(0).getDataStatus())){
                if (!StrUtil.isEmptyIfStr(wmsDelivery.getData().get(0).getErrorMsg())){
                    throw new RRException(String.format("SRM传WMS送货单接口信息,失败原因:%s",wmsDelivery.getData().get(0).getErrorMsg()));
                }
            }
        }
        return wmsDelivery;
    }

    /**
     * 将送货单推送至MES(美博)
     * @param delivery
     * @return
     */
    private boolean pushDeliveryToMes(DeliveryEntity delivery) {
        List<DeliveryOrderParam> deliveryOrderParamList = new ArrayList<>();
        DeliveryOrderParam deliveryOrderParam = new DeliveryOrderParam();
        // SAP组织编码
        deliveryOrderParam.setSapOrgCode(delivery.getDeptCode());
        // 送货单号
        deliveryOrderParam.setBillNo(delivery.getDeNo());
        // 供应商编码
        deliveryOrderParam.setSupplyCode(delivery.getVendorCode());
        // 供应商名称
        deliveryOrderParam.setSupplyName(delivery.getVendorName());
        // MES送货单状态
        deliveryOrderParam.setStatus("DELIVERED");
        // 送货日期
        deliveryOrderParam.setBillDate(DateUtil.format(delivery.getDeliveryDate(),"yyyy-MM-dd HH:mm:ss"));
        // 明细数据
        List<DeliveryOrderLine> deliveryNoteLines = new ArrayList<>();
        delivery.getDeliveryItemEntityList().forEach(item -> {
            DeliveryOrderLine deliveryOrderLine = new DeliveryOrderLine();
            deliveryOrderLine.setBillNo(delivery.getDeNo());
            deliveryOrderLine.setDeliDtlId(item.getId().toString());
            deliveryOrderLine.setSeq(Integer.parseInt(item.getSeq()));
            deliveryOrderLine.setMitemCode(item.getGoodsErpCode());
            deliveryOrderLine.setMitemName(item.getGoodsName());
            deliveryOrderLine.setMitemCategory(item.getGoodsClassName());
            deliveryOrderLine.setShipQty(item.getDevNum().toString());
            if (Objects.nonNull(item.getProdDate())){
                deliveryOrderLine.setProduceDate(DateUtil.format(item.getProdDate(),"yyyy-MM-dd HH:mm:ss"));
            }
            deliveryOrderLine.setPurchaseNo(item.getSaleNo());
            deliveryOrderLine.setPoSeq(item.getSaleSeq());
            deliveryNoteLines.add(deliveryOrderLine);
        });
        deliveryOrderParam.setDeliveryNoteLines(deliveryNoteLines);
        deliveryOrderParamList.add(deliveryOrderParam);
        JSONObject jsonData = new JSONObject();
        jsonData.put("models", deliveryOrderParamList);
        logger.info("SRM推送送货单至MES接口传参信息="+jsonData.toJSONString());
        // 获取MES的IP地址、密钥
        String mesUrl = sysClient.getValueByKeyAndTenantId("mesUrl", delivery.getTenantId());
        String mesIPaaSSecret = sysClient.getValueByKeyAndTenantId("mes_iPaaS_Secret", delivery.getTenantId());
        String mesBasicMode = sysClient.getValueByKeyAndTenantId("Mes_Basic_Mode", delivery.getTenantId());
        if (StrUtil.isEmptyIfStr(mesUrl)){
            throw new RRException("没有维护MES接口访问IP,请求接口失败,请联系IT进行维护数据");
        }
        if (StrUtil.isEmptyIfStr(mesIPaaSSecret)){
            throw new RRException("没有维护MES接口iPaaS验证访问密钥,请求接口失败,请联系IT进行维护数据");
        }
        if (StrUtil.isEmptyIfStr(mesBasicMode)){
            throw new RRException("没有维护MES接口Basic认证模式,请求接口失败,请联系IT进行维护数据");
        }
        // 拼接认证信息
        String basicVal = mesBasicMode + " " + mesIPaaSSecret;
        logger.info("MES认证信息="+basicVal);
        mesUrl = mesUrl + "/flow/api/mes/deliveryorder/addorupdate";
        // 调用美云MES送货单同步接口
        String result = mesApiService.sendMes(mesUrl, basicVal, jsonData.toJSONString(), "SRM送货单同步至MES", delivery.getTenantId());
        logger.info("SRM推送送货单至MES接口返回信息="+result);
        if (StrUtil.isEmptyIfStr(result)) {
            throw new RRException(String.format("SRM推送送货单至MES接口无返回信息,请求接口失败"));
        }
        JSONObject resultJson = JSONObject.parseObject(result);
        if (resultJson.get("ZTYPE").equals("E")) {
            throw new RRException(String.format("SRM推送送货单至MES接口返回信息为异常,%s",resultJson.get("ZMESSAGE")));
        }
        return true;
    }

    /**
     * 推送需要删除送货单数据至MES中进行修改(美博)
     * @param deliverySlaveMap
     * @param opType
     * @param tenantId
     * @return
     */
    private boolean pushDelDeliveryToMes(Map<String, List<DeliverySlaveVO>> deliverySlaveMap,Integer opType,Long tenantId) {
        DelDeliveryParam delDeliveryParam = new DelDeliveryParam();
        delDeliveryParam.setType(opType+"");
        List<DelDeliveryModel> models = new ArrayList<>();
        deliverySlaveMap.forEach((deNo,deliverySlaveList) -> {
            DelDeliveryModel delDeliveryModel = new DelDeliveryModel();
            delDeliveryModel.setBillNo(deNo);
            // opType为2-明细行删除时，需要给MES系统传送货单明细行数据
            if (DelDeliveryTypeEnum.LINE_DELETED.getValue().equals(opType)){
                List<DelDeliveryLine> deliveryNoteLines = new ArrayList<>();
                deliverySlaveList.forEach(item -> {
                    DelDeliveryLine deliveryNoteLine = new DelDeliveryLine();
                    // 送货单明细行ID
                    deliveryNoteLine.setDeliDtlId(item.getDeItemId().toString());
                    // 送货单号
                    deliveryNoteLine.setBillNo(item.getDeNo());
                    // 送货单行号
                    deliveryNoteLine.setSeq(item.getSeq());
                    deliveryNoteLines.add(deliveryNoteLine);
                });
                delDeliveryModel.setDeliveryNoteLines(deliveryNoteLines);
            }
            models.add(delDeliveryModel);
        });
        delDeliveryParam.setModels(models);
        // 获取MES的IP地址、密钥
        String mesUrl = sysClient.getValueByKeyAndTenantId("mesUrl", tenantId);
        String mesIPaaSSecret = sysClient.getValueByKeyAndTenantId("mes_iPaaS_Secret", tenantId);
        String mesBasicMode = sysClient.getValueByKeyAndTenantId("Mes_Basic_Mode", tenantId);
        if (StrUtil.isEmptyIfStr(mesUrl)){
            throw new RRException("没有维护MES接口访问IP,请求接口失败,请联系IT进行维护数据");
        }
        if (StrUtil.isEmptyIfStr(mesIPaaSSecret)){
            throw new RRException("没有维护MES接口iPaaS验证访问密钥,请求接口失败,请联系IT进行维护数据");
        }
        if (StrUtil.isEmptyIfStr(mesBasicMode)){
            throw new RRException("没有维护MES接口Basic认证模式,请求接口失败,请联系IT进行维护数据");
        }
        // 拼接认证信息
        String basicVal = mesBasicMode + " " + mesIPaaSSecret;
        logger.info("MES认证信息="+basicVal);
        mesUrl = mesUrl + "/flow/api/mes/deliveryorder/updatestatus";
        // 调用美云MES送货单修改状态接口
        String result = mesApiService.sendMes(mesUrl, basicVal, JSONObject.toJSONString(delDeliveryParam), "SRM送货单修改删除状态同步至MES", tenantId);
        logger.info("SRM推送送货单至MES接口返回信息="+result);
        if (StrUtil.isEmptyIfStr(result)) {
            throw new RRException(String.format("SRM推送送货单修改至MES接口无返回信息,请求接口失败"));
        }
        JSONObject resultJson = JSONObject.parseObject(result);
        if (resultJson.get("ZTYPE").equals("E")) {
            throw new RRException(String.format("SRM推送送货单修改至MES接口返回信息为异常,%s",resultJson.get("ZMESSAGE")));
        }
        return true;
    }

    /**
     * 推送送货单数据至WMS进行修改(冠宇达)
     * @param delivery
     * @return
     */
    private boolean pushDeliveryToGveWms(DeliveryEntity delivery,Integer operType){
        // WMS送货单表头数据
        Map<String,Object> data = new HashMap<>();
        // 创建人系统
        data.put("creator", "SRM");
        // 送货单号标识
        data.put("code", delivery.getDeNo());
        // 采购订单类型集合
        List<Integer> orderTypeList = delivery.getDeliveryItemEntityList().stream().map(DeliveryItemEntity::getOrderType).distinct().collect(Collectors.toList());
        if (orderTypeList.size() > 1){
            throw new RRException("采购订单类型不一致");
        }
        JinDieOrderTypeEnum orderTypeEnum = JinDieOrderTypeEnum.getEnum(orderTypeList.get(0));
        // 采购订单的业务类型
        data.put("voucherType", orderTypeEnum.getType());
        // 送货单确认时间
        data.put("createTime", DateUtil.format(delivery.getAuditTime(),"yyyy-MM-dd HH:mm:ss"));
        data.put("remark", delivery.getRemark());
        // 供应商编码/名称
        data.put("supplierCode", delivery.getVendorCode());
        data.put("supplierName", delivery.getVendorName());
        // WMS送货单行数据
        List<Map<String, Object>> rows = new ArrayList<>();
        List<Long> goodsIds = delivery.getDeliveryItemEntityList().stream().map(DeliveryItemEntity::getGoodsId).collect(Collectors.toList());
        GoodsReqVo goodsReqVo = new GoodsReqVo();
        goodsReqVo.setGoodsIdList(goodsIds);
        List<GoodsVO> goodsVOList = baseClient.queryGoodsList(goodsReqVo);
        delivery.getDeliveryItemEntityList().forEach(item -> {
            GoodsVO goodsVO = goodsVOList.stream().filter(goods -> goods.getId().equals(item.getGoodsId())).findFirst().orElse(null);
            Map<String, Object> row = new HashMap<>();
            // 送货单明细行标识（以SRM送货单明细ID为唯一值）
            row.put("rowCode", item.getId());
            // 发货单ID
            row.put("reserve1",item.getDeId());
            // 发货单明细ID
            row.put("reserve2",item.getId());
            // 送货数量
            row.put("quantity", item.getDevNum());
            // 生产批次
            row.put("productionBatchNo", item.getProdBatchNo());
            // 生产日期
            row.put("manufactureDate", DateUtil.format(item.getProdDate(), "yyyy-MM-dd"));
            // 单位
            row.put("unitName", item.getUomName());
            // 物料编码
            row.put("materialCode", item.getGoodsErpCode());
            // ERP采购订单号
            row.put("sourceVoucherCode", item.getSaleNo());
            // ERP采购订单明细ID
            row.put("sourceItemId", item.getErpOrderItemId());
            row.put("remark", item.getRemark());
            // 仓库编码
            row.put("warehouseCode", "");
            if (ObjectUtil.isNotEmpty(goodsVO) && !StrUtil.isEmptyIfStr(goodsVO.getWarehouseCode())){
                row.replace("warehouseCode", goodsVO.getWarehouseCode());
            }
            rows.add(row);
        });
        data.put("rows", rows);
        gveWmsService.sendWms("/sync/order/srmPurchaseDelivery/standart", data, "送货单同步至WMS接口", delivery.getTenantId());
        return true;
    }

    /**
     * 推送送货单数据至WMS进行关闭取消(冠宇达)
     * @param delivery
     * @param operType
     * @return
     */
    private boolean closeDeliveryToGveWms(DeliveryEntity delivery,Integer operType){
        // WMS送货单表头数据
        Map<String,Object> data = new HashMap<>();
        data.put("code", delivery.getDeNo());
        data.put("orderTypeId", 44);
        gveWmsService.sendWms("/sync/order/cancel/standart", data, "送货单关闭取消同步至WMS接口", delivery.getTenantId());
        return true;
    }

    /**
     * 批量查询物料信息
     * @param goodsIdList
     * @return
     */
    private List<GoodsVO> queryGoodsList(List<Long> goodsIdList) {
        List<GoodsVO> goodsVoList = new ArrayList<>();
        if (CollectionUtil.isEmpty(goodsIdList)) {
            return goodsVoList;
        }

        // 提取为常量便于维护
        final int batchSize = 10;

        // 预分配容量（可选）
        int expectedSize = (int) Math.ceil(goodsIdList.size() * 1.0 / batchSize);
        goodsVoList = new ArrayList<>(expectedSize);

        for (int i = 0; i < goodsIdList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, goodsIdList.size());
            List<Long> currentBatch = goodsIdList.subList(i, end); // 修正类型错误

            GoodsReqVo goodsReqVo = new GoodsReqVo();
            goodsReqVo.setGoodsIdList(currentBatch);

            try {
                List<GoodsVO> goodsList = baseClient.queryGoodsList(goodsReqVo);
                if (CollectionUtil.isNotEmpty(goodsList)) {
                    goodsVoList.addAll(goodsList);
                }
            } catch (Exception e) {
                // 记录异常但继续执行后续批次
                // logger.error("查询商品列表失败，批次起始索引: {}", i, e);
            }
        }

        return goodsVoList;
    }


    /**
     * SRM->SAP 供应商送货在途数据同步接口
     *
     * @param delivery
     * @return
     */
    private SapInTransitQuantityReturnInfoVO uploadSapInTransitQuantity(DeliveryEntity delivery,Integer operType){
        try {
            delivery.getDeliveryItemEntityList().forEach(item -> {
                item.setPlanDateStr(DateUtil.format(item.getPlanDate(),"yyyyMMdd"));
            });
            Map<String, List<DeliveryItemEntity>> goodsGroupMap = delivery.getDeliveryItemEntityList().stream().collect(Collectors.groupingBy(item -> item.getGoodsErpCode()+"#"+item.getPlanDateStr()+"#"+item.getMrpRegion()));
            if(Objects.nonNull(goodsGroupMap)){
                List<SapInTransitQuantityVO> inTransitQuantityVOList = new ArrayList<>();
                for (String goodsErpCodes:goodsGroupMap.keySet()) {
                    String[] split = goodsErpCodes.split("#");
                    SapInTransitQuantityVO sapInTransitQuantityVO = new SapInTransitQuantityVO();
                    List<DeliveryItemEntity> deliveryItemList = goodsGroupMap.get(goodsErpCodes);
                    BigDecimal sumng = deliveryItemList.stream().map(item -> item.getDevNum()).reduce(BigDecimal.ZERO, (b1, b2) -> b1.add(b2));
                    sapInTransitQuantityVO.setMATNR(split[0]);
                    sapInTransitQuantityVO.setLIFNR(delivery.getVendorCode());
                    sapInTransitQuantityVO.setWERKS(delivery.getDeptCode());
                    if (operType.equals(1)){
                        sapInTransitQuantityVO.setSCMNG(sumng.toString());
                    } else {
                        sapInTransitQuantityVO.setSCMNG(sumng.negate().toString());
                    }
                    sapInTransitQuantityVO.setZTDATE(split[1]);

                    sapInTransitQuantityVO.setBERID(split[2]);
                    sapInTransitQuantityVO.setVDATU(DateUtil.format(new Date(),"yyyyMMdd"));
                    inTransitQuantityVOList.add(sapInTransitQuantityVO);
                }
                // SAP的IP地址
                String sapLinkUrl = sysClient.getValueByKeyAndTenantId("SAPLinkUrl", delivery.getTenantId());
                // SAP的环境
                String sapClient = sysClient.getValueByKeyAndTenantId("sap-client", delivery.getTenantId());
                if (!StrUtil.isEmptyIfStr(sapLinkUrl) && !StrUtil.isEmptyIfStr(sapClient)){
                    sapLinkUrl = sapLinkUrl + "/zpp009_http?sap-client="+sapClient;
                    String jsonData = JSON.toJSONString(inTransitQuantityVOList);
                    logger.info("SRM调用SAP在途数量接口传参="+jsonData);
                    String result = sapApiService.sendSap(sapLinkUrl, jsonData, "SRM调用SAP在途数量接口", delivery.getTenantId());
                    logger.info("接口返回数据===================》"+result);
                }
            }
        } catch (Exception e) {
            logger.info("SRM->SAP供应商送货在途数据同步接口异常",e.getMessage());
        }
        return null;
    }
}
