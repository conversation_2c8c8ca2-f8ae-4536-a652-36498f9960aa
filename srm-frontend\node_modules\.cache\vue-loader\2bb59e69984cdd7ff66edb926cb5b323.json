{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue?vue&type=template&id=a361ec26&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1754374677795}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n<div style=\"background-color: white;height: 100%\">\n<el-row>\n  <el-col :span=\"24\">\n    <el-card class=\"box-card\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>数据统计</span>\n      </div>\n      <div>\n        <PanelGroup v-if=\"userInfo.entType == 1\"></PanelGroup>\n        <VendorPanelGroup v-else></VendorPanelGroup>\n      </div>\n    </el-card>\n  </el-col>\n  <div v-if=\"userInfo.entType != 1\">\n    <el-col :span=\"16\">\n      <el-card class=\"box-card\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span>业务单据消息列表</span>\n        </div>\n        <div>\n          <d-table ref=\"listTable\" :data=\"massageList\" hasC height=\"calc(58vh - 100px)\">\n            <el-table-column prop=\"content\" label=\"消息内容\" align=\"center\" show-overflow-tooltip>\n              <template slot-scope=\"scope\">\n                <el-link type=\"primary\" @click=\"openForm(scope.row)\">{{scope.row.content}}</el-link>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"createDate\" label=\"发出时间\" align=\"center\" show-overflow-tooltip width=\"150\"/>\n          </d-table>\n          <d-pagination :total=\"total\" :page.sync=\"queryParam.page\" :limit.sync=\"queryParam.limit\" @pagination=\"initMassageList\"/>\n        </div>\n      </el-card>\n    </el-col>\n    <el-col :span=\"8\">\n      <el-card class=\"box-card\" style=\"height: 10%\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span>公告通知列表</span>\n        </div>\n        <div>\n          <d-table ref=\"listTable\" :data=\"noticeMsgList\" height=\"calc(63vh - 100px)\">\n            <el-table-column prop=\"content\" label=\"消息内容\" align=\"center\" show-overflow-tooltip>\n              <template slot-scope=\"scope\">\n                <el-link type=\"primary\" @click=\"openForm(scope.row)\">{{scope.row.content}}</el-link>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"createDate\" label=\"发出时间\" align=\"center\" show-overflow-tooltip width=\"150\"/>\n          </d-table>\n        </div>\n      </el-card>\n    </el-col>\n  </div>\n  <div v-else>\n    <el-col :span=\"16\">\n      <el-card class=\"box-card\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span>业务单据消息列表</span>\n        </div>\n        <div>\n          <d-table ref=\"listTable\" :data=\"massageList\" hasC height=\"calc(58vh - 100px)\">\n            <el-table-column prop=\"content\" label=\"消息内容\" align=\"center\" show-overflow-tooltip>\n              <template slot-scope=\"scope\">\n                <el-link type=\"primary\" @click=\"openForm(scope.row)\">{{scope.row.content}}</el-link>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"createDate\" label=\"发出时间\" align=\"center\" show-overflow-tooltip width=\"150\"/>\n          </d-table>\n          <d-pagination :total=\"total\" :page.sync=\"queryParam.page\" :limit.sync=\"queryParam.limit\" @pagination=\"initMassageList\"/>\n        </div>\n      </el-card>\n    </el-col>\n    <el-col :span=\"8\">\n      <el-card class=\"box-card\" style=\"height: 10%\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span>快速通道</span>\n        </div>\n        <div style=\"height: calc(63vh - 102px)\">\n\n        </div>\n      </el-card>\n    </el-col>\n  </div>\n</el-row>\n</div>\n", null]}