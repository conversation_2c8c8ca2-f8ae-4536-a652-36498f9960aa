/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dian.client.base.BaseClient;
import com.dian.client.dm.DmClient;
import com.dian.client.sys.SysClient;
import com.dian.common.dto.UserDto;
import com.dian.common.exception.RRException;
import com.dian.common.local.UserLocal;
import com.dian.common.log.TraceLoggerFactory;
import com.dian.common.server.CommonService;
import com.dian.common.utils.*;
import com.dian.common.validator.Assert;
import com.dian.common.validator.group.AddGroup;
import com.dian.common.validator.group.UpdateGroup;
import com.dian.common.vo.UserSimpleVO;
import com.dian.enums.WhetherEnum;
import com.dian.k3cloud.vo.purchaseOrder.ErpPurOrderReq;
import com.dian.k3cloud.vo.purchaseOrder.PurchaseOrderVo;
import com.dian.modules.base.service.ConfigService;
import com.dian.modules.base.vo.CurrencyVO;
import com.dian.modules.base.vo.VendorVO;
import com.dian.modules.dm.vo.DeliveryItemVO;
import com.dian.modules.dm.vo.DeliveryPlanItemVO;
import com.dian.modules.enums.common.JinDieOrderTypeEnum;
import com.dian.modules.enums.common.OperBillEnum;
import com.dian.modules.enums.common.OperTypeEnum;
import com.dian.modules.enums.order.*;
import com.dian.modules.enums.sys.Ent_EntTypeEnum;
import com.dian.modules.k3cloud.service.PurchaseOrderJoggleService;
import com.dian.modules.order.PurchaseItemVO;
import com.dian.modules.order.dao.PurDao;
import com.dian.modules.order.entity.*;
import com.dian.modules.order.query.PurQuery;
import com.dian.modules.order.service.*;
import com.dian.modules.order.vo.*;
import com.dian.modules.sys.entity.SysEntEntity;
import com.dian.modules.sys.vo.SysUserVO;
import com.dian.vo.EmailMessageVo;
import io.seata.spring.annotation.GlobalTransactional;
import jdk.nashorn.internal.objects.annotations.Where;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购订单服务实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-24 09:36:32
 */
@Service("purService")
public class PurServiceImpl extends ServiceImpl<PurDao, PurEntity> implements PurService {

    protected Logger logger = TraceLoggerFactory.getLogger(getClass());

    @Autowired
    private BaseClient baseClient;
    @Autowired
    public CommonService commonService;
    @Autowired
    public ConfigService configService;
    @Autowired
    public PurItemService purItemService;
    @Autowired
    public PurRequestService purRequestService;
    @Autowired
    public PurRequestItemService purRequestItemService;

    @Autowired
    public PurPlanItemService purPlanItemService;
    @Autowired
    public SaleServiceImpl saleService;
    @Autowired
    public SysClient sysClient;
    @Autowired
    public DmClient dmClient;
    @Autowired
    private PurchaseOrderJoggleService purchaseOrderJoggleService;
    @Autowired
    private MaterialListService materialListService;


    public static final int BILL_INQUIRY_ORDER=1;//生成类型 1-变更单
    public static final int BILL_REPORT_ORDER=2;//生成类型 2-报价单
    public static final int BILL_PUR_ORDER=3;//生成类型 3-采购订单
    public static final int BILL_SALE_ORDER=4;//生成类型 4-销售订单
    public static final int BILL_SEND_ORDER=5;//生成类型 5-送货单
    public static final int BILL_RECEIVER_ORDER=6;//生成类型 6-收货单
    public static final int BILL_RETURN_ORDER=7;//生成类型 7-退货单
    public static final int BILL_ACCOUNT_ORDER=8;//生成类型 8-对账单
    public static final int BILL_INVOICE_ORDER=9;//生成类型 9-发票单
    public static final int BILL_SEARCH_ORDER=10;//生成类型 10-索取单
    public static final int BILL_VENDOR_STATUS=11;//生成类型 11-交易状态(供应商)

    public static final int TYPE_CREATE=1;//操作类型 1-创建
    public static final int TYPE_PUBLISH=2;//操作类型 2-发布
    public static final int TYPE_CONFIRM=3;//操作类型 3-确认答交
    public static final int TYPE_UPDATE=4;//操作类型 4-修改
    public static final int TYPE_DELETE=5;//操作类型 5-删除/作废
    public static final int TYPE_DOWNLOAD=6;//操作类型 6-下载文件
    public static final int TYPE_IMPORT=7;//操作类型 7-导入
    public static final int TYPE_CHECK=8;//操作类型 8-审核
    public static final int TYPE_ALTER=9;//操作类型 9-变更
    public static final int TYPE_SELECT=10;//操作类型 10-查看
    public static final int TYPE_DISAGREE=11;//操作类型 11-退回答交
    public static final int TYPE_UPLOAD=12;//操作类型 12-上传文件
    public static final int TYPE_SUBMIT=13;//操作类型 13-索取
    public static final int TYPE_UPLOAD_CONFIRM=14;//操作类型 14-文档确认
    public static final int TYPE_PRINT=15;//操作类型 15-打印
    public static final int TYPE_BARCODE=16;//操作类型 16-条码
    public static final int TYPE_INTERFACE=17;//操作类型 17-接口下载
    public static final int TYPE_WRITE_BACK=18;//操作类型 18-ERP数据回写
    public static final int VENDOR_STATUS_TWO=19;//供应商状态操作类型 1-交易中
    public static final int VENDOR_STATUS_THREE=20;//供应商状态操作类型 2-停止交易



    public static final String SIGN_NOVISIT="A";//标记 A-未查看(采购方发布订单后供应方未查看)
    public static final String SIGN_VISIT="B";//标记 B-查看(采购方发布订单后供应方已查看)
    public static final String SIGN_FILE="C";//标记 C-上传文件
    public static final String SIGN_BUSY="D";//标记 D-急单
//	public static final int SIGN_=3;//标记

    /**
     * 采购订单分页
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        params.put("userId",commonService.getUserId());
        params.put("userName",commonService.getUserName());
        params.put("tenantId",commonService.getTenantId());
        List<HashMap<String, Object>> list = getBaseMapper().getPurList(page, params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public List<PurchaseOrderSearchVO> queryByTenantLastUpdatePurchaseTimeList(Date lastUpdateTime) {
        Map<String, Object> params = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(!StrUtil.isBlankIfStr(lastUpdateTime)) {
            String format = sdf.format(lastUpdateTime);
            params.put("lastUpdateTime", format);
        }
        params.put("itemStat",1);
        params.put("tenantId",commonService.getTenantId());
        List<HashMap<String, Object>> list= this.getBaseMapper().queryByLastUpdateTime(params);
        List<PurchaseOrderSearchVO> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
               resultList = BeanConverter.convertList(list, PurchaseOrderSearchVO.class);
        }
        return resultList;
    }

    /**
    *  采购订单新增
    * @param purEntity
    * @return
    */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean saveInfo(PurEntity purEntity) {

        //设置编码，等基础默然初始化数据设置
        //purEntity.setOrderDate(new Date());


        //数据完整性校验
        this.paramsCheck(purEntity,AddGroup.class);

//        if (purEntity.getOssId() != null) {
//            QueryWrapper<SysOssEntity> queryWrapper = new QueryWrapper();
//            queryWrapper.eq("head_Id", Long.valueOf(purEntity.getOssId()));
//            queryWrapper.eq("table_Name", "order_pur");
//            List<SysOssEntity> serviceLine = sysOssService.list(queryWrapper);
//            if (CollectionUtils.isNotEmpty(serviceLine)) {
//                for (SysOssEntity sysOss : serviceLine) {
//                    sysOss.setHeadId(twBargainsApplyEntity.getId());
//                    sysOssService.updateById(sysOss);
//                }
//            }
//
//        }
        logger.error("人民币",purEntity.getCompanyName());
        //保存主表
       this.save(purEntity);

        //保存从表
        if (CollectionUtils.isNotEmpty(purEntity.getPurItemEntityList())) {
            List<PurItemEntity> detailList = purEntity.getPurItemEntityList();
            for (PurItemEntity detail : detailList) {
                detail.setPurId(purEntity.getId());
            }
            purItemService.saveBatch(purEntity.getPurItemEntityList());
        }

        JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CREATE.getValue(), purEntity.getId(), "新增采购订单" + purEntity.getPurNo(), purEntity.getPurNo());
        int i= Integer.valueOf(jsonObject.get("code")+"");
        if(i==500){
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        return true;
    }



    /**
     *采购订单更新
     * @param purEntity
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateInfo(PurEntity purEntity) {

        //修改状态校验
        this.updateCheck(purEntity.getId());

        //主表数据完整性校验
        this.paramsCheck(purEntity, UpdateGroup.class);

        //更新主表
        this.updateById(purEntity);

        //更新从表
        purItemService.updateInfo(purEntity);

        JSONObject jsonObject = this.saveOperMsg(3, 6, purEntity.getId(), "修改采购订单" + purEntity.getPurNo(), purEntity.getPurNo());
        int i= Integer.valueOf(jsonObject.get("code")+"");
        if(i==500){
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        return true;
    }

    /**
     *采购订单删除
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean deleteInfo(Long id) {

        //删除状态校验
        this.deleteCheck(id);
        PurEntity purEntity = this.getById(id);
        JSONObject jsonObject =
                this.saveOperMsg(
                        OperBillEnum.SALEORDER.getValue(),
                        OperTypeEnum.DELETE.getValue(),
                        purEntity.getId(),
                        "删除采购订单" + purEntity.getPurNo(),
                        purEntity.getPurNo());
        int i = Integer.valueOf(jsonObject.get("code") + "");
        if (i == 500) {
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        //更新从表
        purItemService.deleteInfo(id);
        //更新主表
        this.remove(new QueryWrapper<PurEntity>().eq("id",id));

        return true;
    }




    /**
     *采购订单更新
     * @param purEntity
     * @return
     */
    private boolean updateByERP(PurEntity purEntity) {
        //查询erp订单是否已存在
        PurEntity entity  = this.queryByPurNo(purEntity);
        if (entity==null){
            throw new RRException( String.format("[%s] 该单在系统中不存在,不能进行修改存在", purEntity.getPurNo()));
        }
        purEntity.setId(entity.getId());
        purEntity.setStat(3); // 已确认
        int count = entity.getChangeCount() + 1;
        purEntity.setChangeCount(count);
        //保存从表
        BigDecimal totalAmount=BigDecimal.ZERO;//含税总金额
        for (PurItemEntity purItemEntity : purEntity.getPurItemEntityList()) {
            totalAmount=totalAmount.add(purItemEntity.getGstAmount());
        }
        purEntity.setTotalAmount(totalAmount);
        //更新主表
        this.updateById(purEntity);
        for (PurItemEntity purItemEntity : purEntity.getPurItemEntityList()) {
            PurItemEntity  purItem = purItemService.queryBySourceItemId(purItemEntity.getSourceItemId(),purEntity.getId());
            if(purItem!=null) {
                //采购订单修改检验订单的入库数量与接口修改订单数量比较
                if (purItemEntity.getOrderNum().compareTo(purItem.getErpMasterNum()) == -1) {
                    throw new RRException(String.format("[%s] 该单在系统中的已制单数量大于订单数,不能进行修改", purEntity.getPurNo()));
                }
                //通过采购订单查询计划所用数
                BigDecimal num = dmClient.qualityByPlan(purEntity.getId(), purItem.getId());
                BigDecimal decimal=new BigDecimal(0);
                //在计划的总数大于等于订单数量
                if(num.compareTo(purItem.getOrderNum())> -1) {
                    //占用数量=计划的总数-待送货量
                    decimal = num.subtract(purItem.getWaitNum());
                }else {
                    //在小于订单数量时  占用数量=计划的总数
                    decimal=num;
                }
                //订单数据只有不等于时去比较占用数量
                //订单修改数量比较占用数量
                if(purItemEntity.getOrderNum().compareTo(purItem.getOrderNum())!=0) {
                    if (purItemEntity.getOrderNum().compareTo(decimal) == -1) {
                        throw new RRException(String.format("[%s] 该单在系统中的订单数量已被计划占用,不能进行修改", purEntity.getPurNo()));
                    }
                }
                purItem.setPurId(purEntity.getId());
                purItem.setSeq(purItemEntity.getSeq());
                // 物料信息
                purItem.setGoodsId(purItemEntity.getGoodsId());
                purItem.setGoodsName(purItemEntity.getGoodsName());
                purItem.setGoodsModel(purItemEntity.getGoodsModel());
                purItem.setGoodsErpCode(purItemEntity.getGoodsErpCode());
                purItem.setClassCode(purItemEntity.getClassCode());
                purItem.setGoodsClassName(purItemEntity.getGoodsClassName());
                purItem.setOrderNum(purItemEntity.getOrderNum());  // 采购订单数量
                purItem.setItemStat(purItem.getItemStat());
                BigDecimal subtract = purItemEntity.getOrderNum().subtract(purItem.getMakeNum());
                purItem.setWaitNum(subtract);
                // 单位
                purItem.setUomId(purItemEntity.getUomId());
                purItem.setUomCode(purItemEntity.getUomCode());
                purItem.setUomName(purItemEntity.getUomName());
                // 税码
                purItem.setRateName(purItemEntity.getRateName());  // 税码
                purItem.setRateVal(purItemEntity.getRateVal()); // 税率
                purItem.setGstPrice(purItemEntity.getGstPrice()); // 含税单价
                purItem.setTaxPrice(purItemEntity.getTaxPrice()); // 采购凭证中的净价
                purItem.setGstAmount(purItemEntity.getGstAmount()); // 含税金额
                purItem.setTaxAmount(purItemEntity.getTaxAmount()); // 不含税金额
                // 币种
                purItem.setCurrencyId(purItemEntity.getCurrencyId());
                purItem.setCurrencyName(purItemEntity.getCurrencyName());
                // / 没有仓库则不保存
                purItem.setWarehouseId(purItemEntity.getWarehouseId()); // 仓库id
                purItem.setWarehouseCode(purItemEntity.getWarehouseCode());
                purItem.setWarehouseName(purItemEntity.getWarehouseName()); // 库存地点
                purItem.setPlans(purItemEntity.getPlans()); // 计划交货天数
                purItem.setReplyDate(purItem.getReplyDate());
                purItem.setItemStat(1);
                purItemService.updateById(purItem);
            }else {
                purItem=new PurItemEntity();
                purItem.setPurId(purEntity.getId());
                purItem.setSeq(String.valueOf(purEntity.getPurItemEntityList().size()-1));
                purItem.setTenantId(commonService.getTenantId());
                purItem.setSourceItemId(purItemEntity.getSourceItemId());
                // 物料信息
                purItem.setGoodsId(purItemEntity.getGoodsId());
                purItem.setGoodsName(purItemEntity.getGoodsName());
                purItem.setGoodsModel(purItemEntity.getGoodsModel());
                purItem.setGoodsErpCode(purItemEntity.getGoodsErpCode());
                purItem.setGoodsCode(purItemEntity.getGoodsErpCode());
                purItem.setClassCode(purItemEntity.getClassCode());
                purItem.setGoodsClassName(purItemEntity.getGoodsClassName());
                purItem.setOrderNum(purItemEntity.getOrderNum());  // 采购订单数量
                // 单位
                purItem.setUomId(purItemEntity.getUomId());
                purItem.setUomCode(purItemEntity.getUomCode());
                purItem.setDeliveryDate(purItemEntity.getDeliveryDate());
                purItem.setUomName(purItemEntity.getUomName());
                // 税码
                purItem.setRateName(purItemEntity.getRateName());  // 税码
                purItem.setRateVal(purItemEntity.getRateVal()); // 税率
                purItem.setGstPrice(purItemEntity.getGstPrice()); // 含税单价
                purItem.setTaxPrice(purItemEntity.getTaxPrice()); // 采购凭证中的净价
                purItem.setGstAmount(purItemEntity.getGstAmount()); // 含税金额
                purItem.setTaxAmount(purItemEntity.getTaxAmount()); // 不含税金额
                // 币种
                purItem.setCurrencyId(purItemEntity.getCurrencyId());
                purItem.setCurrencyName(purItemEntity.getCurrencyName());
                purItem.setWaitNum(purItemEntity.getOrderNum()); // 待送货数量
                // / 没有仓库则不保存
                purItem.setWarehouseId(purItemEntity.getWarehouseId()); // 仓库id
                purItem.setWarehouseCode(purItemEntity.getWarehouseCode());
                purItem.setWarehouseName(purItemEntity.getWarehouseName()); // 库存地点
                purItem.setPlans(purItemEntity.getPlans()); // 计划交货天数
                purItem.setDeliveryDate(purItemEntity.getDeliveryDate()); // 交货日期
                purItem.setReplyDate(new Date());
                purItem.setAddress(purItemEntity.getAddress());
                purItem.setItemStat(1);
//                if (entity.getStat().equals(PurStatEnum.CONFIRM.getValue())) {
//                    purItem.setItemStat(4);//已确认
//                } else if(entity.getStat().equals(PurStatEnum.REPLYEXCEPTION.getValue())){
//                    purItem.setItemStat(2);//答交异常
//                }else if(entity.getStat().equals(PurStatEnum.REFUSE.getValue())) {
//                    purItem.setItemStat(3);//已拒绝
//                }
                purItem.setDeleteFlag(0);
                purItemService.save(purItem);
            }
        }
        //获取数据库中的明细数据
        List<PurItemEntity> purItemEntities = purItemService.queryByPurIdList(purEntity.getId());
       List<PurItemEntity> returnList = new ArrayList<>();
        boolean returnFlag;
        for (PurItemEntity purItemEntity : purItemEntities) {
            returnFlag = false;
            //比较传入的采购订单明细
            for (PurItemEntity itemEntity : purEntity.getPurItemEntityList()) {
                if (purItemEntity.getSourceItemId().equals(itemEntity.getSourceItemId())) {
                    returnFlag=true;
                }
            }
            if(returnFlag==false){
                //不存在传入数据中
                returnList.add(purItemEntity);
            }
        }
        if (CollectionUtils.isNotEmpty(returnList)) {
            for (PurItemEntity purItemEntity : returnList) {
                BigDecimal  num  = dmClient.qualityByPlan(purItemEntity.getPurId(),purItemEntity.getId());
                if(!StrUtil.isBlankIfStr(num)&&num.compareTo(BigDecimal.ZERO)== 1) {
                    throw new RRException("该单在系统中的订单已被计划单据占用,不能进行修改 单号为"+ purEntity.getPurNo()+"物料号为"+purItemEntity.getGoodsCode());
                }
                List<DeliveryItemVO> deliveryItemVOS = dmClient.queryBySaleItemId(purEntity.getId(), purItemEntity.getId());
                if (CollectionUtils.isNotEmpty(deliveryItemVOS)) {
                    throw new RRException("该单在系统中的订单已被送货单据占用,不能进行修改 单号为"+ purEntity.getPurNo()+"物料号为"+purItemEntity.getGoodsCode());
                }
                purItemService.removeById(purItemEntity);
            }
        }


        JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CHANGE.getValue(), purEntity.getId(), "修改成功订单" + purEntity.getPurNo(), purEntity.getPurNo());
        int i= Integer.valueOf(jsonObject.get("code")+"");
        if(i==500){
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        return true;
    }

    private PurEntity queryByPurNo(PurEntity purEntity) {
        QueryWrapper<PurEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("pur_no",purEntity.getPurNo());
        queryWrapper.eq("source_id",purEntity.getSourceId());
        return this.getOne(queryWrapper);
    }


    /**
     *采购订单发布
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean release(Long id) {

        //删除状态校验
//        this.releaseCheck(id);

        //更新从表
        purItemService.deleteInfo(id);

        //更新主表
        this.remove(new QueryWrapper<PurEntity>().eq("id",id));

        return true;
    }


    /**
     * 采购订单详情
     * @param id
     * @return
     */
    @Override
    public PurEntity getInfo(Long id) {
        UserDto userInfo = commonService.getUser();
        List<String> lookPricePermsList = userInfo.getPermsList().stream().filter(perms -> perms.equals("order:pur:lookPrice")).collect(Collectors.toList());
        Integer ifLookPricePerms = WhetherEnum.NO.getCode();
        if (CollectionUtil.isNotEmpty(lookPricePermsList)){
            ifLookPricePerms = WhetherEnum.YES.getCode();
        }
        PurEntity purEntity = getById(id);
        purEntity.setIfLookPrice(ifLookPricePerms);
        JSONObject companyInfo = baseClient.getCompanyInfo().getJSONObject("data");

        if (ObjectUtil.isNotEmpty(companyInfo)) {
            if (companyInfo.getString("companyContact") != null) {
                purEntity.setCompanyName(companyInfo.getString("companyContact"));
            }
            if (companyInfo.getString("companyTel") != null) {
                purEntity.setCompanyTel(companyInfo.getString("companyTel"));
            }
        }

        List<PurItemEntity> lineList = purItemService.queryLineList(id);

        BigDecimal goodsSum = BigDecimal.ZERO;
        /**
         * 系统参数
         * 1 - 整单答交
         * 2 - 明细答交
         */
        purEntity.setOrderReply(sysClient.getValueByKeyAndTenantId("orderReply", purEntity.getTenantId()));
        if (CollectionUtil.isNotEmpty(lineList)){
            Map<String, PurchaseOrderVo> erpOrderMap = new HashMap<>();
            // 查询ERP采购订单数据
            if (JinDieOrderTypeEnum.OUTSOURCING.getValue().equals(purEntity.getOrderType()) && ObjectUtil.isNotEmpty(purEntity.getSourceId())){
                ErpPurOrderReq purOrderReq = new ErpPurOrderReq();
                purOrderReq.setFids(purEntity.getSourceId().toString());
                List<PurchaseOrderVo> erpOrderList = purchaseOrderJoggleService.queryErpPurOrderPickQtyList(purOrderReq);
                erpOrderMap = erpOrderList.stream()
                        .filter(vo -> StrUtil.isNotEmpty(vo.getFEntryID()))
                        .collect(Collectors.toMap(
                                PurchaseOrderVo::getFEntryID,
                                Function.identity(),
                                (existing, replacement) -> existing
                        ));
            }
            for (PurItemEntity item:lineList) {
                item.setIfLookPrice(purEntity.getIfLookPrice());
                goodsSum = goodsSum.add(ObjectUtil.isNotEmpty(item.getGstAmount()) ? item.getGstAmount() : BigDecimal.ZERO );
                // 订单的未制单数量 = 订单数量 - 已制单数量
                item.setUnMakeNum(item.getOrderNum().subtract(item.getMakeNum()));
                item.setDeliveryStatus("未开始送货");
                // 订单行已送数量大于0 且 订单行已送数量小于订单行数量
                if (item.getFixNum().compareTo(BigDecimal.ZERO) > 0 && item.getFixNum().compareTo(item.getOrderNum()) < 0 ){
                    item.setDeliveryStatus("送货进行中");
                }
                // 订单行已送数量等于订单行数量
                if (item.getFixNum().compareTo(item.getOrderNum()) == 0){
                    item.setDeliveryStatus("已完成送货");
                }
                // 订单行已收数量等于订单行数量
                if (item.getErpMasterNum().compareTo(item.getOrderNum()) == 0){
                    item.setDeliveryStatus("已完成入库");
                }
                if (CollectionUtil.isNotEmpty(erpOrderMap) && ObjectUtil.isNotEmpty(item.getSourceId())){
                    PurchaseOrderVo purchaseOrderVo = erpOrderMap.get(item.getSourceItemId().toString());
                    if (ObjectUtil.isNotEmpty(purchaseOrderVo)){
                        // 订单行委外领料套数
                        item.setErpOutPickQty(StrUtil.isEmptyIfStr(purchaseOrderVo.getFRemainStockINQty()) ? null : new BigDecimal(purchaseOrderVo.getFRemainStockINQty()));
                    }
                }
                // 查询用料清单
                List<MaterialListEntity> materialList = materialListService.queryListByPurOrderLineId(item.getId());
                item.setMaterialList(materialList);
            }
        }
        purEntity.setGoodsSum(goodsSum);
        purEntity.setPurItemEntityList(lineList);
        JSONObject jsonObject = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.INFO.getValue(), purEntity.getId(), "查看采购订单" + purEntity.getPurNo(), purEntity.getPurNo());
        int i= Integer.valueOf(jsonObject.get("code")+"");
        if(i==500){
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        return purEntity;
    }

    @Override
   @Transactional(rollbackFor = Throwable.class)
/*    @GlobalTransactional(rollbackFor = Exception.class)*/
    public JSONObject saveOperMsg(int operBill,int operType,Long soureHeadId,String remark,String attr1 ){
        /**
         * 保存业务操作日志
         *   oper_bill int
         生成类型 1-询价单；2-报价单;3-采购订单;4-销售订单;5-送货单;6-收货单;7-退货单;8-对账单;9-发票单
         oper_type 操作类型 1-创建；2-发布;3-接受;4-提交;具体以单据类型确定
         soure_head_id 来源单据主id
         remark :操作说明 查看采购订单PO20210323000003
         */
        JSONObject jsonObject1 = null;
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tenantId",commonService.getTenantId());
//        jsonObject.put("tenantName",commonService.get());
            jsonObject.put("operBill",operBill);
            jsonObject.put("operType",operType);
            jsonObject.put("soureHeadId",soureHeadId);
            jsonObject.put("remark",remark);
            jsonObject.put("attr1",attr1);
            jsonObject.put("attr2",commonService.getUserName());
            jsonObject1 = baseClient.saveOperMsg(jsonObject);
            int i = Integer.valueOf(jsonObject1.get("code")+"");
        } catch (NumberFormatException e) {
            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.put("code",500);
            jsonObject2.put("msg","保存业务操作信息失败"+e.getMessage());
            return jsonObject2;
        }
        return jsonObject1;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateStatus(PurConfirmVO purConfirmVO) {
        PurEntity purEntity =getById(purConfirmVO.getId());
        if(purEntity==null){
            throw new RRException("无效的采购订单");
        }
        JSONObject jsonObject =
                this.saveOperMsg(
                        OperBillEnum.SALEORDER.getValue(),
                        OperTypeEnum.PUBLISH.getValue(),
                        purEntity.getId(),
                        "发布采购订单" + purEntity.getPurNo(),
                        purEntity.getPurNo());
        int i = Integer.valueOf(jsonObject.get("code") + "");
        if (i == 500) {
            throw new RRException(String.format("[%s] "+jsonObject.get("msg"),purEntity.getPurNo()));
        }
        //订单操作类型与当前订单状态判断
        if(purConfirmVO.getOperType().equals(3)) {
            checkCheck(purEntity, PurStatEnum.WAITREPLY);
        }
        //答交模式(1-整单答交;2-明细答交)
        String orderReply=sysClient.getValueByKeyAndTenantId("orderReply",purEntity.getTenantId());

        //参数为空默认1整单答交
        if(StringUtils.isEmpty(orderReply)){
            orderReply="1";
        }

        //整单答交
        if("1".equals(orderReply)){
            if(purConfirmVO.getPurList().size()!=purItemService.queryByPurIdItemCount(purConfirmVO.getId())){
                throw new RRException(String.format("当前采购订单为整单答交"));
            }
        }

        if(purEntity.getStat().equals(PurStatEnum.AUDITED.getValue())){
                throw new RRException(String.format("重复审核采购订单"+purEntity.getPurNo()+"失败"));
        }else if(purEntity.getStat().equals(PurStatEnum.WAITREPLY.getValue())){
            // 采购订单交期符复
            for (PurConfirmItemVO itemVO:purConfirmVO.getPurList()) {
                if(purItemService.checkOrderReplyDate(itemVO.getPurId(),itemVO.getId(),itemVO.getReplyDate())){
                    itemVO.setItemStat(PurlineStatEnum.CONFIRM.getValue());
                }else{
                    itemVO.setItemStat(PurlineStatEnum.REPLYEXCEPTION.getValue());
                }
            }
            this.updatePurInfo(purConfirmVO,OperTypeEnum.CONFIRM);
        }
        /*else if(purEntity.getStat()==PurStatEnum.DELETE.getValue()){
            if(scmOrderPurEntity_HI.getStat()!=1 && scmOrderPurEntity_HI.getStat()!=2){
                throw new RRException(String.format("重复作废采购订单"+scmOrderPurEntity_HI.getPurNo()+"失败"));
            }
            Map<String,Object> paramsMap1 = new HashMap<>();
            paramsMap1.put("purId", purEntity.getId());
            paramsMap1.put("deleteFlag", 0);
            paramsMap1.put("itemStat",-1);
            List<PurItemEntity> purList = purItemService.findList(paramsMap1);
            if(purList == null || CollectionUtils.isEmpty(purList)){
                throw new RRException(String.format("作废采购订单"+scmOrderPurEntity_HI.getPurNo()+"失败,未找到当前订单明细行状态为待发布的数据"));
            }else{
                for (PurItemEntity scmOrderPurItemEntity_hi:
                        purList) {
                    scmOrderPurItemEntity_hi.setItemStat(0);
                    purItemService.updateById(scmOrderPurItemEntity_hi);
                }
            }
            remark="作废采购订单"+scmOrderPurEntity_HI.getPurNo();
            operType=OperTypeEnum.DELETE.getValue();
        }*/

        return true;
    }

    /**
     * 采购方退回或确认采购订单交期
     * @param purConfirmVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateOrderStatus(PurConfirmVO purConfirmVO) {
        PurEntity purEntity = this.getById(purConfirmVO.getId());
        if(purEntity==null){
            throw new RRException("无效的采购订单");
        }
        // 采购方名称
//        JSONObject tenantJson = baseClient.getCompanyInMail(purEntity.getTenantId());
//        String tenantNames = "";
//        if (!tenantJson.isEmpty()){
//            tenantNames = tenantJson.get("company").toString();
//        }

        List<PurItemEntity> purItemEntities = new ArrayList<>();
        if (OrderOperateEnum.TCONFIRM.getValue().equals(purConfirmVO.getOperType())) { /* 采购方确认 */
            for (PurConfirmItemVO purItemListVo : purConfirmVO.getPurList()) {
                PurItemEntity purItems = purItemService.getById(purItemListVo.getId());
                if(purItems.getItemStat()==0){
                    throw new RRException("采购订单已关闭，禁止再次操作！");
                }
                purItems.setItemStat(PurlineStatEnum.CONFIRM.getValue());
                // 如果真实答交时间为空，状态为已为答交状态，为空则加入当前日期，方便后面做绩效
                if (PurlineStatEnum.CONFIRM.getValue().equals(purItems.getItemStat())){
                    purItems.setTrueReplyDate(new Date());
                }
                purItems.setPurchaseRemark(purItemListVo.getPurchaseRemark()); // 采购方备注
                purItemEntities.add(purItems);
            }
            purEntity.setPurItemEntityList(purItemEntities);
            purEntity.setStat(PurStatEnum.CONFIRM.getValue());
            saleService.replyOrder(purEntity);
            // 发送站内信及邮箱信息
            String content = String.format("[%s]已同意并确认采购订单[%s]中答交异常状态的订单数据",purEntity.getDeptName(),purEntity.getPurNo());
            sendEmailAndMessage(purEntity, content,purEntity.getDeptName());
        } else if (OrderOperateEnum.TREFUSEREPLY.getValue().equals(purConfirmVO.getOperType())) { /* 采购方拒绝 */
            for (PurConfirmItemVO purItemListVo : purConfirmVO.getPurList()) {
                PurItemEntity purItems = purItemService.getById(purItemListVo.getId());
                if(purItems.getItemStat()==0){
                    throw new RRException("采购订单已关闭，禁止再次操作！");
                }
                purItems.setItemStat(PurlineStatEnum.WAITREPLY.getValue());
                purItems.setPurchaseRemark(purItemListVo.getPurchaseRemark()); // 采购方备注
                purItemEntities.add(purItems);
            }
            // 主从表信息保存
            purEntity.setPurItemEntityList(purItemEntities);
            purEntity.setStat(PurStatEnum.WAITREPLY.getValue());
            saleService.replyOrder(purEntity);
            //采购方退回订单时以邮箱消息和站内业务消息的方式通知供应商;
            JSONObject mailJson = new JSONObject();
            mailJson.put("tenantId",purEntity.getTenantId());
            mailJson.put("userId",0L);
            mailJson.put("content",purEntity.getDeptName()+"退回销售订单:"+purEntity.getPurNo());
            mailJson.put("menuTitle","销售订单详情");//菜单标题
            mailJson.put("url","order/produce/vendor?id="+purEntity.getId());//菜单地址
            sysClient.sendMail(mailJson);
        }
        purEntity.setPurItemEntityList(purItemEntities);
        return true;
    }



    /**
     * 采购方确认/退回订单时发送邮箱消息
     * @param purEntity
     */
    @Override
    public void sendConfirmOrRefuseEmail(PurEntity purEntity) {
        JSONObject tenantJson = baseClient.getCompanyInMail(purEntity.getTenantId());
        String tenantNames = "";
        if (!tenantJson.isEmpty()){
            tenantNames = tenantJson.get("company").toString();
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId",purEntity.getTenantId());//发送方租户Id
        if(PurStatEnum.WAITREPLY.getValue().equals(purEntity.getStat())) {//拒绝
            // 保存操作记录
//            saleService.saveOperationMessage(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CONFIRM.getValue(), purEntity.getId(), "退回销售订单"+purEntity.getPurNo(), purEntity.getPurNo());
            jsonObject.put("title", "销售订单-退回-" + purEntity.getVendorName());//发送方租户名称
            jsonObject.put("content","退回了销售订单");//发送内容
            jsonObject.put("orderStat","待答交");//单据状态
            jsonObject.put("setingCode","orderReturn");//通知编码(业务节点标识)
        }else if(PurStatEnum.CONFIRM.getValue().equals(purEntity.getStat())){
            // 保存操作记录
//            saleService.saveOperationMessage(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CONFIRM.getValue(), purEntity.getId(), "已确认销售订单"+purEntity.getPurNo(), purEntity.getPurNo());
            jsonObject.put("title", "销售订单-确认-" + purEntity.getVendorName());//发送方租户名称
            jsonObject.put("content","确认了答交异常的销售订单");//发送内容
            jsonObject.put("orderStat","已确认");//单据状态
            jsonObject.put("setingCode","confirmExceptionOrder");//通知编码(业务节点标识)
        }
        jsonObject.put("receiver",purEntity.getVendorName());//收件企业
        jsonObject.put("url","order/produce/vendor");//收件企业
        jsonObject.put("orderId",purEntity.getId());//收件企业
//        jsonObject.put("sender","创维空调科技（安徽）有限公司");//发件企业
        jsonObject.put("sender",tenantNames);//发件企业
        jsonObject.put("orderNo",purEntity.getPurNo());//单据号
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        jsonObject.put("publishDate", sdf.format(new Date()));
        jsonObject.put("receiverEntId",purEntity.getVendorId());//接收方租户ID
        jsonObject.put("roleCode","21005,tenantAdmin");//供应商业务员和管理员
        // 获取供应商信息-邮箱
        JSONObject vendorJson = baseClient.getVendor(purEntity.getTenantId(), purEntity.getVendorCode());
        if (vendorJson != null){
            String vendorEmail = vendorJson.getString("vendorEmail");
            jsonObject.put("receiver",vendorJson.getString("vendorFullName"));//收件企业
            if(!StrUtil.isEmptyIfStr(vendorEmail)){
                jsonObject.put("receiveEmail",vendorEmail);
                String soureId = vendorJson.getString("soureId");
                jsonObject.put("vendorId",soureId);
                baseClient.sendConfirmOrRefuseEmail(jsonObject);
            }
        }
    }

    @Override
    public PageUtils progressList(Map<String, Object> params) {
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
//        logger.info(params+"---------------采购订单进度列表----------------");
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        params.put("userId",commonService.getUserId());
        params.put("tenantId",commonService.getTenantId());
//        logger.info(params+"---------------采购订单进度列表气泡----------------");
        if(!StrUtil.isEmptyIfStr(params.get("keyword"))) {
            String keyword = params.get("keyword").toString().trim();
            params.remove("keyword");
            params.put("keyword",keyword);
        }
        List<HashMap<String, Object>> list = getBaseMapper().findPagination(page, params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public Map<String, Object> countMap(Map<String, Object> params) {
        Map<String,Object> longMap = null;
        try {
            params.put("userName",commonService.getUserName());
            params.put("tenantId",commonService.getTenantId());
            params.put("userId",commonService.getUserId());
            // 未送货
            params.put("whereType",7);
            Long count1 = getBaseMapper().countMap(params);
            // 超时未收货
            params.put("whereType",8);
            Long count2 = getBaseMapper().countMap(params);
            // 未完成
            params.put("whereType",9);
            Long count3 = getBaseMapper().countMap(params);
            // 已完成
            params.put("whereType",10);
            Long count4 = getBaseMapper().countMap(params);
            // 全部
            params.put("whereType",4);
            Long count5= getBaseMapper().countMap(params);
            // 急单
            params.put("whereType",5);
            Long count6 = getBaseMapper().countMap(params);
            // 我关注的
            params.put("whereType",6);
            Long count7 = getBaseMapper().countMap(params);
            // 我的
            params.put("whereType",15);
            Long count8 = getBaseMapper().countMap(params);

            longMap = new HashMap<>();
            longMap.put("count1",count1);
            longMap.put("count2",count2);
            longMap.put("count3",count3);
            longMap.put("count4",count4);
            longMap.put("count5",count5);
            longMap.put("count6",count6);
            longMap.put("count7",count7);
            longMap.put("count8",count8);
        } catch (Exception e) {
            return null;
        }
        return longMap;
    }

    @Override
    public Map<String, Object> countMap6(Map<String, Object> params) {
        Map<String,Object> longMap = null;
        try {
            params.put("tenantId",commonService.getTenantId());
            params.put("userId",commonService.getUserId());
            Long count5= getBaseMapper().countMap(params);
            longMap = new HashMap<>();
            longMap.put("count",count5);

        } catch (Exception e) {
            return null;
        }
        return longMap;
    }


    @Override
    public Map<String, Object> countMap2(Map<String, Object> params) {
        Map<String,Object> longMap = null;
        try {
            // 我的  全部 待发布
            params.put("userName",commonService.getUserName());
            params.put("tenantId",commonService.getTenantId());
            params.put("userId",commonService.getUserId());
            params.put("whereType",15);
            Long count1 = getBaseMapper().countMap(params);
            params.put("whereType",4);
            Long count2 = getBaseMapper().countMap(params);
            params.put("whereType",11);
            Long count3 = getBaseMapper().countMap(params);
            params.put("whereType",55);
            Long count4 = getBaseMapper().countMap(params);
            params.put("whereType",3);
            Long count5 = getBaseMapper().countMap(params);

            longMap = new HashMap<>();
            longMap.put("count1",count1);
            longMap.put("count2",count2);
            longMap.put("count3",count3);
            longMap.put("count4",count4);
            longMap.put("count5",count5);

        } catch (Exception e) {
            return null;
        }
        return longMap;
    }


    @Override
    public Map<String, Object> countMap3(Map<String, Object> params) {
        Map<String,Object> longMap = null;
        try {
            // 我的  全部 待发布
            params.put("tenantId",commonService.getTenantId());
            params.put("userId",commonService.getUserId());
            params.put("whereType",11);
            Long count3 = getBaseMapper().countMap(params);

            longMap = new HashMap<>();
            longMap.put("count",count3);

        } catch (Exception e) {
            return null;
        }
        return longMap;
    }

    @Override
    public Map<String, Object> countMap4(Map<String, Object> params) {
        Map<String,Object> longMap = null;
        try {
            // 我的  全部 待发布
            params.put("tenantId",commonService.getTenantId());
            params.put("userId",commonService.getUserId());
            params.put("whereType",55);
            Long count3 = getBaseMapper().countMap(params);

            longMap = new HashMap<>();
            longMap.put("count",count3);

        } catch (Exception e) {
            return null;
        }
        return longMap;
    }

    @Override
    public Map<String, Object> countMap5(Map<String, Object> params) {
        Map<String,Object> longMap = null;
        try {
            // 我的  全部 待发布
            params.put("tenantId",commonService.getTenantId());
            params.put("userId",commonService.getUserId());
            params.put("whereType",3);
            Long count3 = getBaseMapper().countMap(params);

            longMap = new HashMap<>();
            longMap.put("count",count3);

        } catch (Exception e) {
            return null;
        }
        return longMap;
    }

    /**
     * 采购方订单下达及确认列表查询
     *  -- dlw
     * @param params
     * @return
     */
    @Override
    public PageUtils queryConfirmList(Map<String, Object> params) {
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        params.put("userId",commonService.getUserId());
        params.put("userName",commonService.getUserName());
        params.put("tenantId",commonService.getTenantId());

        //答交超时时间 临时使用
        params.put("replyMinute",1440);
        List<HashMap<String, Object>> list = getBaseMapper().getPurConfirmList(page, params);
        page.setRecords(list);
        return new PageUtils(page);
    }
    /**
     * 采购方订单下达及确认 气泡查询
     *  -- dlw
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> confirmListCountMap(Map<String, Object> params) {
        Map<String,Object> resultMap = null;
        try {
            resultMap = new HashMap<>();
            if(!StrUtil.isEmptyIfStr(params.get("whereType"))){
                if("8".equals(params.get("whereType"))){
                    params.put("link","1");
                }
            }
            for (int i = 1; i <= 6; i++) {
                if(i==6){
                    params.put("whereType",8);
                }else{
                    params.put("whereType", i);
                }
                if(i==5){
                    params.remove("link");
                    params.remove("isUser");
                }
                resultMap.put("count"+i,getBaseMapper().confirmCountMap(params));
            }

        } catch (Exception e) {
            return null;
        }
        return resultMap;
    }

    @Override
    public List<PurConfirmExportVO> confirmExportList(Map<String, Object> params) {
        List<HashMap<String, Object>> list;


        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {
        }else{
            params.put("page", 1);
            params.put("limit", Long.MAX_VALUE);
        }

        list= this.queryConfirmList(params).getList();

//        logger.info("执行了confirmExportList");
//        logger.info("\n"+list.get(0).toString()+"\n");

        List<PurConfirmExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, PurConfirmExportVO.class);
        }
        return resultList;
    }

    @Override
    public List<PurConfirmExportaVO> ordinaryExportList(Map<String, Object> params) {
        List<HashMap<String, Object>> list;


        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {


        }else{
            params.put("page", 1);
            params.put("limit", Long.MAX_VALUE);
        }

        list= this.confirmMainList(params).getList();

//        logger.info("执行了confirmExportList");
//        logger.info("\n"+list.get(0).toString()+"\n");

        List<PurConfirmExportaVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, PurConfirmExportaVO.class);
        }
        return resultList;
    }

    /**
     * 采购方订单执行进度 气泡查询
     *  -- dlw
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> progressListCountMap(Map<String, Object> params) {
        Map<String,Object> resultMap = null;
        try {
            resultMap = new HashMap<>();
            params.put("userId", commonService.getUserId());
            // 我的
            params.put("whereType",8);
            resultMap.put("count"+8,getBaseMapper().progressCountMap(params));
            if(StrUtil.isEmptyIfStr(params.get("isUser"))){
                params.remove("userId");
            }
            for (int i = 1; i < 5; i++) {
                params.put("whereType",i);
                resultMap.put("count"+i,getBaseMapper().progressCountMap(params));
            }
            // 全部
            params.remove("whereType");
            params.remove("isUser");
            resultMap.put("count"+5,getBaseMapper().progressCountMap(params));
        } catch (Exception e) {
            return null;
        }
        return resultMap;
    }

    /**
     * 执行进度导出
     * @param params
     * @return
     */
    @Override
    public List<PurProgressExportVO> progressExportList(Map<String, Object> params) {
        List<HashMap<String, Object>> list;
        params.put("page", 1);
        params.put("limit", Long.MAX_VALUE);
        list= purItemService.queryOrderDetails(params).getList();

        List<PurProgressExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, PurProgressExportVO.class);
        }
        return resultList;
    }

    @Override
    public void updateIsBusy(Map<String, Object> params) {
        String[] orderIds = String.valueOf(params.get("orderIds")).split(",");
        int isBusy = Integer.parseInt(String.valueOf(params.get("isBusy")));

        if(orderIds != null && orderIds.length != 0){
            for (String id : orderIds) {
                PurItemEntity purItemEntity = purItemService.getById(id);
                //身份校验
                if(purItemEntity.getTenantId().equals(commonService.getTenantId())){
                    purItemEntity.setIsBusy(isBusy);

                    purItemService.updateById(purItemEntity);
                }
            }

        }

    }

    /**
     * 接口模块采购订单保存
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean purchaseSave(Map<String, Object> params) {
            PurEntity purEntity = new PurEntity();
            purEntity.setTenantId(Long.valueOf(params.get("tenantId")+""));
            purEntity.setTenantName(params.get("tenantName")+"");
            purEntity.setPurNo(params.get("purNo")+"");
            purEntity.setVendorId(Long.valueOf(params.get("vendorId")+""));
            purEntity.setVendorCode(params.get("vendorCode")+"");
            purEntity.setVendorName(params.get("vendorName")+"");
            // 采购员
            if (params.get("purId") != null) {
                purEntity.setPurId(Long.valueOf(params.get("purId")+"")); // 采购员ID
                purEntity.setPurName(params.get("purName")+""); // 采购员名称
            }
            // 付款方式
            if (!StrUtil.isBlankIfStr(params.get("payName"))){
                purEntity.setPayName(params.get("payName").toString());
            } else {
                purEntity.setPayName("");
            }
            purEntity.setCurrencyCode(params.get("currencyCode")+"");
            purEntity.setBukrs(params.get("bukrs")+"");
            // 订单类型
            purEntity.setOrderType(Integer.valueOf(params.get("orderType")+""));
            purEntity.setOrderFlag(2); // 1-Excel导出;2-接口导入
            purEntity.setReplyStat(PurReplyStatEnum.q.getValue());
            purEntity.setBsart(params.get("bsart")+"");
            purEntity.setCurrencyName(params.get("currencyName")+"");
            // 订单日期
            purEntity.setOrderDate(DateUtils.stringToDate(params.get("orderDate").toString(), DateUtils.DATE_TIME_PATTERN));
            purEntity.setCreateDate(new Date());
            purEntity.setDeptName(params.get("deptName")+"");
            // 订单如果是委外订单，则伪删除
            purEntity.setDeleteFlag(0);
            if (OrderTypeEnum.OUTSOURCING.getValue().equals(purEntity.getOrderType())) {
            }
            purEntity.setStat(PurStatEnum.WAITREPLY.getValue()); // 已发布
            purEntity.setExRate(new BigDecimal(params.get("exRate")+""));
            // 保存主表信息
            this.save(purEntity);
            BigDecimal totalAmount=BigDecimal.ZERO;//含税总金额
            JSONArray jsonArray = JSONObject.parseObject(params+"").getJSONArray("itemList");
            List<PurItemEntity> purItemEntityList = new ArrayList<>();
            List<PurPlanItemEntity> purPlanItemEntityList = new ArrayList<>();
            for (Object arr : jsonArray){
                JSONObject jsonObject = JSONObject.parseObject(arr + "");
                PurItemEntity purItemEntity = new PurItemEntity();
                purItemEntity.setTenantId(purEntity.getTenantId());
                purItemEntity.setPurId(purEntity.getId());
                purItemEntity.setSeq(jsonObject.get("seq")+"");
                // 物料信息
                purItemEntity.setGoodsId(Long.valueOf(jsonObject.get("goodsId")+""));
                purItemEntity.setGoodsName(jsonObject.get("goodsName")+"");
                purItemEntity.setGoodsModel(jsonObject.get("goodsModel")+"");
                purItemEntity.setGoodsErpCode(jsonObject.get("goodsErpCode")+"");
                purItemEntity.setClassCode(jsonObject.get("classCode")+"");
                purItemEntity.setGoodsClassName(jsonObject.get("className")+"");
                purItemEntity.setOrderNum(new BigDecimal(jsonObject.get("orderNum")+""));  // 采购订单数量
                // 单位
                purItemEntity.setUomId(Long.valueOf(jsonObject.get("uomId")+""));
                purItemEntity.setUomCode(jsonObject.get("uomCode")+"");
                purItemEntity.setUomName(jsonObject.get("uomName")+"");
                // 税码
                purItemEntity.setRateId(Long.valueOf(jsonObject.get("rateId")+""));
                purItemEntity.setRateName(jsonObject.get("rateName")+"");  // 税码
                purItemEntity.setRateVal(new BigDecimal(jsonObject.get("rateVal") +"")); // 税率
                purItemEntity.setGstPrice(new BigDecimal(jsonObject.get("gstPrice") +"")); // 含税单价
                purItemEntity.setTaxPrice(new BigDecimal(jsonObject.get("taxPrice")+"")); // 采购凭证中的净价
                BigDecimal gstAmount = new BigDecimal(jsonObject.get("orderNum")+"").multiply(new BigDecimal(jsonObject.get("gstPrice")+""))
                        .divide(new BigDecimal(jsonObject.get("priceUom")+""), 2,BigDecimal.ROUND_HALF_UP);
                BigDecimal taxAmount = new BigDecimal(jsonObject.get("orderNum")+"").multiply(new BigDecimal(jsonObject.get("taxPrice")+""))
                        .divide(new BigDecimal(jsonObject.get("priceUom")+""), 2,BigDecimal.ROUND_HALF_UP);
                purItemEntity.setGstAmount(gstAmount); // 含税金额 (保留俩位小数)
                purItemEntity.setTaxAmount(taxAmount); // 不含税金额 (保留俩位小数)
                purItemEntity.setItemStat(1); //待答交
                // 币种
                purItemEntity.setCurrencyId(Long.valueOf(jsonObject.get("currencyId")+""));
                purItemEntity.setCurrencyName(jsonObject.get("currencyName")+"");
                purItemEntity.setWaitNum(new BigDecimal(jsonObject.get("orderNum")+"")); // 待送货数量
                purItemEntity.setDeliveryDate(DateUtils.stringToDate(jsonObject.get("deliveryDate").toString(), DateUtils.DATE_TIME_PATTERN)); // 交货日期
                // 没有仓库则不保存
                if (null != jsonObject.get("warehouseId")) {
                    purItemEntity.setWarehouseId(Long.valueOf(jsonObject.get("warehouseId") + "")); // 仓库id
                    purItemEntity.setWarehouseCode(jsonObject.get("warehouseCode") + "");
                    purItemEntity.setWarehouseName(jsonObject.get("warehouseName") + ""); // 库存地点
                }
                // 采购凭证编号   订单价格单位   采购订单计量单位
                purItemEntity.setIsReturnPo((int)jsonObject.get("isReturnPo")); // 退回标识
                purItemEntity.setOrderPriceUom(jsonObject.get("orderPriceUom") +""); // 单价格单位
                purItemEntity.setPriceUom(new BigDecimal(jsonObject.get("priceUom") +"")); // 价格单位
                purItemEntity.setWerks(jsonObject.get("werks")+""); // 工厂
                if (jsonObject.get("plans") != null) {
                    purItemEntity.setPlans(jsonObject.get("plans") + ""); // 计划交货天数
                }
                purItemEntity.setDeleteFlag(0); // 删除标识
                // SAP(L) - 删除标识    提醒一下，当时这个地方写的X，需要纠正一下，SAP传输的应该是L值
                if (null != jsonObject.get("deleteFlag")) {
                    purItemEntity.setDeleteFlag(Integer.parseInt(jsonObject.get("deleteFlag").toString()));
                }
                totalAmount=totalAmount.add(purItemEntity.getGstAmount());//含税金额累加
                purItemService.save(purItemEntity);
                // 保存交易料品
                jsonObject.put("tenantId", purEntity.getTenantId());
                jsonObject.put("vendorId", purEntity.getVendorId());
                jsonObject.put("vendorCode", purEntity.getVendorCode());
                jsonObject.put("vendorName", purEntity.getVendorName());
                JSONArray jsonPlanArray =JSONObject.parseObject(jsonObject+"").getJSONArray("planItemList");
                // 如果采购计划明细行不为空，则导入
                if (jsonPlanArray != null) {
                    for (Object arrPlan : jsonPlanArray) {
                        JSONObject jsonPlanObject = JSONObject.parseObject(arrPlan + "");
                        PurPlanItemEntity purPlanItemEntity = new PurPlanItemEntity();
                        purPlanItemEntity.setTenantPId(purEntity.getTenantPId());
                        purPlanItemEntity.setTenantId(purEntity.getTenantId());
                        purPlanItemEntity.setPurId(purEntity.getId());  // 主表id
                        purPlanItemEntity.setPurItemId(purItemEntity.getId());  // 从表id
                        purPlanItemEntity.setGoodsErpCode(purItemEntity.getGoodsErpCode()); // 从表物料编号
                        purPlanItemEntity.setGoodsName(purItemEntity.getGoodsName());   // 从表物料名
                        purPlanItemEntity.setSeq(Long.valueOf(jsonPlanObject.get("planSeq") + ""));  // 计划行计数器不能为空
                        purPlanItemEntity.setDeliveryDate(DateUtils.stringToDate(jsonPlanObject.get("deliveryDate").toString(), DateUtils.DATE_TIME_PATTERN));

                        purPlanItemEntity.setPlanNum(new BigDecimal(jsonPlanObject.get("planNum") + "")); // 计划数量
                        if (jsonPlanObject.get("deliverNum") != null) {
                            purPlanItemEntity.setDeliverNum(new BigDecimal(jsonPlanObject.get("deliverNum") + ""));  // 发货数量
                        }
                        if (jsonPlanObject.get("receiveNum") != null) {
                            purPlanItemEntity.setReceiveNum(new BigDecimal(jsonPlanObject.get("receiveNum") + "")); // 已收货数量
                        }
                        // 次表数据保存
                        purPlanItemEntityList.add(purPlanItemEntity);
                    }
                }
            }
            purPlanItemService.saveBatch(purPlanItemEntityList);
            purEntity.setTotalAmount(totalAmount);//订单含税总金额
            purEntity.setPublishDate(new Date());//订单发布时间,判断订单是否超时未答交
            updateById(purEntity);

            //向供应商发送站内信消息和邮箱消息
        return true;
    }

    public JSONObject creatMasterJson(Map<String, Object> params){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", params.get("tenantId"));
        jsonObject.put("masterNo", params.get("purNo")+""); // 退货凭证号
        Date date = new Date();
        jsonObject.put("documentYear", new SimpleDateFormat("yyyy").format(date)); // 年份
        jsonObject.put("documentDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date)); // 凭证日期
        jsonObject.put("postDate",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date)); // 过账日期
        jsonObject.put("vendorCode", params.get("vendorCode"));
        jsonObject.put("vendorId", params.get("vendorId"));
        jsonObject.put("vendorName", params.get("vendorName"));
        jsonObject.put("masterType", "2"); // 退货单
        jsonObject.put("isReturnPo","1");//是否退货PO
        List<JSONObject> jsonObjectList = new ArrayList<>();
        JSONArray jsonArray =JSONObject.parseObject(params+"").getJSONArray("itemList");
        for (Object arr : jsonArray){
            JSONObject purItem = JSONObject.parseObject(arr + "");
            JSONObject purItemJson = new JSONObject();
            purItemJson.put("tenantId", params.get("tenantId"));
            purItemJson.put("seq", purItem.get("seq")+""); // 收货凭证行号
            purItemJson.put("goodsErpCode", purItem.get("goodsErpCode")+"");
            purItemJson.put("goodsName", purItem.get("goodsName")+""); // 物料名称
            purItemJson.put("goodsCode", purItem.get("goodsErpCode")+"");
            purItemJson.put("goodsModel", purItem.get("goodsModel")); // 物料描述
            purItemJson.put("werks", purItem.get("werks")+""); // 工厂
            purItemJson.put("loan", "H"); // 借贷标志
            purItemJson.put("masterNum", purItem.get("orderNum")+""); // 送货数量
            purItemJson.put("uomCode", purItem.get("uomCode")); // 送货数量单位
            purItemJson.put("saleNo", params.get("purNo")+""); // 采购订单号
            purItemJson.put("saleSeq", purItem.get("seq")+""); // 采购订单行号
            purItemJson.put("taxPrice", purItem.get("taxPrice")+""); // 采购凭证中的净价
            purItemJson.put("gstPrice", purItem.get("gstPrice")); // 含税单价
            purItemJson.put("priceUom", purItem.get("priceUom")); // 价格单位
            purItemJson.put("orderPriceUom", purItem.get("orderPriceUom") +""); // 订单价格单位
            BigDecimal gstAmount = new BigDecimal(purItem.get("orderNum")+"").multiply(new BigDecimal(purItem.get("gstPrice")+""))
                    .divide(new BigDecimal(purItem.get("priceUom")+""), 6,BigDecimal.ROUND_HALF_UP);
            BigDecimal taxAmount = new BigDecimal(purItem.get("orderNum")+"").multiply(new BigDecimal(purItem.get("taxPrice")+""))
                    .divide(new BigDecimal(purItem.get("priceUom")+""), 6,BigDecimal.ROUND_HALF_UP);
            purItemJson.put("taxAmount", taxAmount);//不含税金额
            purItemJson.put("gstAmount", gstAmount);//含税金额
            purItemJson.put("currencyCode", purItem.get("currencyName")+""); // 币种
            purItemJson.put("rateName", purItem.get("rateName")); // 税码
            purItemJson.put("warehouseCode", purItem.get("warehouseCode"));
            purItemJson.put("warehouseName", purItem.get("warehouseName")); //库存地点
            purItemJson.put("warehouseId",purItem.get("warehouseId"));
            purItemJson.put("goodsId", purItem.get("goodsId")+"");
            purItemJson.put("uomId", purItem.get("uomId")+"");//设置单位数据
            purItemJson.put("uomName", purItem.get("uomName")+"");
            purItemJson.put("rateVal", purItem.get("rateVal")+"");
            purItemJson.put("rateId", purItem.get("rateId")+"");
            purItemJson.put("currencyId", purItem.get("currencyId")+""); // 主数据Id
            purItemJson.put("currencyName", purItem.get("currencyName")+""); // 主数据Id
            jsonObjectList.add(purItemJson);
        }
        jsonObject.put("deliveryMasterItemParam", jsonObjectList);
        return jsonObject;
    }

    public void sendCreateEmail(PurEntity purEntity) {
        // 采购方名称
        JSONObject tenantJson = baseClient.getCompanyInMail(purEntity.getTenantId());
        String tenantNames = "";
        if (!tenantJson.isEmpty()){
            tenantNames = tenantJson.get("company").toString();
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId",commonService.getTenantId());//发送方租户Id
        if (!StrUtil.isEmptyIfStr(purEntity.getOrderReply())) {
            if("1".equals(purEntity.getOrderReply())) {
                jsonObject.put("title", "采购订单-发布-" + tenantNames);//发送方租户名称
                jsonObject.put("orderStat","已发布新");//单据类型
                jsonObject.put("orderId",purEntity.getId());//单据id
                jsonObject.put("url","/pages/srm/order/vendor/saleDetail");//单据id
            }else if("2".equals(purEntity.getOrderReply())) {
                jsonObject.put("title", "采购订单-变更-" + tenantNames);//发送方租户名称
                jsonObject.put("orderStat","已变更");//单据类型
                jsonObject.put("orderId",purEntity.getId());//单据id
                jsonObject.put("url","/pages/srm/order/vendor/saleDetail");//单据id
            }else if("3".equals(purEntity.getOrderReply())) {
                jsonObject.put("title", "采购订单-退回-" + tenantNames);//发送方租户名称
                jsonObject.put("orderStat","已退回");//单据类型
                jsonObject.put("url","/pages/srm/order/vendor/saleDetail");//单据id
            }
        }else {
            jsonObject.put("title", "采购订单-发布-" + tenantNames);//发送方租户名称
            jsonObject.put("orderStat","已发布");//单据类型
            jsonObject.put("url","/pages/srm/order/vendor/saleDetail");//单据id
        }
        jsonObject.put("sender",tenantNames);//发件企业
        jsonObject.put("orderType","采购订单");//单据类型
        jsonObject.put("orderNo",purEntity.getPurNo());//单据号
        jsonObject.put("orderId",purEntity.getId());//单据号
        jsonObject.put("receiverEntId",purEntity.getVendorId());//收件企业租户ID
        jsonObject.put("roleCode","21005,tenantAdmin");//供应商业务员和管理员
        jsonObject.put("setingCode","newOrder");//通知编码(业务节点标识)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        jsonObject.put("publishDate", sdf.format(new Date()));
        jsonObject.put("appUserId",commonService.getUserId());
        // 获取供应商信息-邮箱
        JSONObject vendorJson = baseClient.getVendorCodeByTenantIdAndVendorCode(purEntity.getTenantId(), purEntity.getVendorCode());
        String vendorEmail = vendorJson.getString("vendorEmail");
        jsonObject.put("receiver",vendorJson.getString("vendorFullName"));//收件企业
        if(StringUtils.isEmpty(vendorEmail)){

        }else{
            String soureId = vendorJson.getString("soureId");
            jsonObject.put("vendorId",soureId);
            jsonObject.put("receiveEmail",vendorEmail);
            baseClient.sendCreateEmail(jsonObject);
        }

    }

    /**
     * 2000模块erp采购数据保存
     * @param params
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public String purchaseErpSave(Map<String, Object> params) {
        PurEntity purEntity = new PurEntity();
        purEntity.setTenantId(Long.valueOf(params.get("tenantId")+""));
        purEntity.setTenantName(params.get("tenantName")+"");
        purEntity.setPurNo(params.get("purNo")+"");
        purEntity.setVendorId(Long.valueOf(params.get("vendorId")+""));
        purEntity.setVendorCode(params.get("vendorCode")+"");
        purEntity.setVendorName(params.get("vendorName")+"");
        /**
         * 采购员
         */
        if (params.get("purId") != null) {
            purEntity.setPurId(Long.valueOf(params.get("purId")+"")); // 采购员ID
            purEntity.setPurName(params.get("purName")+""); // 采购员名称
        }
        purEntity.setOrderType(OrderTypeEnum.ERP.getValue());
        purEntity.setOrderFlag(2); // 接口导入
        purEntity.setReplyStat(PurReplyStatEnum.q.getValue());
        try {
            SimpleDateFormat dateFormat =new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            purEntity.setOrderDate(dateFormat.parse(params.get("orderDate")+"")); // 订单日期
        } catch (Exception e) {
            e.printStackTrace();
            throw new RRException("订单日期出错 - ："+e.getMessage());
        }
        purEntity.setCreateDate(new Date());
        purEntity.setDeleteFlag(0);
        purEntity.setStat(PurStatEnum.WAITREPLY.getValue()); // 已发布
        // 保存主表信息
        this.save(purEntity);
        BigDecimal totalAmount=BigDecimal.ZERO;//含税总金额
        JSONArray jsonArray =JSONObject.parseObject(params+"").getJSONArray("itemList");
        for (Object arr : jsonArray) {
            JSONObject jsonObject = JSONObject.parseObject(arr + "");
            PurItemEntity purItemEntity = new PurItemEntity();
            purItemEntity.setTenantId(purEntity.getTenantId());
            purItemEntity.setPurId(purEntity.getId());
            purItemEntity.setSeq(jsonObject.get("seq") + "");
            // 物料信息
            purItemEntity.setGoodsName(jsonObject.get("goodsName") + "");
            purItemEntity.setGoodsModel(jsonObject.get("goodsModel") + "");
            purItemEntity.setGoodsErpCode(jsonObject.get("goodsErpCode") + "");
            purItemEntity.setOrderNum(new BigDecimal(jsonObject.get("orderNum") + ""));  // 采购订单数量
            // 单位
            purItemEntity.setUomId(Long.valueOf(jsonObject.get("uomId") + ""));
            purItemEntity.setUomCode(jsonObject.get("uomCode") + "");
            purItemEntity.setUomName(jsonObject.get("uomName") + "");
            // 税码
            purItemEntity.setRateName(jsonObject.get("rateName") + "");  // 税码
            purItemEntity.setRateVal(new BigDecimal(jsonObject.get("rateVal") + "")); // 税率
            purItemEntity.setGstPrice(new BigDecimal(jsonObject.get("gstPrice") + "")); // 含税单价
            purItemEntity.setTaxPrice(new BigDecimal(jsonObject.get("taxPrice") + "")); // 采购凭证中的净价
            purItemEntity.setGstAmount(new BigDecimal(jsonObject.get("gstAmount") + "")); // 含税金额
            purItemEntity.setTaxAmount(new BigDecimal(jsonObject.get("taxAmount") + "")); // 不含税金额
            purItemEntity.setItemStat(1); //待答交

            // 币种
            purItemEntity.setCurrencyId(Long.valueOf(jsonObject.get("currencyId") + ""));
            purItemEntity.setCurrencyName(jsonObject.get("currencyName") + "");

            purItemEntity.setWaitNum(new BigDecimal(jsonObject.get("orderNum") + "")); // 待送货数量

            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                purItemEntity.setDeliveryDate(dateFormat.parse(jsonObject.get("deliveryDate") + "")); // 交货日期
            } catch (ParseException e) {
                e.printStackTrace();
                throw new RRException("交货日期出错 - ：" + e.getMessage());
            }
            if (jsonObject.get("plans") != null) {
                purItemEntity.setPlans(jsonObject.get("plans") + ""); // 计划交货天数
            }

            purItemEntity.setDeleteFlag(0); // 删除标识
            totalAmount = totalAmount.add(purItemEntity.getGstAmount());//含税金额累加

            purItemService.save(purItemEntity);

        }
        purEntity.setTotalAmount(totalAmount);//订单含税总金额
        purEntity.setPublishDate(new Date());//订单发布时间,判断订单是否超时未答交
        updateById(purEntity);
        //向供应商发送站内信消息和邮箱消息
        return "ok";
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Map<String, Object> importCwOrder(List<PurCwImportVO> list, int operType) {
        //校验
        Map<String,Object> msgMap= new HashMap<>();
        Map<String, PurEntity> orderMap = new HashMap<>();
        Long tenantId = commonService.getTenantId();
        for (PurCwImportVO purVO : list) {
            int index = list.indexOf(purVO)+3;
            //设置采购订单号
            PurItemEntity scmOrderPurItemEntity_hi = JSONObject.parseObject(JSONObject.toJSONString(purVO), PurItemEntity.class);
            scmOrderPurItemEntity_hi.setTenantId(tenantId);//组织ID
            scmOrderPurItemEntity_hi.setTenantPId(new Long(0));//关联组织ID
            scmOrderPurItemEntity_hi.setBarcodeType(0);//条码类型 无设置
            scmOrderPurItemEntity_hi.setItemStat(-1);//待发布
            scmOrderPurItemEntity_hi.setDeliveryStat(1);//未送货
            scmOrderPurItemEntity_hi.setFixNum(BigDecimal.ZERO);//已送货量为0
            scmOrderPurItemEntity_hi.setWaitNum(purVO.getOrderNum());//待送货量默认为订单数量
            scmOrderPurItemEntity_hi.setDeleteFlag(0);//未删除
            scmOrderPurItemEntity_hi.setInvoiceType(1);//发票类型;1-增值税专用发票;2-普通发票;3-专业发票
            scmOrderPurItemEntity_hi.setMakeNum(BigDecimal.ZERO);//已制送货单数量为0
            scmOrderPurItemEntity_hi.setSelfNum(BigDecimal.ZERO);//入库退货数量为0
            scmOrderPurItemEntity_hi.setDeliveryType(1);////送货方式;1-按订单交期送货;2-按送货计划送货
            scmOrderPurItemEntity_hi.setPurchaseConfirm(0);//采购方是否确认;0-无;1-待确认;2-已确认
            scmOrderPurItemEntity_hi.setChangeCount(0);//变更次数
            if (orderMap != null && orderMap.get(purVO.getPurNo()) != null) {
                PurEntity scmOrderPurEntity_HI =orderMap.get(purVO.getPurNo());
                List<PurItemEntity> scmOrderPurItemEntity_hiList =scmOrderPurEntity_HI.getPurItemEntityList();
                scmOrderPurItemEntity_hiList.add(scmOrderPurItemEntity_hi);
                scmOrderPurEntity_HI.setPurItemEntityList(scmOrderPurItemEntity_hiList);
                orderMap.put(purVO.getPurNo(),scmOrderPurEntity_HI);
            }else{
                PurEntity scmOrderPurEntity_HI = JSONObject.parseObject(JSONObject.toJSONString(purVO), PurEntity.class);
                scmOrderPurEntity_HI.setTenantId(tenantId);//组织ID
                scmOrderPurEntity_HI.setTenantPId(new Long(0));//关联组织ID
                scmOrderPurEntity_HI.setTenantName(commonService.getUser().getEnt().getEntName());
                scmOrderPurEntity_HI.setStat(PurStatEnum.MAKING.getValue());//单据状态为制单
                scmOrderPurEntity_HI.setReplyStat(1);//单据状态为为答交状态
                scmOrderPurEntity_HI.setDeleteFlag(0);//未删除
                List<PurItemEntity> scmOrderPurItemEntity_hiList = new ArrayList<>();
                scmOrderPurItemEntity_hiList.add(scmOrderPurItemEntity_hi);
                scmOrderPurEntity_HI.setPurItemEntityList(scmOrderPurItemEntity_hiList);
                orderMap.put(purVO.getPurNo(),scmOrderPurEntity_HI);
            }
        }
        for (String purNo: orderMap.keySet()) {
            //传数据,根据订单号判断是否已存在
            HashMap<String,Object> purEntity = this.getPurEntity(purNo,tenantId);
            PurEntity scmOrderPurEntity_hi = orderMap.get(purNo);
            if(purEntity!=null){
                scmOrderPurEntity_hi.setId(Long.valueOf(purEntity.get("id")+""));
            }else {
                scmOrderPurEntity_hi.setChangeCount(0);
                this.save(scmOrderPurEntity_hi);
            }
            List<PurItemEntity> scmOrderPurItemEntity_hiList = scmOrderPurEntity_hi.getPurItemEntityList();
            for (PurItemEntity scmOrderPurItemEntity_hi:
                    scmOrderPurItemEntity_hiList) {
                scmOrderPurItemEntity_hi.setPurId(scmOrderPurEntity_hi.getId());//采购订单ID
                JSONObject jsonObject = new JSONObject();

                BeanUtil.copyProperties(scmOrderPurItemEntity_hi,jsonObject, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                jsonObject.put("vendorId",scmOrderPurEntity_hi.getVendorId());
                jsonObject.put("vendorCode",scmOrderPurEntity_hi.getVendorCode());
                jsonObject.put("vendorName",scmOrderPurEntity_hi.getVendorName());

                JSONObject jgResponseData = baseClient.saveVendorGoods(jsonObject);
                if(Long.valueOf(jgResponseData.get("code")+"").equals(500L)){
                    throw new RRException(jgResponseData.get("msg")+"");
                }
                //设置急单标志
                int days = (int) ((scmOrderPurItemEntity_hi.getDeliveryDate().getTime() - scmOrderPurEntity_hi.getOrderDate().getTime()) / (1000 * 3600 * 24));
                if(days>=0 && days<=3) {
                    scmOrderPurItemEntity_hi.setSign(PurServiceImpl.SIGN_BUSY);
                }
                if(scmOrderPurItemEntity_hi.getSoureNo()==null || "null".equals(scmOrderPurItemEntity_hi.getSoureNo())){
                    scmOrderPurItemEntity_hi.setSoureNo("");
                }
                if(scmOrderPurItemEntity_hi.getGoodsClassName()==null || "null".equals(scmOrderPurItemEntity_hi.getGoodsClassName())){
                    scmOrderPurItemEntity_hi.setGoodsClassName("");
                }
                purItemService.save(scmOrderPurItemEntity_hi);
            }
            if(operType==PurServiceImpl.TYPE_INTERFACE){
                scmOrderPurEntity_hi.setStat(PurStatEnum.WAITREPLY.getValue());//接口下载的订单不需要审核，自动发布变为待答交状态
                this.updateById(scmOrderPurEntity_hi);
                for (PurItemEntity scmOrderPurItemEntityHi:
                        scmOrderPurItemEntity_hiList) {
                    scmOrderPurItemEntityHi.setItemStat(1);//待答交
                    if(scmOrderPurItemEntityHi.getSign()==null){
                        scmOrderPurItemEntityHi.setSign("");
                    }
                    scmOrderPurItemEntityHi.setSign(scmOrderPurItemEntityHi.getSign()+PurServiceImpl.SIGN_NOVISIT);
                    purItemService.updateById(scmOrderPurItemEntityHi);
                }
                String remark="发布采购订单"+scmOrderPurEntity_hi.getPurNo();
                operType=PurServiceImpl.TYPE_PUBLISH;
                //生成销售订单操作日志
                JSONObject jsonObject33 = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CREATE.getValue(), scmOrderPurEntity_hi.getId(), "导入销售订单" + scmOrderPurEntity_hi.getPurNo(), scmOrderPurEntity_hi.getPurNo());
                int i= Integer.valueOf(jsonObject33.get("code")+"");
                if(i==500){
                    throw new RRException(String.format("[%s] "+jsonObject33.get("msg"),scmOrderPurEntity_hi.getPurNo()));
                }
            }
        }
        return msgMap;
    }

    /**
     * sap 单号查询
     * @param params
     * @return
     */
    @Override
    public String purchaseNo(Map<String, Object> params) {
        UserDto userDto = new UserDto();
        userDto.setTenantId(Long.valueOf(params.get("tenantId")+""));
        UserLocal.set(userDto);
        List<PurEntity> list = this.list(new QueryWrapper<PurEntity>()
                .eq("tenant_id", UserLocal.getTenantId()).eq("pur_no", params.get("purNo") + ""));
        if (list.size() != 0) {
            return list.get(0).getPurNo();
        }
        return "";
    }

    /**
     * sap 单号及明细查询
     * @param params
     * @return
     */
    @Override
    public PurItemEntity purchaseNoItem(Map<String, Object> params) {
        UserDto userDto = new UserDto();
        userDto.setTenantId(Long.valueOf(params.get("tenantId")+""));
        UserLocal.set(userDto);
        PurEntity one = this.getOne(new QueryWrapper<PurEntity>().eq("tenant_id", UserLocal.getTenantId())
                .eq("pur_no", params.get("purNo") + ""));
        List<PurItemEntity> list = purItemService.list(new QueryWrapper<PurItemEntity>()
                .eq("tenant_id", UserLocal.getTenantId())
                .eq("pur_id", one.getId())
                .eq("seq", params.get("seq")));
        if (list.size() != 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public PageUtils confirmMainList(Map<String, Object> params) {
        UserDto userInfo = commonService.getUser();
        List<String> lookPricePermsList = userInfo.getPermsList().stream().filter(perms -> perms.equals("order:pur:lookPrice")).collect(Collectors.toList());
        Integer ifLookPricePerms = WhetherEnum.NO.getCode();
        if (CollectionUtil.isNotEmpty(lookPricePermsList)){
            ifLookPricePerms = WhetherEnum.YES.getCode();
        }
        try {
            if (params.get("orderDate") != null && !params.get("orderDate").equals("")) {
                String[] split = params.get("orderDate").toString().split(" 至 ");
                params.put("sorderDate", split[0] + " 00:00:00");
                params.put("eorderDate", split[1] + " 23:59:59");
            }
        } catch (Exception e) {
            logger.error("订单搜索订单日期有误");
            throw new RRException("订单日期输入有误");
        }
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        params.put("tenantId",commonService.getTenantId());
        params.put("userId",commonService.getUserId());

        //答交超时时间 临时使用
//        params.put("replyMinute",24);
//        logger.info(params+"--------params--------------");
        params.put("replyMinute",1440);
        List<HashMap<String, Object>> list = getBaseMapper().getPurConfirmMainList(page, params);
        BigDecimal goodsSum = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(list)){
            PurEntity purEntity = null;
            for (HashMap<String, Object> item:list) {
                purEntity = getById(Long.valueOf(item.get("id").toString()));
                Map<String, PurchaseOrderVo> erpOrderMap = new HashMap<>();
                // 查询ERP采购订单数据
                if (JinDieOrderTypeEnum.OUTSOURCING.getValue().equals(purEntity.getOrderType()) && ObjectUtil.isNotEmpty(purEntity.getSourceId())){
                    ErpPurOrderReq purOrderReq = new ErpPurOrderReq();
                    purOrderReq.setFids(purEntity.getSourceId().toString());
                    List<PurchaseOrderVo> erpOrderList = purchaseOrderJoggleService.queryErpPurOrderPickQtyList(purOrderReq);
                    erpOrderMap = erpOrderList.stream()
                            .filter(vo -> StrUtil.isNotEmpty(vo.getFEntryID()))
                            .collect(Collectors.toMap(
                                    PurchaseOrderVo::getFEntryID,
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ));
                }
                item.put("ifLookPrice",ifLookPricePerms);
                goodsSum = goodsSum.add(ObjectUtil.isNotEmpty(item.get("gstAmount")) ? new BigDecimal(item.get("gstAmount").toString()) : BigDecimal.ZERO );
                // 订单的未制单数量 = 订单数量 - 已制单数量
                item.put("unMakeNum",new BigDecimal(item.get("orderNum").toString()).subtract(new BigDecimal(item.get("makeNum").toString())));
                item.put("deliveryStatus","未开始送货");
                // 订单行已送数量大于0 且 订单行已送数量小于订单行数量
                if (new BigDecimal(item.get("fixNum").toString()).compareTo(BigDecimal.ZERO) > 0 && new BigDecimal(item.get("fixNum").toString()).compareTo(new BigDecimal(item.get("orderNum").toString())) < 0 ){
                    item.put("deliveryStatus","送货进行中");
                }
                // 订单行已送数量等于订单行数量
                if (new BigDecimal(item.get("fixNum").toString()).compareTo(new BigDecimal(item.get("orderNum").toString())) == 0){
                    item.put("deliveryStatus","已完成送货");
                }
                // 订单行已收数量等于订单行数量
                if (new BigDecimal(item.get("erpMasterNum").toString()).compareTo(new BigDecimal(item.get("orderNum").toString())) == 0){
                    item.put("deliveryStatus","已完成入库");
                }
//                if (CollectionUtil.isNotEmpty(erpOrderMap) && !StrUtil.isEmptyIfStr(item.get("sourceId"))){
//                    PurchaseOrderVo purchaseOrderVo = erpOrderMap.get(item.get("sourceId").toString());
//                    // 订单行委外领料套数
//                    if(purchaseOrderVo == null){
//                        item.put("erpOutPickQty",null);
//                    }else{
//                        item.put("erpOutPickQty",StrUtil.isEmptyIfStr(purchaseOrderVo.getFRemainStockINQty()) ? null : new BigDecimal(purchaseOrderVo.getFRemainStockINQty()));
//                    }
//                }
                // 查询用料清单
                List<MaterialListEntity> materialList = materialListService.queryListByPurOrderLineId(Long.valueOf(item.get("id").toString()));
                item.put("materialList",materialList);
            }
            assert purEntity != null;
            purEntity.setGoodsSum(goodsSum);
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public Map<String, Object> confirmMainCountMap(Map<String, Object> params) {
        Map<String,Object> resultMap = null;
        try {
            resultMap = new HashMap<>();
            //全部
            resultMap.put("all",getBaseMapper().confirmCountMap(params));
            // 我的
            params.put("userId", commonService.getUserId());
            params.put("whereType",8);
            resultMap.put("count8",getBaseMapper().confirmCountMap(params));
            if(StrUtil.isEmptyIfStr(params.get("isUser"))){
                params.remove("userId");
            }
            for (int i = 1; i < 5; i++) {
                params.put("whereType",i);
                // count 1 - 待答交
                // count 3 - 答交异常
                resultMap.put("count"+i,getBaseMapper().confirmMainCountMap(params));
            }
            // 全部
            params.remove("isUser");
            params.remove("whereType");
            resultMap.put("count"+5,getBaseMapper().confirmMainCountMap(params));
        } catch (Exception e) {
            return null;
        }
        return resultMap;
    }

    /**
     * 采购订单详情信息
     * @return
     */
    @Override
    public PageUtils findOrderInfo(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> purList = getBaseMapper().findOrderInfo(page, params);
        page.setRecords(purList);
        return new PageUtils(page);
    }

    /**
     * 分页查询供应商已下发了采购订单的物料数据
     * @param params
     * @return
     */
    @Override
    public PageUtils queryVendorOrderGoodsPage(Map<String, Object> params) {
        params.put("vendorId",commonService.getTenantId());
        IPage<VendorOrderGoodsVO> page = new Query<VendorOrderGoodsVO>().getPage(params);
        List<VendorOrderGoodsVO> purList = getBaseMapper().queryVendorOrderGoodsPage(page, params);
        page.setRecords(purList);
        return new PageUtils(page);
    }

    /**
     * 根据id及状态获取订单明细中的气泡数据
     * @param params
     * @return
     */
    @Override
    public R getOrderInfoCounts(Map<String, Object> params) {
        params.put("stat",1);
        Integer waitQuantity = purItemService.count(getQueryWrapper(params));
        params.put("stat",2);
        Integer exceptionGoodsQuantity = purItemService.count(getQueryWrapper(params));
        params.put("stat",4);
        Integer smallQuantity = purItemService.count(getQueryWrapper(params));
        params.remove("stat");
        params.remove("goodsInfo");
        Integer allQuantity = purItemService.count(getQueryWrapper(params));

        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("code",200);
        resultMap.put("msg","查询成功");
        resultMap.put("allQuantity", allQuantity); // 全部数量
        resultMap.put("waitQuantity", waitQuantity); // 待答交数量
        resultMap.put("exceptionGoodsQuantity", exceptionGoodsQuantity); // 答交异常数量
        resultMap.put("smallQuantity", smallQuantity); // 已确认数量
        return R.ok().put(resultMap);
    }

    @Override
    @GlobalTransactional(rollbackFor = Throwable.class,timeoutMills = 60000*10)
    public boolean newData(PurEntityVO purEntityVO) {
        String uuid= IdUtil.fastUUID();
        logger.info("uuid :"+uuid+"单号"+purEntityVO.getPurNo());
        PurEntity purEntity = BeanConverter.convert(purEntityVO, PurEntity.class);
        List<PurItemEntity> purItemEntities = BeanConverter.convertList(purEntityVO.getPurItemEntityList(), PurItemEntity.class);
        purEntity.setPurItemEntityList(purItemEntities);
        purEntity.setTenantId(commonService.getTenantId());
        purEntity.setPublishDate(new Date());
        SysEntEntity tenantInfo = commonService.getTenantInfo(commonService.getTenantId());
        purEntity.setTenantName(tenantInfo.getEntName());
        purEntity.setStat(PurStatEnum.WAITREPLY.getValue()); // 已发布
        //保存从表
        BigDecimal totalAmount = BigDecimal.ZERO;//含税总金额
        for (PurItemEntity purItemEntity : purItemEntities) {
            purItemEntity.setTenantId(commonService.getTenantId());
            purItemEntity.setItemStat(1);
            purItemEntity.setDeleteFlag(0);
            String isCreateDm = configService.getValueByKeyAndTenantId("IsCreateDm", commonService.getTenantId().toString());
            if ("2".equals(isCreateDm)) {
                purItemEntity.setDeliveryType(Integer.parseInt(isCreateDm));
            }
            totalAmount = totalAmount.add(purItemEntity.getGstAmount());
        }
        purEntity.setTotalAmount(totalAmount);
        purEntity.setReplyStat(1);
        purEntity.setDeleteFlag(0);
        purEntity.setOrderFlag(2);
        //新增
        if(purEntityVO.getOperType()==1){
            PurEntity doesItExist = this.doesItExist(purEntity);
            if(doesItExist!=null){
                if( !StrUtil.isEmptyIfStr(purEntityVO.getAttr6())){
                    return true;
                }else {
                    throw new RRException(String.format("[%s] 当前订单已存在", purEntity.getPurNo()));
                }
            }else {
                this.saveInfo(purEntity);
            }
        } else if (purEntityVO.getOperType() == 2) {
            this.updateByERP(purEntity);
        } else if (purEntityVO.getOperType() == 3) {
            PurEntity entity = this.queryByPurNo(purEntity);
            if(entity!=null) {
                List<PurItemEntity> purItem = purItemService.queryLineList(entity.getId());
                if (CollectionUtils.isNotEmpty(purItem)) {
                    for (PurItemEntity purItemEntity : purItem) {
                        if(!PurlineStatEnum.WAITREPLY.getValue().equals(purItemEntity.getItemStat())){
                            throw new RRException(String.format("[%s] 当前订单已答交,禁止返审核", purEntity.getPurNo()));
                        }
                         List<DeliveryPlanItemVO> list = dmClient.qualityByList(entity.getId(), purItemEntity.getId());
                             if(CollectionUtils.isNotEmpty(list)) {
                            throw new RRException(String.format("[%s] 当前订单已匹配计划单据,禁止返审核", purEntity.getPurNo()));
                        }
                    }
                    purItemService.deleteInfo(entity.getId());
                    this.remove(new QueryWrapper<PurEntity>().eq("id", entity.getId()));
                }else {
                    this.remove(new QueryWrapper<PurEntity>().eq("id", entity.getId()));
                }
            }else {
                throw new RRException(String.format("[%s] 未找到当前订单，请检查数据", purEntity.getPurNo()));
            }
        }
        purEntity.setOrderReply(purEntityVO.getOperType()+"");
        //向供应商发送站内信消息和邮箱消息
        JSONObject mailJson = new JSONObject();
        mailJson.put("tenantId", purEntity.getVendorId());
        mailJson.put("userId", 0L);

        logger.info("uuid :"+uuid+"开始准备发送邮件");
        JSONObject tenantJson = baseClient.getCompanyInMail(purEntity.getTenantId());
        String tenantNames = "";
        if (!tenantJson.isEmpty()) {
            tenantNames = tenantJson.get("company").toString();
        }
        if(purEntityVO.getOperType()==1) {
            mailJson.put("content", tenantNames+"刚发布了新的采购订单" + purEntity.getPurNo());
        }else if( purEntityVO.getOperType()==2){
            mailJson.put("content", tenantNames+"刚刚变更采购订单" + purEntity.getPurNo());
        }else if( purEntityVO.getOperType()==3){
            mailJson.put("content", tenantNames+"刚刚退回采购订单" + purEntity.getPurNo());
        }
        mailJson.put("menuTitle", "销售订单详情");//菜单标题
        mailJson.put("url", "order/sale/saleForm.html?id=" + purEntity.getId());//菜单地址
        logger.info("uuid :"+uuid+"发送邮件结束");
        return true;
    }

    private void updateByPur(PurEntity purEntity) {
        //查询erp订单是否已存在
        PurEntity entity  = this.queryByPurNo(purEntity);
        //for update
       this.getBaseMapper().queryById(entity.getId());
        if (entity==null){
            throw new RRException( String.format("[%s] 该单在系统中不存在,不能进行修改存在", purEntity.getPurNo()));
        }
        purEntity.setId(entity.getId());
        purEntity.setStat(3); // 已确认
        int count = entity.getChangeCount() + 1;
        purEntity.setChangeCount(count);
        //保存从表
        BigDecimal totalAmount=BigDecimal.ZERO;//含税总金额
        for (PurItemEntity purItemEntity : purEntity.getPurItemEntityList()) {
            totalAmount=totalAmount.add(purItemEntity.getGstAmount());
        }
        purEntity.setTotalAmount(totalAmount);
        //更新主表

        for (PurItemEntity purItemEntity : purEntity.getPurItemEntityList()) {
            PurItemEntity  purItem = purItemService.queryBySourceItemId(purItemEntity.getSourceItemId(),purEntity.getId());
            if(purItem!=null) {
                //采购订单修改检验订单的入库数量与接口修改订单数量比较
                if (purItemEntity.getOrderNum().compareTo(purItem.getErpMasterNum()) == -1) {
                    throw new RRException(String.format("[%s] 该单在系统中的已制单数量大于订单数,不能进行修改", purEntity.getPurNo()));
                }
                this.getBaseMapper().queryByLienId(purItem.getId());
                //通过采购订单查询计划所用数
                BigDecimal num = dmClient.qualityByPlan(purEntity.getId(), purItem.getId());
                BigDecimal decimal=new BigDecimal(0);
                //在计划的总数大于等于订单数量
                if(num.compareTo(purItem.getOrderNum())> -1) {
                    //占用数量=计划的总数-待送货量
                    decimal = num.subtract(purItem.getWaitNum());
                }else {
                    //在小于订单数量时  占用数量=计划的总数
                    decimal=num;
                }
                //订单修改数量比较占用数量
                if (purItemEntity.getOrderNum().compareTo(decimal) == -1) {

                 return;
                }
                purItem.setPurId(purEntity.getId());
                purItem.setSeq(purItemEntity.getSeq());
                // 物料信息
                purItem.setGoodsId(purItemEntity.getGoodsId());
                purItem.setGoodsName(purItemEntity.getGoodsName());
                purItem.setGoodsModel(purItemEntity.getGoodsModel());
                purItem.setGoodsErpCode(purItemEntity.getGoodsErpCode());
                purItem.setClassCode(purItemEntity.getClassCode());
                purItem.setGoodsClassName(purItemEntity.getGoodsClassName());
                purItem.setOrderNum(purItemEntity.getOrderNum());  // 采购订单数量
                purItem.setItemStat(purItem.getItemStat());
                //传入的订单数量+原订单已制数=订单数量
                BigDecimal bigDecimal = purItemEntity.getOrderNum().add(purItem.getMakeNum());
                purItemEntity.setOrderNum(bigDecimal);
                BigDecimal subtract = bigDecimal.subtract(purItem.getMakeNum());
                purItem.setWaitNum(subtract);
                // 单位
                purItem.setUomId(purItemEntity.getUomId());
                purItem.setUomCode(purItemEntity.getUomCode());
                purItem.setUomName(purItemEntity.getUomName());
                // 税码
                purItem.setRateName(purItemEntity.getRateName());  // 税码
                purItem.setRateVal(purItemEntity.getRateVal()); // 税率
                purItem.setGstPrice(purItemEntity.getGstPrice()); // 含税单价
                purItem.setTaxPrice(purItemEntity.getTaxPrice()); // 采购凭证中的净价
                purItem.setGstAmount(purItemEntity.getGstAmount()); // 含税金额
                purItem.setTaxAmount(purItemEntity.getTaxAmount()); // 不含税金额
                // 币种
                purItem.setCurrencyId(purItemEntity.getCurrencyId());
                purItem.setCurrencyName(purItemEntity.getCurrencyName());
                // / 没有仓库则不保存
                purItem.setWarehouseId(purItemEntity.getWarehouseId()); // 仓库id
                purItem.setWarehouseCode(purItemEntity.getWarehouseCode());
                purItem.setWarehouseName(purItemEntity.getWarehouseName()); // 库存地点
                purItem.setPlans(purItemEntity.getPlans()); // 计划交货天数
                purItem.setReplyDate(purItem.getReplyDate());
                purItem.setItemStat(1);
                purItemService.updateById(purItem);
            }else {
                purItem=new PurItemEntity();
                purItem.setPurId(purEntity.getId());
                purItem.setSeq(String.valueOf(purEntity.getPurItemEntityList().size()-1));
                purItem.setTenantId(commonService.getTenantId());
                purItem.setSourceItemId(purItemEntity.getSourceItemId());
                // 物料信息
                purItem.setGoodsId(purItemEntity.getGoodsId());
                purItem.setGoodsName(purItemEntity.getGoodsName());
                purItem.setGoodsModel(purItemEntity.getGoodsModel());
                purItem.setGoodsErpCode(purItemEntity.getGoodsErpCode());
                purItem.setGoodsCode(purItemEntity.getGoodsErpCode());
                purItem.setClassCode(purItemEntity.getClassCode());
                purItem.setGoodsClassName(purItemEntity.getGoodsClassName());
                purItem.setOrderNum(purItemEntity.getOrderNum());  // 采购订单数量
                // 单位
                purItem.setUomId(purItemEntity.getUomId());
                purItem.setUomCode(purItemEntity.getUomCode());
                purItem.setDeliveryDate(purItemEntity.getDeliveryDate());
                purItem.setUomName(purItemEntity.getUomName());
                // 税码
                purItem.setRateName(purItemEntity.getRateName());  // 税码
                purItem.setRateVal(purItemEntity.getRateVal()); // 税率
                purItem.setGstPrice(purItemEntity.getGstPrice()); // 含税单价
                purItem.setTaxPrice(purItemEntity.getTaxPrice()); // 采购凭证中的净价
                purItem.setGstAmount(purItemEntity.getGstAmount()); // 含税金额
                purItem.setTaxAmount(purItemEntity.getTaxAmount()); // 不含税金额
                // 币种
                purItem.setCurrencyId(purItemEntity.getCurrencyId());
                purItem.setCurrencyName(purItemEntity.getCurrencyName());
                purItem.setWaitNum(purItemEntity.getOrderNum()); // 待送货数量
                // / 没有仓库则不保存
                purItem.setWarehouseId(purItemEntity.getWarehouseId()); // 仓库id
                purItem.setWarehouseCode(purItemEntity.getWarehouseCode());
                purItem.setWarehouseName(purItemEntity.getWarehouseName()); // 库存地点
                purItem.setPlans(purItemEntity.getPlans()); // 计划交货天数
                purItem.setDeliveryDate(purItemEntity.getDeliveryDate()); // 交货日期
                purItem.setReplyDate(new Date());
                purItem.setAddress(purItemEntity.getAddress());
                purItem.setItemStat(1);
//                if (entity.getStat().equals(PurStatEnum.CONFIRM.getValue())) {
//                    purItem.setItemStat(4);//已确认
//                } else if(entity.getStat().equals(PurStatEnum.REPLYEXCEPTION.getValue())){
//                    purItem.setItemStat(2);//答交异常
//                }else if(entity.getStat().equals(PurStatEnum.REFUSE.getValue())) {
//                    purItem.setItemStat(3);//已拒绝
//                }
                purItem.setDeleteFlag(0);
                purItemService.save(purItem);
            }
        }

        this.updateById(purEntity);
        //获取数据库中的明细数据
        List<PurItemEntity> purItemEntities = purItemService.queryByPurIdList(purEntity.getId());
        List<PurItemEntity> returnList = new ArrayList<>();
        boolean returnFlag;
        for (PurItemEntity purItemEntity : purItemEntities) {
            returnFlag = false;
            //比较传入的采购订单明细
            for (PurItemEntity itemEntity : purEntity.getPurItemEntityList()) {
                if (purItemEntity.getSourceItemId().equals(itemEntity.getSourceItemId())) {
                    returnFlag=true;
                }
            }
            if(returnFlag==false){
                //不存在传入数据中
                returnList.add(purItemEntity);
            }
        }
        if (CollectionUtils.isNotEmpty(returnList)) {
            for (PurItemEntity purItemEntity : returnList) {
                BigDecimal num = dmClient.qualityByPlan(purItemEntity.getPurId(), purItemEntity.getId());
                if (!StrUtil.isBlankIfStr(num)) {
                    throw new RRException("该单在系统中的订单已被计划单据占用,不能进行修改 单号为" + purEntity.getPurNo() + "物料号为" + purItemEntity.getGoodsCode());
                }
                List<DeliveryItemVO> deliveryItemVOS = dmClient.queryBySaleItemId(purEntity.getId(), purItemEntity.getId());
                if (CollectionUtils.isNotEmpty(deliveryItemVOS)) {
                    throw new RRException("该单在系统中的订单已被送货单据占用,不能进行修改 单号为" + purEntity.getPurNo() + "物料号为" + purItemEntity.getGoodsCode());
                }
                purItemService.removeById(purItemEntity);
            }
        }
    }

    /**
     * 根据采购单号和序号将它修改为交货完成状态
     * @param purchaseItemVOList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean updateOrderItemStat(List<PurchaseItemVO> purchaseItemVOList) {
        List<PurItemEntity> purItemEntities = new ArrayList<>();

        if (!purchaseItemVOList.isEmpty()) {
            // 根据单号主表ID
            PurEntity one = this.getOne(new QueryWrapper<PurEntity>()
                    .eq("tenant_id", purchaseItemVOList.get(0).getTenantId())
                    .eq("pur_no", purchaseItemVOList.get(0).getPurNo()));

            for (PurchaseItemVO purchaseItemVO : purchaseItemVOList) {
                // 根据序号查询到该明细
                PurItemEntity purItemEntity = purItemService.getOne(new QueryWrapper<PurItemEntity>()
                        .eq("pur_id", one.getId()).eq("seq", purchaseItemVO.getSeq()));
                if (purItemEntity != null) {
//                    purItemEntity.setDeleteFlag(purchaseItemVO.getDeleteFlag());
                    if (null != purchaseItemVO.getIsClose()) {
                        purItemEntity.setIsClose(purchaseItemVO.getIsClose());
                    }
                    if (null != purchaseItemVO.getDeleteFlag()) {
                        purItemEntity.setDeleteFlag(purchaseItemVO.getDeleteFlag());
                    }
                    purItemEntities.add(purItemEntity);
                } else {
                    throw new RRException(String.format("查询不到序号为 %s 的明细单。", purchaseItemVO.getSeq()));
                }
            }

        } else {
            throw new RRException("订单明细为空，不能进行修改");
        }
        // 批量保存
        return purItemService.updateBatchById(purItemEntities);
    }

    @Override
    public boolean closing(PurchaseOrderClosingVO purchaseOrderClosing) {
        PurEntity  purEntity =new PurEntity();
        purEntity.setPurNo(purchaseOrderClosing.getSourceNo());
        purEntity.setSourceId(purchaseOrderClosing.getSourceId());
        PurEntity doesItExist = this.doesItExist(purEntity);

        if(doesItExist!=null) {
            PurItemEntity purItemEntity = purItemService.queryBySourceItemId(purchaseOrderClosing.getSourceItemId(), doesItExist.getId());
            if (purItemEntity != null) {
                this.getBaseMapper().queryByLienId(purItemEntity.getId());
                //通过采购订单主表id 明细id看是否被占用
                dmClient.qualityByPlanlist(purItemEntity.getPurId(),purItemEntity.getId());
                //通过采购订单主表id 明细id查询是否有未完成的送货单据
                dmClient.queryById(doesItExist.getId(),purItemEntity.getId());
                purItemEntity.setItemStat(PurlineStatEnum.INVALID.getValue());
                purItemEntity.setClosingTime(purchaseOrderClosing.getClosingTime());
                purItemService.updateById(purItemEntity);
            }else {
                throw new RRException("没有查询到当前订单，禁止关闭！");
            }
        }else {
            throw new RRException("没有查询到当前订单，禁止关闭！");
        }
        int num = purItemService.queryLineListItemStat(doesItExist.getId());
        if(num==0){
        }

        return true;
    }

    @Override
    public PurEntityVO queryById(Long id) {
        PurEntity purEntity = this.getById(id);
        if(purEntity!=null) {
            PurEntityVO purchaseVO = new PurEntityVO();
            BeanUtils.copyProperties(purEntity, purchaseVO);
            return purchaseVO;
        }else {
            return  null;
        }
    }

    @Override
    public HashMap<String, Object> queryByNoAndSeqList(String saleNo, String saleSeq) {
        return getBaseMapper().queryByNoAndSeqList(saleNo,saleSeq);
    }

    @Override
    public List<HashMap<String, Object>> queryByOrder(Long orderId, Long orderItemId) {
        return getBaseMapper().queryByOrder(orderId,orderItemId);
    }

    /**
     * erp查询对账单接口 - 通过对账单明细中每一条的采购订单号来查找该订单的订单类型
     * @param purNo
     * @return
     */
    @Override
    public PurEntityVO findOrderByPurNO(String purNo) {
        QueryWrapper<PurEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pur_no",purNo);
        PurEntity purEntity = this.getOne(queryWrapper);
        if(purEntity != null) {
            PurEntityVO purchaseVO = new PurEntityVO();
            BeanUtils.copyProperties(purEntity, purchaseVO);
            return purchaseVO;
        }else {
            return  null;
        }
    }

    @Override
    public PageUtils orderList(Map<String, Object> params) {
        params.put("tenantId",commonService.getTenantId());
        if (params.get("deliveryDate") != null && !params.get("deliveryDate").equals("")) {
            String[] split = params.get("deliveryDate").toString().split(" 至 ");
            params.put("startDate", split[0]);
            params.put("endDate", split[1]);
        }


        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list =getBaseMapper().orderList(page, params);
        for (HashMap<String, Object> objectHashMap : list) {
            Map<String, Object> param =new HashMap<>();
            param.put("tenantId", commonService.getTenantId());
            param.put("orderDate",  objectHashMap.get("orderDate"));
            param.put("goodsErpCode",  objectHashMap.get("goodsErpCode"));
            param.put("vendorCode",  objectHashMap.get("vendorCode"));
            BigDecimal gstPrice=  getBaseMapper().getGstPrice(param);
            if (StrUtil.isEmptyIfStr((gstPrice))) {
                param.remove("orderDate");
                param.put("orderDates",  objectHashMap.get("orderDate"));
                BigDecimal gstPrices=  getBaseMapper().getGstPrice(param);
                if (!StrUtil.isEmptyIfStr((gstPrices))) {
                    objectHashMap.put("singlePrice", gstPrices);
                }else {
                    objectHashMap.put("singlePrice", 0);
                }
            }else {
                objectHashMap.put("singlePrice",gstPrice);
            }
            BigDecimal orderNum=  new BigDecimal(objectHashMap.get("orderNum").toString());
            BigDecimal price=  new BigDecimal(objectHashMap.get("gstPrice").toString());
            BigDecimal singlePrice=  new BigDecimal(objectHashMap.get("singlePrice").toString());
            BigDecimal costReductionPrice = (singlePrice.multiply(orderNum)).subtract((price.multiply(orderNum)));
            objectHashMap.put("costReductionPrice",costReductionPrice);
            BigDecimal subtract = singlePrice.subtract(price);
            objectHashMap.put("costReduction",subtract);
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    /**
     * 小程序 - 首页查询采购方/供应商关于订单的信息提醒条数(单表)
     * @param params
     * @return
     */
    @Override
    public Map<String,Object> findOrderMsgSingleCount(Map<String, Object> params) {
        Map<String,Object> orderMsgSingleCount = new HashMap<>();
        params.put("replyMinute",1440);
        for (int i = 1; i <=3 ; i++) {
            params.put("whereType",i);
            orderMsgSingleCount.put("count"+i,getBaseMapper().findOrderMsgSingleCount(params));
        }
        return orderMsgSingleCount;
    }

    /**
     * 小程序 - 首页查询采购方/供应商关于订单的信息提醒条数(主从表)
     * @param params
     * @return
     */
    @Override
    public Map<String,Object> findOrderMsgMasterSlaveCount(Map<String, Object> params) {
        Map<String,Object> orderMsgMasterSlaveCount = new HashMap<>();
        for (int i = 1; i <=3 ; i++) {
            params.put("whereType",i);
            orderMsgMasterSlaveCount.put("count"+i,getBaseMapper().findOrderMsgMasterSlaveCount(params));
        }
        return orderMsgMasterSlaveCount;
    }

    /**
     * 通过接口拉取采购订单信息
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean pullPurchaseOrderByJoggle(Map<String,Object> params) {
        Long tenantId = commonService.getTenantId();
        params.put("tenantId",tenantId);
        //调用金蝶接口拉取采购订单信息
        this.pullPurchaseOrderByJinDie(params);
        return true;
    }

    /**
     * 拉取ERP时间范围内整单关闭的数据，更改订单数据
     * @param params
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean updateCloseOrderListByErp(Map<String,Object> params) {
        Long tenantId = commonService.getTenantId();
        params.put("tenantId",tenantId);
        List<PurchaseOrderVo> purchaseOrderVoList = purchaseOrderJoggleService.queryClosePurchaseOrderList(params);
        if (CollectionUtil.isNotEmpty(purchaseOrderVoList)){
            HashMap<String, List<PurchaseOrderVo>> map = new HashMap<>();
            //循环
            for (PurchaseOrderVo purchaseOrderVo:purchaseOrderVoList){
                //根据特定键值去重数据
                //主表键值说明 - 根据采购订单号进行分组
                String mainTableKey = String.format("purNo[%s]", purchaseOrderVo.getFBillNo());
                //根据key从获取数据
                List<PurchaseOrderVo> arrayPurchaseOrder = map.get(mainTableKey);
                if (arrayPurchaseOrder == null) {
                    arrayPurchaseOrder = new ArrayList<>();
                }
                arrayPurchaseOrder.add(purchaseOrderVo);
                map.put(mainTableKey, arrayPurchaseOrder);
            }
            //迭代器
            Iterator<String> iter = map.keySet().iterator();
            while (iter.hasNext()) {
                //主表分组key
                String key = iter.next();
                List<PurchaseOrderVo> purchaseOrderListIn = map.get(key);
                //获取第一条数据
                PurchaseOrderVo firstData = purchaseOrderListIn.get(0);
                //根据ERP订单数据中的订单号在系统中查询是否存在
                PurEntity purEntity = this.getOne(new LambdaQueryWrapper<PurEntity>()
                        .eq(PurEntity::getPurNo, firstData.getFBillNo())
                        .eq(PurEntity::getTenantId, tenantId)
                );
                //purEntity采购订单信息不存在时，保存新增
                if (purEntity != null){
                    /**
                     * ERP采购订单关闭状态一般为A-未关闭，B-已关闭
                     */
                    if(firstData.getFCloseStatus().equals("B")){
                        purEntity.setDeleteFlag(WhetherEnum.YES.getCode());
                    }
                    purItemService.closeWholeOrderItem(purEntity);
                    this.updateById(purEntity);
                }
            }
        }
        return false;
    }

    /**
     * 获取采购订单机构信息
     * @param purEntity
     * @param params
     * @return
     */
    @Override
    public PurEntity getOrderDeptInfo(PurEntity purEntity, Map<String, Object> params) {
        Map<String, Object> orderDeptInfo = this.getBaseMapper().getOrderDeptInfo(params);
        if (orderDeptInfo != null){
            purEntity.setDeptId(Long.parseLong(orderDeptInfo.get("id").toString()));
            purEntity.setDeptCode(orderDeptInfo.get("deptCode").toString());
            purEntity.setDeptName(orderDeptInfo.get("deptName").toString());
        }
        return purEntity;
    }

    /**
     * 下载更改初始化订单数据的入库数据模板信息
     * @return
     */
    @Override
    public List<UpdateInitOrderDataVO> downloadUpdateInitOrderDataVO(Map<String,Object> params) {
        List<UpdateInitOrderDataVO> list = new ArrayList<>();
        return list;
    }

    /**
     * 导入更新初始化订单数据
     * @param list
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean importUpdateInitOrder(List<UpdateInitOrderDataVO> list) {
        if(CollectionUtil.isNotEmpty(list)){
            for (UpdateInitOrderDataVO updateInitOrderDataVO:list) {
                PurEntity purEntity = getOne(new LambdaQueryWrapper<PurEntity>().eq(PurEntity::getPurNo, updateInitOrderDataVO.getOrderNo()));
                if (purEntity != null){
                    purItemService.importUpdateInitOrderItem(purEntity,list);
                }
            }
        }
        return false;
    }

    /**
     * 根据ERP采购订单变更日期拉取数据变更SRM采购订单
     * @param params
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public void changeOrderByErpChangeDate(Map<String, Object> params) {
        params.put("ifChange",1);
//        params.put("isPullByDay",1);
        List<PurchaseOrderVo> purchaseOrderVoList = purchaseOrderJoggleService.queryPurchaseOrderList(params);
        //从接口中获取到的数据不为空
        if(CollectionUtil.isNotEmpty(purchaseOrderVoList)){
            HashMap<String, List<PurchaseOrderVo>> map = new HashMap<>();
            //循环
            for (PurchaseOrderVo purchaseOrderVo:purchaseOrderVoList){
                //根据特定键值去重数据
                //主表键值说明 - 根据采购订单号进行分组
                String mainTableKey = String.format("purNo[%s]", purchaseOrderVo.getFBillNo());
                //根据key从获取数据
                List<PurchaseOrderVo> arrayPurchaseOrder = map.get(mainTableKey);
                if (arrayPurchaseOrder == null) {
                    arrayPurchaseOrder = new ArrayList<>();
                }
                arrayPurchaseOrder.add(purchaseOrderVo);
                map.put(mainTableKey, arrayPurchaseOrder);
            }
            //迭代器
            Iterator<String> iter = map.keySet().iterator();
            while (iter.hasNext()) {
                //主表分组key
                String key = iter.next();
                List<PurchaseOrderVo> purchaseOrderListIn = map.get(key);
                //获取第一条数据
                PurchaseOrderVo firstData = purchaseOrderListIn.get(0);
                //根据ERP订单数据中的订单号在系统中查询是否存在
                PurEntity purEntity = this.getOne(new LambdaQueryWrapper<PurEntity>()
                        .eq(PurEntity::getPurNo, firstData.getFBillNo())
                        .eq(PurEntity::getTenantId, commonService.getTenantId())
                );
                if (purEntity != null){
                    purItemService.changeOrderItemByErpChangeDate(purEntity,purchaseOrderListIn);
                }
            }
        }
    }

    /**
     * SAP同步采购订单数据至SRM(新)
     * @param orderList
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean synchronizePurOrderInSAP(List<PurEntityVO> orderList) {
        if (CollectionUtil.isNotEmpty(orderList)){
            for (PurEntityVO orderVo:orderList) {
                PurEntity order = getOrderByPurNo(commonService.getTenantId(), orderVo.getPurNo());
                // 不存在采购订单
                if (Objects.isNull(order)){
                    order = new PurEntity();
                    order = BeanConverter.convert(orderVo,PurEntity.class);
                    order.setMrpRegion(order.getDeptCode());
//                    if (StrUtil.isEmptyIfStr(order.getMrpRegion())){
//                        order.setMrpRegion(order.getDeptCode());
                        // 工厂为1040时，判断一下地址来区分不同的MRP区域
                        if ("1040".equals(order.getDeptCode())){
                            if (!StrUtil.isEmptyIfStr(orderVo.getShippingAddress()) && !orderVo.getShippingAddress().contains("岳西")){
                                order.setMrpRegion("1040-03");
                            }
                        }
//                    }
                    order.setTenantId(commonService.getTenantId());
                    order.setTenantPId(0L);
                    order.setStat(PurStatEnum.CONFIRM.getValue());
                    order.setReplyStat(PurReplyStatEnum.e.getValue());
                    order.setReplyDate(new Date());
                    //系统发布日期
                    order.setPublishDate(new Date());
                    order.setDeleteFlag(WhetherEnum.NO.getCode());
                    order.setReserved06(orderVo.getAttr6());
                    // 计算出明细行的总含税金额
                    BigDecimal totalAmount = orderVo.getPurItemEntityList().stream().filter(item -> !WhetherEnum.YES.getCode().equals(item.getDeleteFlag())).map(orderItemPrice -> orderItemPrice.getGstPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    order.setTotalAmount(totalAmount);
                    this.save(order);
                    purItemService.synchronizePurOrderItemInSAP(order,orderVo.getPurItemEntityList());
                } else {
                    order.setShippingAddress(orderVo.getShippingAddress());
//                    if (StrUtil.isEmptyIfStr(order.getMrpRegion())){
//                        order.setMrpRegion(order.getDeptCode());
                        // 工厂为1040时，判断一下地址来区分不同的MRP区域
                        if ("1040".equals(order.getDeptCode())){
                            if (!StrUtil.isEmptyIfStr(orderVo.getShippingAddress()) && !orderVo.getShippingAddress().contains("岳西")){
                                order.setMrpRegion("1040-03");
                            }
                        }
//                    }
                    order.setReserved06(orderVo.getAttr6());
                    order.setPurchasingGroup(orderVo.getPurchasingGroup());
                    order.setPurId(orderVo.getPurId());
                    order.setPurName(orderVo.getPurName());
                    // 存在采购订单
                    purItemService.synchronizePurOrderItemInSAP(order,orderVo.getPurItemEntityList());
                    // 计算出明细行的总含税金额
                    BigDecimal totalAmount = orderVo.getPurItemEntityList().stream().filter(item -> !WhetherEnum.YES.getCode().equals(item.getDeleteFlag())).map(orderItemPrice -> orderItemPrice.getGstPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    order.setTotalAmount(totalAmount);
                    this.updateById(order);
                }
            }
        }
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public boolean synchronizePurOrderBySrm(List<PurEntityVO> orderList) {
        if (CollectionUtil.isNotEmpty(orderList)) {
            orderList.forEach(orderVo -> {
                PurEntity order = this.getOne(new LambdaQueryWrapper<PurEntity>()
                        .eq(PurEntity::getTenantId,orderVo.getTenantId())
                        .eq(PurEntity::getPurNo,orderVo.getPurNo()));
                if (ObjectUtil.isEmpty(order)){
                    order = new PurEntity();
                    order = BeanConverter.convert(orderVo,PurEntity.class);
                    order.setMrpRegion(order.getDeptCode());
                    order.setStat(PurStatEnum.WAITREPLY.getValue());
                    order.setReplyStat(PurReplyStatEnum.q.getValue());
                    //系统发布日期
                    order.setPublishDate(new Date());
                    order.setSyncDate(new Date());
                    order.setDeleteFlag(WhetherEnum.NO.getCode());
                    order.setReserved06(orderVo.getAttr6());
                    this.save(order);
                    purItemService.synchronizePurOrderItemBySrm(order, orderVo);
                    // 发送站内信及邮箱通知供应商
                    this.sendPurMsgToVendor(order);
                } else {
                    order.setShippingAddress(orderVo.getShippingAddress());
                    order.setReserved06(orderVo.getAttr6());
                    order.setPurchasingGroup(orderVo.getPurchasingGroup());
                    order.setPurId(orderVo.getPurId());
                    order.setPurName(orderVo.getPurName());
                    List<Long> updateItemIds = purItemService.synchronizePurOrderItemBySrm(order, orderVo);
                    if (orderVo.getOperType().equals(2)){
                        purItemService.deleteErpOrderDelData(order.getId(), updateItemIds);
                    }
                    List<PurItemEntity> purItemList = purItemService.queryByPurIdList(order.getId());
                    List<PurItemEntity> notReplyList = purItemList.stream()
                            .filter(item -> !WhetherEnum.YES.getCode().equals(item.getIsClose())
                                    && PurlineStatEnum.WAITREPLY.getValue().equals(item.getItemStat()))
                            .collect(Collectors.toList());
                    List<PurItemEntity> replyList = purItemList.stream()
                            .filter(item -> !WhetherEnum.YES.getCode().equals(item.getIsClose())
                                    && !PurlineStatEnum.WAITREPLY.getValue().equals(item.getItemStat()))
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(notReplyList) && CollectionUtil.isNotEmpty(replyList)){
                        order.setStat(PurStatEnum.PART_REPLY.getValue());
                        order.setReplyStat(PurReplyStatEnum.w.getValue());
                    }
                    order.setDeleteFlag(WhetherEnum.NO.getCode());
                    // 已关闭的数据
                    List<PurItemEntity> closeItemList = purItemList.stream().filter(item -> WhetherEnum.YES.getCode().equals(item.getIsClose())).collect(Collectors.toList());
                    // purItemList.size() == closeItemList.size()为全部关闭
                    if (purItemList.size() == closeItemList.size()){
                        order.setDeleteFlag(WhetherEnum.YES.getCode());
                    }
                    List<PurItemEntity> itemList = purItemService.list(new LambdaQueryWrapper<PurItemEntity>().eq(PurItemEntity::getPurId, order.getId()).eq(PurItemEntity::getIsClose, WhetherEnum.YES.getCode()));
                    if (CollectionUtil.isNotEmpty(itemList)){
                        BigDecimal totalAmount = itemList.stream().filter(item -> !WhetherEnum.YES.getCode().equals(item.getDeleteFlag())).map(orderItemPrice -> orderItemPrice.getGstPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
                        order.setTotalAmount(totalAmount);
                    }
                    this.updateById(order);
                    // 发送站内信及邮箱通知供应商
                    this.sendPurMsgToVendor(order);
                }
            });
        }
        return true;
    }

    /**
     * 根据单号获取
     * @param orderNo
     * @return
     */
    @Override
    public PurEntityVO getOrderVoByNo(String orderNo) {
        PurEntity purEntity = this.getOrderByPurNo(commonService.getTenantId(), orderNo);
        if (Objects.nonNull(purEntity)){
            return BeanConverter.convert(purEntity,PurEntityVO.class);
        }
        return null;
    }

    /**
     * 采购申请转采购订单
     * @param purRequestTurnVO
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Throwable.class)
    public Boolean purRequestTurnPurOrder(PurRequestTurnVO purRequestTurnVO) {

        Assert.isNull(purRequestTurnVO,String.format("采购申请转采购订单数据为空"));
        if (CollectionUtil.isEmpty(purRequestTurnVO.getPurRequestTurnItemVOList())){
            throw new RRException(String.format("采购申请转采购订单明细数据为空"));
        }

        // 获取采购申请IDList
        List<Long> purReqIds = purRequestTurnVO.getPurRequestTurnItemVOList().stream().map(PurRequestTurnItemVO::getPurReqId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(purReqIds)){
            throw new RRException(String.format("采购申请转采购订单的采购申请ID为空"));
        }
        // 获取采购申请主表数据
        List<PurRequestEntity> purRequestList = purRequestService.listByIds(purReqIds);
        if (CollectionUtil.isEmpty(purRequestList)){
            throw new RRException(String.format("采购申请转采购订单的采购申请数据为空"));
        }
        List<Integer> purTypeList = purRequestList.stream().map(PurRequestEntity::getPurType).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(purTypeList)){
            throw new RRException(String.format("采购申请转采购订单的采购申请类型为空"));
        }
        if (purTypeList.size() > 1){
            throw new RRException(String.format("采购申请转采购订单的采购申请类型不统一"));
        }
        Integer purType = purTypeList.get(0);
        // 修改采购申请详情
        purRequestTurnVO.getPurRequestTurnItemVOList().stream().forEach(item -> {

            LambdaUpdateWrapper<PurRequestItemEntity> updateWrapper = new LambdaUpdateWrapper<PurRequestItemEntity>()
                    .eq(PurRequestItemEntity::getId, item.getId())
                    .set(PurRequestItemEntity::getTranQuantity, item.getTranQuantity().add(item.getThisTimeReqQuantity()))
                    .set(PurRequestItemEntity::getClosedQuantity, item.getClosedQuantity());
            purRequestItemService.update(updateWrapper);
        });

        PurEntity purOrder = BeanConverter.convert(purRequestTurnVO, PurEntity.class);
        purOrder.setId(null);
        purOrder.setTenantId(commonService.getTenantId());
        purOrder.setTenantPId(0L);
        purOrder.setPurNo(sysClient.getBillNo("order_pur"));
        purOrder.setPurchasingGroup("101");
        purOrder.setStat(PurStatEnum.WAITREPLY.getValue());
        purOrder.setReplyStat(PurReplyStatEnum.q.getValue());
        purOrder.setOrderDate(purRequestTurnVO.getOrderDate());
        purOrder.setPublishDate(new Date());
        purOrder.setPurId(commonService.getUser().getUserId());
        purOrder.setPurName(commonService.getUser().getUserName());
        purOrder.setDeleteFlag(WhetherEnum.NO.getCode());
        purOrder.setOrderType(1);
        purOrder.setReserved06(purType.equals(3)?"模具":"Z001");
        this.save(purOrder);
        purItemService.purRequestTurnPurOrderItem(purOrder,purRequestTurnVO.getPurRequestTurnItemVOList());
        return Boolean.TRUE;
    }

    /**
     * 查询采购订单未交数量（订单数量-已入库数量）
     * @param purQuery
     * @return
     */
    @Override
    public BigDecimal queryPurOrderUnpaidQty(PurQuery purQuery) {
        return this.getBaseMapper().queryPurOrderUnpaidQty(purQuery);
    }

    @Override
    public PageUtils dataList(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().dataList(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils orderDelivery(Map<String, Object> params) {
        IPage<HashMap<String, Object>> page = new Query<HashMap<String, Object>>().getPage(params);
        List<HashMap<String, Object>> list = getBaseMapper().orderDelivery(page,params);
        page.setRecords(list);
        return new PageUtils(page);
    }


    /***
     * 通过接口数据查询是否存在 新增
     * @param purEntity
     */
    private PurEntity doesItExist(PurEntity purEntity) {
        QueryWrapper<PurEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("pur_no",purEntity.getPurNo());
        queryWrapper.eq("source_id",purEntity.getSourceId());
        PurEntity entity = this.getOne(queryWrapper);
        if (entity!=null){
            return  entity;
        }else {
            return  null;
        }
    }

    private Wrapper<PurItemEntity> getQueryWrapper(Map<String, Object> params) {
        QueryWrapper<PurItemEntity> queryWrapper=new QueryWrapper<>();
        /**
         * 主表id放第一列，防止从物料中筛选 例
         * SELECT COUNT(*) FROM order_pur_item WHERE (goods_erp_code LIKE '%N050304-000001-001%' OR goods_name = 'N050304-000001-001' AND pur_id = 687) ORDER BY id ASC
         * SELECT COUNT(*) FROM order_pur_item WHERE (pur_id = 687 AND goods_erp_code LIKE '%N050304-000001-001%' OR goods_name = 'N050304-000001-001' ) ORDER BY id ASC
         */
        if(params.containsKey("ids") && !StrUtil.isEmptyIfStr((params.get("ids")))){//主表id
            queryWrapper.and(wrapper ->wrapper.eq("pur_id",Long.parseLong(params.get("ids").toString())));
        }
        if (!StrUtil.isEmptyIfStr((params.get("tenantIds")))) {
            queryWrapper.and(wrapper ->wrapper.eq("tenant_id", StringUtils.trim(params.get("tenantIds").toString())));
        }
        //查询明细状态
        if(params.containsKey("stat") && !StrUtil.isEmptyIfStr((params.get("stat")))){//主表id
            queryWrapper.and(wrapper ->wrapper.eq("item_stat",Long.parseLong(params.get("stat").toString())));
        }
        if (!StrUtil.isEmptyIfStr((params.get("goodsInfo")))) {
            queryWrapper.and(wrapper ->wrapper.like("goods_erp_code", StringUtils.trim(params.get("goodsInfo").toString()))
                    .or().like("goods_name", StringUtils.trim(params.get("goodsInfo").toString())));
        }
        // 序号查询
        if (!StrUtil.isEmptyIfStr((params.get("seq")))) {
            queryWrapper.and(wrapper ->wrapper.eq("seq",Long.parseLong(params.get("seq").toString())));
        }
        if (!StrUtil.isEmptyIfStr((params.get("deleteFlag")))) {
            queryWrapper.lambda().eq(PurItemEntity::getDeleteFlag,params.get("deleteFlag").toString().trim());
        }
        queryWrapper.orderByAsc("id");
        return queryWrapper;
    }



    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Map<String,Object> importNew(List<PurImportVO> list,int operType) {
        //校验
        Map<String,Object> msgMap= new HashMap<>();
        Map<String, PurEntity> orderMap = new HashMap<>();
        Long tenantId = commonService.getTenantId();
        for (PurImportVO purVO : list) {
            //设置采购订单号
            PurItemEntity scmOrderPurItemEntity_hi = JSONObject.parseObject(JSONObject.toJSONString(purVO), PurItemEntity.class);
            scmOrderPurItemEntity_hi.setTenantId(tenantId);//组织ID
            scmOrderPurItemEntity_hi.setTenantPId(new Long(0));//关联组织ID
            scmOrderPurItemEntity_hi.setBarcodeType(0);//条码类型 无设置
            scmOrderPurItemEntity_hi.setItemStat(-1);
            scmOrderPurItemEntity_hi.setDeliveryStat(1);
            scmOrderPurItemEntity_hi.setFixNum(BigDecimal.ZERO);//已送货量为0
            scmOrderPurItemEntity_hi.setWaitNum(purVO.getOrderNum());//待送货量默认为订单数量
            scmOrderPurItemEntity_hi.setDeleteFlag(0);//未删除
            scmOrderPurItemEntity_hi.setInvoiceType(1);//发票类型;1-增值税专用发票;2-普通发票;3-专业发票
            scmOrderPurItemEntity_hi.setMakeNum(BigDecimal.ZERO);//已制送货单数量为0
            scmOrderPurItemEntity_hi.setSelfNum(BigDecimal.ZERO);//入库退货数量为0
            scmOrderPurItemEntity_hi.setDeliveryType(1);////送货方式;1-按订单交期送货;2-按送货计划送货
            scmOrderPurItemEntity_hi.setPurchaseConfirm(0);//采购方是否确认;0-无;1-待确认;2-已确认
            scmOrderPurItemEntity_hi.setChangeCount(0);//变更次数
            if (orderMap != null && orderMap.get(purVO.getPurNo()) != null) {
                PurEntity scmOrderPurEntity_HI =orderMap.get(purVO.getPurNo());
                List<PurItemEntity> scmOrderPurItemEntity_hiList =scmOrderPurEntity_HI.getPurItemEntityList();
                scmOrderPurItemEntity_hiList.add(scmOrderPurItemEntity_hi);
                scmOrderPurEntity_HI.setPurItemEntityList(scmOrderPurItemEntity_hiList);
                orderMap.put(purVO.getPurNo(),scmOrderPurEntity_HI);
            }else{
                PurEntity scmOrderPurEntity_HI = JSONObject.parseObject(JSONObject.toJSONString(purVO), PurEntity.class);
                scmOrderPurEntity_HI.setTenantId(tenantId);//组织ID
                scmOrderPurEntity_HI.setTenantPId(new Long(0));//关联组织ID
                scmOrderPurEntity_HI.setTenantName(commonService.getUser().getEnt().getEntName());
                // 采购员
                scmOrderPurEntity_HI.setTenantId(tenantId);//组织ID
                scmOrderPurEntity_HI.setTenantPId(new Long(0));//关联组织ID
                /**
                 * 该供应商所属的采购员
                 */
                String purName = purVO.getCurrencyName();
                if (purName != null) {
                    HashMap<String, Object> mapPurName = new HashMap();
                    mapPurName.put("userName", purVO.getCurrencyName());
                }

                scmOrderPurEntity_HI.setStat(PurStatEnum.MAKING.getValue());//单据状态为制单
                scmOrderPurEntity_HI.setReplyStat(1);//单据状态为为答交状态
                scmOrderPurEntity_HI.setDeleteFlag(0);//未删除
                scmOrderPurEntity_HI.setSyncDate(scmOrderPurEntity_HI.getOrderDate());
                List<PurItemEntity> scmOrderPurItemEntity_hiList = new ArrayList<>();
                scmOrderPurItemEntity_hiList.add(scmOrderPurItemEntity_hi);
                scmOrderPurEntity_HI.setPurItemEntityList(scmOrderPurItemEntity_hiList);
                orderMap.put(purVO.getPurNo(),scmOrderPurEntity_HI);
            }
        }
        for (String purNo: orderMap.keySet()) {
            //传数据,根据订单号判断是否已存在
            HashMap<String,Object> purEntity = this.getPurEntity(purNo,tenantId);
            PurEntity scmOrderPurEntity_hi = orderMap.get(purNo);
            if(purEntity!=null){
                scmOrderPurEntity_hi.setId(Long.valueOf(purEntity.get("id")+""));
            }else {
                scmOrderPurEntity_hi.setChangeCount(0);
                this.save(scmOrderPurEntity_hi);
            }
            List<PurItemEntity> scmOrderPurItemEntity_hiList = scmOrderPurEntity_hi.getPurItemEntityList();
            for (PurItemEntity scmOrderPurItemEntity_hi:
                    scmOrderPurItemEntity_hiList) {
                scmOrderPurItemEntity_hi.setPurId(scmOrderPurEntity_hi.getId());//采购订单ID
                JSONObject jsonObject = new JSONObject();
                BeanUtil.copyProperties(scmOrderPurItemEntity_hi,jsonObject, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                jsonObject.put("vendorId",scmOrderPurEntity_hi.getVendorId());
                jsonObject.put("vendorCode",scmOrderPurEntity_hi.getVendorCode());
                jsonObject.put("vendorName",scmOrderPurEntity_hi.getVendorName());

                JSONObject jgResponseData = baseClient.saveVendorGoods(jsonObject);
                if(Long.valueOf(jgResponseData.get("code")+"").equals(500L)){
//                    throw new RuntimeException();
                    throw new RRException(jgResponseData.get("msg")+"");
                }
                //设置急单标志
                int days = (int) ((scmOrderPurItemEntity_hi.getDeliveryDate().getTime() - scmOrderPurEntity_hi.getOrderDate().getTime()) / (1000 * 3600 * 24));
                if(days>=0 && days<=3) {
                    scmOrderPurItemEntity_hi.setSign(PurServiceImpl.SIGN_BUSY);
                }
                if(scmOrderPurItemEntity_hi.getSoureNo()==null || "null".equals(scmOrderPurItemEntity_hi.getSoureNo())){
                    scmOrderPurItemEntity_hi.setSoureNo("");
                }
                if(scmOrderPurItemEntity_hi.getGoodsClassName()==null || "null".equals(scmOrderPurItemEntity_hi.getGoodsClassName())){
                    scmOrderPurItemEntity_hi.setGoodsClassName("");
                }
                purItemService.save(scmOrderPurItemEntity_hi);
            }
            if(operType==PurServiceImpl.TYPE_INTERFACE){
                scmOrderPurEntity_hi.setStat(PurStatEnum.WAITREPLY.getValue());//接口下载的订单不需要审核，自动发布变为待答交状态
                this.updateById(scmOrderPurEntity_hi);
                for (PurItemEntity scmOrderPurItemEntityHi:
                        scmOrderPurItemEntity_hiList) {
                    scmOrderPurItemEntityHi.setItemStat(1);//待答交
                    if(scmOrderPurItemEntityHi.getSign()==null){
                        scmOrderPurItemEntityHi.setSign("");
                    }
                    scmOrderPurItemEntityHi.setSign(scmOrderPurItemEntityHi.getSign()+PurServiceImpl.SIGN_NOVISIT);
                    purItemService.updateById(scmOrderPurItemEntityHi);
                }
                String remark="发布采购订单"+scmOrderPurEntity_hi.getPurNo();
                operType=PurServiceImpl.TYPE_PUBLISH;
                //生成销售订单操作日志
                JSONObject jsonObject33 = this.saveOperMsg(OperBillEnum.PURORDER.getValue(), OperTypeEnum.CREATE.getValue(), scmOrderPurEntity_hi.getId(), "导入销售订单" + scmOrderPurEntity_hi.getPurNo(), scmOrderPurEntity_hi.getPurNo());
                int i= Integer.valueOf(jsonObject33.get("code")+"");
                if(i==500){
                    throw new RRException(String.format("[%s] "+jsonObject33.get("msg"),scmOrderPurEntity_hi.getPurNo()));
                }
            }
        }
        return msgMap;
    }


    private HashMap<String,Object> getPurEntity(String purNo,Long tenantId){
        JSONObject purObject= new JSONObject();
        purObject.put("purNo",purNo);
        purObject.put("deleteFlag",0);
        purObject.put("tenantId",tenantId);
        PageUtils pageUtils = this.queryPage(purObject);
        List list = pageUtils.getList();
        if(list!=null && CollectionUtils.isNotEmpty(list)) {
            HashMap<String, Object> map = (HashMap<String, Object>) list.get(0);
            return map;
        }else{
            return null;
        }
    }

    /**
     *采购订单当前页or全部导出
     * @param params
     * @return
     */
    @Override
    public List<PurExportVO> exportList(Map<String, Object> params) {
        List<PurEntity> list;


        if (!StrUtil.isBlankIfStr(params.get("exportType")) && "0".equals(params.get("exportType") + "")) {
        }else{
            params.put("page", 1);
            params.put("limit", Long.MAX_VALUE);
        }

        list= this.queryPage(params).getList();

        List<PurExportVO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)) {
            resultList = BeanConverter.convertList(list, PurExportVO.class);
        }
        return resultList;
    }


    /***********************************************************************************************/
    /****************************************** 私有方法 ********************************************/
    /***********************************************************************************************/

    /**
     * 调用金蝶接口拉取采购订单信息
     * @param params
     */
    @GlobalTransactional(rollbackFor = Throwable.class)
    private void pullPurchaseOrderByJinDie(Map<String,Object> params){
        Long tenantId = Long.parseLong(params.get("tenantId").toString());
        //调用接口获取数据
        List<PurchaseOrderVo> purchaseOrderVoList = purchaseOrderJoggleService.queryPurchaseOrderList(params);
        logger.info("采购订单条数（含明细行）="+purchaseOrderVoList.size());
        //从接口中获取到的数据不为空
        if (CollectionUtil.isNotEmpty(purchaseOrderVoList)) {
            int initialCapacity = Math.max((int) (purchaseOrderVoList.size() / 0.75f) + 1, 16);
            HashMap<String, List<PurchaseOrderVo>> map = new HashMap<>(initialCapacity);

            // 分组去重
            for (PurchaseOrderVo purchaseOrderVo : purchaseOrderVoList) {
                String mainTableKey = "purNo[" + purchaseOrderVo.getFBillNo() + "]";
                map.computeIfAbsent(mainTableKey, k -> new ArrayList<>()).add(purchaseOrderVo);
            }

            // 日期格式复用
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

            // 迭代处理
            for (Map.Entry<String, List<PurchaseOrderVo>> entry : map.entrySet()) {
                List<PurchaseOrderVo> purchaseOrderListIn = entry.getValue();
                PurchaseOrderVo firstData = purchaseOrderListIn.get(0);

                PurEntity purEntity = this.getOne(new LambdaQueryWrapper<PurEntity>()
                        .eq(PurEntity::getPurNo, firstData.getFBillNo())
                        .eq(PurEntity::getTenantId, tenantId));

                boolean ifAdd = !StrUtil.isEmptyIfStr(params.get("ifAdd"));
                boolean ifUpdate = !StrUtil.isEmptyIfStr(params.get("ifUpdate"));

                if (purEntity == null && ifAdd) {
                    purEntity = new PurEntity();
                    purEntity.setTenantPId(0L);
                    purEntity.setTenantId(tenantId);
                    purEntity.setTenantName(commonService.getTenantInfo().getEntName());
                    purEntity.setPurNo(firstData.getFBillNo());
                    purEntity.setSourceId(Long.parseLong(firstData.getFID()));
                    logger.info("供应商编码="+firstData.getFSupplierFNumber());
                    VendorVO vendor = baseClient.findVendorByVendorCode(firstData.getFSupplierFNumber());
                    if (ObjectUtil.isNotEmpty(vendor)) {
                        purEntity.setVendorId(vendor.getSoureId());
                        purEntity.setVendorName(vendor.getVendorName());
                        purEntity.setVendorCode(vendor.getVendorErpCode());
                        purEntity.setPurId(vendor.getPurId());
                        purEntity.setPurName(vendor.getPurName());
                    }

                    purEntity.setOrderType(OrderTypeEnum.ERP.getValue());
                    purEntity.setDeleteFlag(WhetherEnum.NO.getCode());
                    purEntity.setStat(PurStatEnum.WAITREPLY.getValue());
                    purEntity.setReplyStat(PurReplyStatEnum.q.getValue());
                    purEntity.setIsPrint(WhetherEnum.NO.getCode());
                    purEntity.setPrintCount(0);
                    purEntity.setDeptName(firstData.getFPurchaserGroupFName());

                    Map<String, Object> currencyParams = new HashMap<>();
                    currencyParams.put("tenantId", purEntity.getTenantId());
                    currencyParams.put("remark", firstData.getFSettleCurrFNumber());
                    CurrencyVO currencyVo = baseClient.getCurrencyInfo(currencyParams);
                    if (currencyVo != null) {
                        purEntity.setCurrencyName(currencyVo.getCurrencyName());
                    }

                    BigDecimal totalAmount = BigDecimal.ZERO;
                    purEntity.setTotalAmount(totalAmount);

                    try {
                        if (!StrUtil.isEmptyIfStr(firstData.getFDate())) {
                            purEntity.setOrderDate(df.parse(firstData.getFDate()));
                        }
                        if (!StrUtil.isEmptyIfStr(firstData.getFApproveDate())) {
                            purEntity.setErpApproveDate(df.parse(firstData.getFApproveDate()));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    purEntity.setPublishDate(new Date());
                    purEntity.setOrderFlag(OrderImportTypeEnum.ERP.getValue());
                    purEntity.setVendorDeliveryAddress(firstData.getFProviderAddress());
                    purEntity.setBsart(BsartTypeEnum.Z005.getValue());

                    if (!StrUtil.isEmptyIfStr(firstData.getFPurchaseFNumber())) {
                        Map<String, Object> queryParams = new HashMap<>();
                        queryParams.put("tenantId", purEntity.getTenantId());
                        queryParams.put("deptCode", firstData.getFPurchaseFNumber());
                        purEntity = this.getOrderDeptInfo(purEntity, queryParams);
                    }

                    if (!StrUtil.isEmptyIfStr(firstData.getFCreatorFName())) {
                        List<UserSimpleVO> userSimpleVOS = sysClient.queryUserByNames(purEntity.getTenantId(), firstData.getFCreatorFName());
                        if (!userSimpleVOS.isEmpty()) {
                            UserSimpleVO userSimpleVO = userSimpleVOS.get(0);
                            purEntity.setPurId(userSimpleVO.getUserId());
                            purEntity.setPurName(userSimpleVO.getUserName());
                        }
                    }

                    purEntity.setReserved06(firstData.getFPurchaseFNumber());
                    purEntity.setReserved07(firstData.getFPurchaseFName());
                    purEntity.setReserved08(firstData.getFCloseStatus());

                    if ("B".equals(firstData.getFCloseStatus())) {
                        purEntity.setDeleteFlag(WhetherEnum.YES.getCode());
                    }

                    if (purEntity.getVendorId() != null) {
                        purEntity.setStat(PurStatEnum.CONFIRM.getValue());
                        purEntity.setReplyStat(PurReplyStatEnum.e.getValue());
                        purEntity.setDeliveryType(PurItemDeliveryTypeEnum.PLAN.getValue());
                        this.save(purEntity);

                        purItemService.saveOrUpdateInfoByJinDie(purEntity, purchaseOrderListIn);

                        Map<String, Object> queryParams = new HashMap<>();
                        queryParams.put("purId", purEntity.getId());
                        totalAmount = purItemService.getOrderItemSumGstAmount(queryParams);
                        purEntity.setTotalAmount(totalAmount.setScale(6, RoundingMode.HALF_UP));
                        this.updateById(purEntity);
                    }
                } else if (purEntity != null && (ifUpdate || ifAdd)) {
                    try {
                        if (!StrUtil.isEmptyIfStr(firstData.getFDate())) {
                            purEntity.setOrderDate(df.parse(firstData.getFDate()));
                        }
                        if (!StrUtil.isEmptyIfStr(firstData.getFApproveDate())) {
                            purEntity.setErpApproveDate(df.parse(firstData.getFApproveDate()));
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    purEntity.setSourceId(Long.parseLong(firstData.getFID()));
                    purEntity.setVendorDeliveryAddress(firstData.getFProviderAddress());
                    purEntity.setBsart(BsartTypeEnum.Z005.getValue());

                    if (!StrUtil.isEmptyIfStr(firstData.getFPurchaseFNumber())) {
                        Map<String, Object> queryParams = new HashMap<>();
                        queryParams.put("tenantId", purEntity.getTenantId());
                        queryParams.put("deptCode", firstData.getFPurchaseFNumber());
                        purEntity = this.getOrderDeptInfo(purEntity, queryParams);
                    }

                    if (!StrUtil.isEmptyIfStr(firstData.getFCreatorFName())) {
                        List<UserSimpleVO> userSimpleVOS = sysClient.queryUserByNames(purEntity.getTenantId(), firstData.getFCreatorFName());
                        if (!userSimpleVOS.isEmpty()) {
                            UserSimpleVO userSimpleVO = userSimpleVOS.get(0);
                            purEntity.setPurId(userSimpleVO.getUserId());
                            purEntity.setPurName(userSimpleVO.getUserName());
                        }
                    }

                    purEntity.setReserved06(firstData.getFPurchaseFNumber());
                    purEntity.setReserved07(firstData.getFPurchaseFName());
                    purEntity.setReserved08(firstData.getFCloseStatus());

                    purEntity.setDeliveryType(PurItemDeliveryTypeEnum.PLAN.getValue());

                    if ("B".equals(firstData.getFCloseStatus())) {
                        purEntity.setDeleteFlag(WhetherEnum.YES.getCode());
                    }

                    purItemService.saveOrUpdateInfoByJinDie(purEntity, purchaseOrderListIn);

                    Map<String, Object> queryParams = new HashMap<>();
                    queryParams.put("purId", purEntity.getId());
                    BigDecimal totalAmount = purItemService.getOrderItemSumGstAmount(queryParams);
                    purEntity.setTotalAmount(totalAmount.setScale(4, RoundingMode.HALF_UP));
                    this.updateById(purEntity);
                }
            }
        }
    }

    /**
     * 修改状态校验
     *
     * @param
     */
    private void updateCheck(Long id) {
        PurEntity purEntity =this.getById(id);
    }
    /**
     * 校验订单状态
     *
     * @param
     */
    private void checkCheck(PurEntity purEntity,PurStatEnum purStatEnum) {


        if(!purEntity.getStat().equals(purStatEnum.getValue().intValue())){
            throw new RRException(String.format("[%s] 当前订单%s,禁止操作",purEntity.getPurNo(),purStatEnum.getDesc()));
        }
    }

    /**
     * 删除状态校验
     *
     * @param
     */
    private void deleteCheck(Long id) {
        PurEntity purEntity =this.getById(id);
        if (purEntity==null){
            throw new RRException(" 当前订单已不存在,请检查数据");
        }
        if (!PurReplyStatEnum.q.getValue().equals(purEntity.getReplyStat())){
            throw new RRException(String.format("[%s] 当前订单已生成送货单的采购订单,禁止删除",purEntity.getPurNo()));
        }
    }

    /**
     * 新增和修改参数校验
     *
     * @param record
     */
    private void paramsCheck(PurEntity record, Class<?> cls) {

        //设置明细的主表ID
//        ListUtils.setPropertyValue(record.getPurItemEntityList(),"pur_id",record.getId());

 /*       ValidatorUtils.validateEntity(record, cls);*/

//        if (CollectionUtils.isEmpty(record.getPurItemEntityList())) {
//            throw new RRException("采购订单明细数据不能为空");
//        }
    }

    /**
     * 根据采购订单号查询订单主表信息
     * @param tenantId
     * @param purNo
     * @return
     */
    private PurEntity getOrderByPurNo(Long tenantId,String purNo){
        return this.getOne(new LambdaQueryWrapper<PurEntity>()
                .eq(PurEntity::getTenantId,tenantId)
                .eq(PurEntity::getPurNo,purNo)
                .eq(PurEntity::getDeleteFlag,WhetherEnum.NO.getCode()));
    }


    private void updatePurInfo(PurConfirmVO confirmVO,OperTypeEnum operTypeEnum){
        //确认答交更新主表状态
        if(OperTypeEnum.CONFIRM.equals(operTypeEnum)) {
            //更新主表
            this.getBaseMapper().updatePurStat(confirmVO.getId(), confirmVO.getStat());
            //更新从表
            purItemService.updateItemStat(confirmVO);
        }
    }

    /**
     *  站内信及邮箱信息发送
     */
    private void sendEmailAndMessage(PurEntity purEntity,String content, String tenantNames){
        SysEntEntity tenantInfo = commonService.getTenantInfo();
        JSONObject mailJson = new JSONObject();
        mailJson.put("userId",0L);
        //组织类型为1-采购商,一般为采购方下发新订单/更新订单到供应商时，推送站内信消息
        if (Ent_EntTypeEnum.PURCHASER.getValue().equals(tenantInfo.getEntType())){
            mailJson.put("tenantId",purEntity.getVendorId());
            mailJson.put("content",content);
            mailJson.put("menuTitle","销售订单列表");//菜单标题
            mailJson.put("url","order/produce/vendor?id="+purEntity.getId());//菜单地址
            sysClient.sendMail(mailJson);
        }
        //组织类型为2-供应商，一般为供应商订单答交异常/拒绝订单时，推送站内信消息
        if (Ent_EntTypeEnum.VENDOR.getValue().equals(tenantInfo.getEntType())){
            mailJson.put("tenantId",purEntity.getTenantId());
            mailJson.put("content",content);
            mailJson.put("menuTitle","采购订单列表");//菜单标题
            mailJson.put("url","order/produce/tenant?id="+purEntity.getId());//菜单地址
            sysClient.sendMail(mailJson);
        }
    }

    /**
     * 发送微信公众号信息
     * @param purEntity
     */
    private void sendWxMsg(PurEntity purEntity){
        JSONObject wxMsg = new JSONObject();
        wxMsg.put("vendorId",purEntity.getVendorId());
        wxMsg.put("tenantId",purEntity.getTenantId());
        wxMsg.put("roleCode","tenantAdmin,21011");
        wxMsg.put("orderId",purEntity.getId());
        wxMsg.put("wxMsgTitle","您有一条新的消息提醒");
        wxMsg.put("wxMsg","您有一条订单待答交");
        wxMsg.put("documentNo",purEntity.getPurNo());
        wxMsg.put("documentDate",DateUtil.today());
        wxMsg.put("founder",purEntity.getPurName());
        wxMsg.put("url","/pages/srm/order/vendor/saleDetail");
        baseClient.sendCreteWxMsg(wxMsg);
    }


    /**
     * 采购订单下发通知供应商
     * @param purEntity
     */
    private void sendPurMsgToVendor(PurEntity purEntity ){
        SysEntEntity tenantInfo = commonService.getTenantInfo();
        String content = String.format("采购组织%s向您下发了新的采购订单%s",
                tenantInfo.getEntName(), purEntity.getPurNo());
        // 发送站内信
        JSONObject mailJson = new JSONObject();
        mailJson.put("userId", 0L);
        mailJson.put("tenantId", purEntity.getVendorId());
        mailJson.put("content", content);
        mailJson.put("menuTitle", "采购订单详情");
        mailJson.put("url","order/produce/vendor?id="+purEntity.getId());//菜单地址
        sysClient.sendMail(mailJson);
        // 发送邮件通知
        try {
            VendorVO vendorVo = baseClient.getVendorVoBySourceId(purEntity.getTenantId(), purEntity.getVendorId());;
            if (vendorVo != null && !StrUtil.isEmptyIfStr(vendorVo.getVendorEmail())) {
                EmailMessageVo emailMessageVo = new EmailMessageVo();
                emailMessageVo.setTenantId(purEntity.getTenantId());
                emailMessageVo.setTitle("您有一封新的邮件信息");
                String emailContent = purEntity.getVendorName() + ",您好：\n" +
                        "<br>\n" +
                        "<br>采购组织:" + tenantInfo.getEntName() + "向您下发了新的采购订单(" + purEntity.getPurNo() + ")，请及时查看并处理。\n" +
                        "<br>\n" +
                        "<br>请您及时查阅、处理！如果邮件中有任何不清楚的地方或者您需要提供任何帮助，请您联系我司对应的对接人员!\n" +
                        "<br>\n";
                emailMessageVo.setContent(emailContent);
                emailMessageVo.setEmail(vendorVo.getVendorEmail());
                baseClient.customSendEmail(emailMessageVo);
            }
        } catch (Exception e) {
            logger.error("发送邮件失败：{}", e.getMessage(), e);
        }
    }
}
