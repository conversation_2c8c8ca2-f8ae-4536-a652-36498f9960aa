{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sampleDemand\\tenant\\index.vue", "mtime": 1754405432592}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {\r\n  getSampleDemandList,\r\n  delSampleDemand,\r\n  batchIssuedSample,\r\n  updateSampleDemand,\r\n  returnSample,\r\n  localClosed,\r\n  batchUpdateBuyer\r\n} from '@/api/base/sampleDemand'\r\nimport Form from './form'\r\nimport store from \"@/store\";\r\nimport VendorProp from '@/views/popup/base/vendor/vendor'\r\nimport UserProp from '@/views/popup/sys/user/userProp'\r\n\r\nexport default {\r\n  components: {Form, VendorProp, UserProp},\r\n  name: 'base-sampleDemand-tenant',\r\n  data() {\r\n    return {\r\n      list: [],\r\n      total: 0,\r\n      showAll: false,\r\n      queryParam: {//查询条件\r\n        demandNo: '',//需求申请单号\r\n        applicant:'',//申请人\r\n        isNeedUpFile:null,//明细表检验状态\r\n        dept: '' , // 采购组织\r\n        applyDate: '', // 申请日期\r\n        startDate: '', // 开始时间\r\n        endDate: '' , // 结束时间\r\n        demandDate: '', // 需求日期\r\n        demandDateStart: '', // 需求开始日期\r\n        demandDateEnd: '', // 需求结束日期\r\n        selectType: '3', // 默认查询显示全部的数据\r\n        demandClassType: '2', // 默认查询显示采购打样单据\r\n        caseStat: '1', // 默认查询待分配的数据\r\n        page: 1,\r\n        limit: 20,\r\n      },\r\n      detailVisible: false,\r\n      qualityVisible: false,\r\n      listLoading: true,\r\n      btnLoading: false, // 按钮加载状态\r\n      selectedDatas: [],\r\n      sampleDates: [],\r\n      demandDates: [], // 需求日期选择器\r\n      applyDates: [], // 申请日期选择器\r\n      sampleItemStatOptions: store.getters.commonEnums['base.SampleItemEnums'], // 明细行检验状态\r\n      sampleStatOptions: store.getters.commonEnums['base.SampleEnums'], // 需求状态\r\n      validOps: store.getters.commonEnums['comm.ValidEnum'], // 检验状态\r\n      demandTypeOption: store.getters.commonEnums['base.DemandClassTypeEnum'], // 单据类型\r\n      selectTypeOptions: [ // 查询类型\r\n        {\r\n          key: '1',\r\n          value: '未完成'\r\n        },\r\n        {\r\n          key: '2',\r\n          value: '已完成'\r\n        },\r\n        {\r\n          key: '3',\r\n          value: '全部'\r\n        }\r\n      ],\r\n      caseStatOptions: [ // 需求状态\r\n        {\r\n          key: '',\r\n          value: '全部'\r\n        },\r\n        {\r\n          key: '1',\r\n          value: '待分配'\r\n        },\r\n        {\r\n          key: '2',\r\n          value: '已分配'\r\n        },\r\n        {\r\n          key: '3',\r\n          value: '已拒绝'\r\n        },\r\n        {\r\n          key: '4',\r\n          value: '退回'\r\n        },\r\n        {\r\n          key: '5',\r\n          value: '已结案'\r\n        }\r\n      ],\r\n    }\r\n  },\r\n  created() {\r\n    this.handleRouteQuery();\r\n    this.initData()\r\n  },\r\n  watch: {\r\n    // 监听路由变化\r\n    '$route'(to) {\r\n      if (to.path === '/base/sampleDemand/tenant' && to.query.id) {\r\n        this.handleRouteQuery();\r\n      }\r\n    }\r\n  },\r\n  filters:{\r\n    date(time){\r\n      if (!time){\r\n        return ''\r\n      }\r\n      let date = new Date(time)\r\n      let year = date.getFullYear();\r\n      let month = date.getMonth()+1;\r\n      let day = date.getDate();\r\n      return year+\"-\"+month+\"-\"+day;\r\n    }\r\n  },\r\n  methods: {\r\n    handleRouteQuery() {\r\n      const query = this.$route.query;\r\n      if (query.detailVisible && query.id) {\r\n        if (this.detailVisible) {\r\n          this.detailVisible = false;\r\n          this.$nextTick(() => {\r\n            this.detailVisible = true;\r\n            this.$nextTick(() => {\r\n              this.$refs.detail.init(query.id);\r\n            });\r\n          });\r\n        } else {\r\n          this.detailVisible = true;\r\n          this.$nextTick(() => {\r\n            this.$refs.detail.init(query.id);\r\n          });\r\n        }\r\n      }\r\n    },\r\n    // 页面初始化加载列表数据\r\n    initData() {\r\n      this.listLoading = true\r\n      let query = {\r\n        ...this.queryParam,\r\n        includeVendors: true // 请求包含供应商信息\r\n      }\r\n      getSampleDemandList(query).then(res => {\r\n        this.total = res.data.totalCount\r\n        this.list = res.data.list\r\n        // 处理供应商信息\r\n        if (this.list && this.list.length > 0) {\r\n          this.list.forEach(item => {\r\n            if (!item.sampleDemandVendorEntityList) {\r\n              item.sampleDemandVendorEntityList = [];\r\n            }\r\n          });\r\n        }\r\n\r\n        this.listLoading = false\r\n      }).catch(() => {\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    //打开新增/修改弹窗页面\r\n    addOrUpdateHandle(id) {\r\n      this.detailVisible = true;\r\n      this.$nextTick(() => {\r\n        this.$refs.detail.init(id);\r\n      })\r\n    },\r\n    //审核\r\n    handleCheck(id) {\r\n\r\n    },\r\n    search() {\r\n      this.queryParam.page=1;\r\n\r\n      // 处理申请日期\r\n      if (this.applyDates.length === 2) {\r\n        try {\r\n          const startDate = this.$dian.dateFormat(this.applyDates[0], 'YYYY-MM-DD');\r\n          const endDate = this.$dian.dateFormat(this.applyDates[1], 'YYYY-MM-DD');\r\n          this.queryParam.startDate = startDate;\r\n          this.queryParam.endDate = endDate;\r\n        } catch (error) {\r\n          this.queryParam.startDate = '';\r\n          this.queryParam.endDate = '';\r\n        }\r\n      } else {\r\n        this.queryParam.startDate = '';\r\n        this.queryParam.endDate = '';\r\n      }\r\n\r\n      // 处理需求日期\r\n      if (this.demandDates.length === 2) {\r\n        try {\r\n          const demandDateStart = this.$dian.dateFormat(this.demandDates[0], 'YYYY-MM-DD');\r\n          const demandDateEnd = this.$dian.dateFormat(this.demandDates[1], 'YYYY-MM-DD');\r\n          this.queryParam.demandDateStart = demandDateStart;\r\n          this.queryParam.demandDateEnd = demandDateEnd;\r\n        } catch (error) {\r\n          this.queryParam.demandDateStart = '';\r\n          this.queryParam.demandDateEnd = '';\r\n        }\r\n      } else {\r\n        this.queryParam.demandDateStart = '';\r\n        this.queryParam.demandDateEnd = '';\r\n      }\r\n\r\n      this.initData()\r\n    },\r\n    reset() {\r\n      this.queryParam = this.$options.data().queryParam;\r\n      this.demandDates = [];\r\n      this.applyDates = [];\r\n      this.initData()\r\n    },\r\n    //删除\r\n    handleDel(id) {\r\n      this.$confirm(this.$t('common.delTip'), this.$t('common.tipTitle'), {\r\n        type: 'warning'\r\n      }).then(() => {\r\n        delSampleDemand(id).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '删除成功',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.search()\r\n            }\r\n          })\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    callRefreshList() {\r\n      this.detailVisible = false;\r\n      this.qualityVisible = false;\r\n      this.search();\r\n    },\r\n    handleSelectionChange(selection){\r\n      this.selectedDatas = selection.map(item => item)\r\n    },\r\n    quality(row){\r\n      this.qualityVisible = true;\r\n      let isAdd = false;\r\n      if(row.ifInspection > 0){\r\n        isAdd = true\r\n      }\r\n      this.$nextTick(() => {\r\n        this.$refs.quality.init(row.id,isAdd);\r\n      })\r\n    },\r\n    // 打开供应商选择弹窗\r\n    openVendorDialog() {\r\n      if(this.selectedDatas.some(item => item.caseStat == 4)){\r\n        this.$message.error('存在已退回的物料明细记录，不允许添加供应商');\r\n        return;\r\n      }\r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message.error('请先选择物料');\r\n        return;\r\n      }\r\n      \r\n      // 获取组织ID，使用第一个选中项的组织ID\r\n      const orgId = this.selectedDatas[0].orgId;\r\n      if (!orgId) {\r\n        this.$message.error('采购组织信息为空');\r\n        return;\r\n      }\r\n      \r\n      let params = {\r\n        deptIds: orgId,\r\n      };\r\n      \r\n      this.$nextTick(() => {\r\n        this.$refs.vendor.init(params);\r\n      });\r\n    },\r\n    // 处理供应商选择结果\r\n    vendorSelect(vendorData) {\r\n      if (!vendorData || vendorData.length === 0) {\r\n        this.$message.error('未选择任何供应商');\r\n        return;\r\n      }\r\n      \r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message.error('未选择任何物料');\r\n        return;\r\n      }\r\n      \r\n      // 处理供应商数据\r\n      this.processVendorData(vendorData, this.selectedDatas);\r\n    },\r\n    // 批量生成送样通知单\r\n    batchGenerateSampleNotice(sampleIds) {\r\n      this.btnLoading = true;\r\n      \r\n      console.log(\"批量生成送样通知单 =======> \", sampleIds);\r\n      batchIssuedSample(sampleIds).then(res => {\r\n        this.$message({\r\n          message: \"送样通知单生成成功\",\r\n          type: 'success',\r\n          duration: 1500,\r\n          onClose: () => {\r\n            this.btnLoading = false;\r\n            this.search(); // 刷新列表\r\n          }\r\n        });\r\n      }).catch(err => {\r\n        this.btnLoading = false;\r\n        this.$message.error('生成送样通知单失败: ' + (err.message || '未知错误'));\r\n      });\r\n    },\r\n    \r\n    // 处理供应商数据\r\n    processVendorData(vendorData, materials) {\r\n      // 存储需要更新的数据\r\n      const updateData = [];\r\n      \r\n      // 遍历所有选中的物料\r\n      for (let i = 0; i < materials.length; i++) {\r\n        const material = materials[i];\r\n        \r\n        // 确保供应商列表存在\r\n        if (!material.sampleDemandVendorEntityList) {\r\n          material.sampleDemandVendorEntityList = [];\r\n        }\r\n        // 初始化物料明细列表\r\n        if (!material.sampleDemandItemEntityList) {\r\n          material.sampleDemandItemEntityList = [];\r\n        }\r\n        \r\n        // 为每个物料添加选中的供应商\r\n        for (let j = 0; j < vendorData.length; j++) {\r\n          const vendor = vendorData[j];\r\n          \r\n          // 检查是否已存在相同的供应商\r\n          const isExist = material.sampleDemandVendorEntityList.some(\r\n            item => item.goodsId === material.goodsId && \r\n                   item.vendorId === vendor.soureId\r\n          );\r\n          \r\n          // 如果不存在，则填充供应商数据\r\n          if (!isExist) {\r\n            const vendorItem = {\r\n              demandId: material.demandId,\r\n              demandItemId: material.id,\r\n              goodsId: material.goodsId,\r\n              goodsErpCode: material.goodsErpCode,\r\n              goodsName: material.goodsName,\r\n              vendorId: vendor.soureId,\r\n              vendorCode: vendor.vendorErpCode,\r\n              vendorName: vendor.vendorFullName,\r\n              isAssigned: 0,\r\n              isReturn: 0,\r\n              // returnRemark: '',\r\n              returnCause: '',\r\n              caseDate: null,\r\n              caseStat: null,\r\n              isValid: 1,\r\n              deleteFlag: 0,\r\n            };\r\n            \r\n            // 填充物料明细\r\n            const materialItem = {\r\n              id: material.itemId,\r\n              demandId: material.demandId,\r\n              goodsId: material.goodsId,\r\n              goodsErpCode: material.goodsErpCode,\r\n              goodsName: material.goodsName,\r\n              goodsModel: material.goodsModel,\r\n              vendorId: material.vendorId,\r\n              vendorCode: material.vendorCode,\r\n              vendorName: material.vendorName,\r\n              demandDate: material.demandDate,\r\n              demandQty: material.demandQty,\r\n              saleDeptId: material.saleDeptId,\r\n              saleDeptCode: material.saleDeptCode,\r\n              saleDeptName: material.saleDeptName,\r\n              model: material.model,\r\n              purpose: material.purpose,\r\n              uomId: material.uomId,\r\n              uomCode: material.uomCode,\r\n              uomName: material.uomName,\r\n              caseDate: material.caseDate,\r\n              sourceItemId: material.sourceItemId,\r\n              caseStat: material.caseStat,\r\n              isValid: material.itemIsValid,\r\n              deleteFlag: material.itemDeleteFlag,\r\n              remark: material.item_remark,\r\n              createId: material.itemCreateId,\r\n              creater: material.itemCreater,\r\n              createDate: material.itemCreateDate,\r\n              modifiId: material.itemModifiId,\r\n              modifier: material.itemModifier,\r\n              modifyDate: material.itemModifyDate\r\n\r\n            };\r\n            material.sampleDemandVendorEntityList.push(vendorItem);\r\n            material.sampleDemandItemEntityList.push(materialItem);\r\n            \r\n            // 添加到需要更新的数据中\r\n            if (!updateData.includes(material)) {\r\n              updateData.push(material);\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      // 如果有数据需要更新\r\n      if (updateData.length > 0) {\r\n        this.$confirm('是否保存供应商数据并生成送样通知单?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.saveSampleVendors(updateData);\r\n        }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '已取消操作'\r\n          });\r\n        });\r\n      } else {\r\n        this.$message.error('没有新的供应商数据需要保存');\r\n      }\r\n    },\r\n    saveSampleVendors(materials) {\r\n      this.btnLoading = true;\r\n      const updatePromises = materials.map(material => {\r\n        console.log(\"保存参数为=====================>\", JSON.stringify(material, null, 2));\r\n        return updateSampleDemand(material);\r\n      });\r\n      \r\n      Promise.all(updatePromises)\r\n        .then(() => {\r\n          this.$message({\r\n            message: \"供应商数据保存成功\",\r\n            type: 'success',\r\n            duration: 1500\r\n          });\r\n          \r\n          // 保存成功后，调用批量生成送样通知单的方法\r\n          const sampleIds = materials.map(item => item.id);\r\n          this.batchGenerateSampleNotice(sampleIds);\r\n        })\r\n        .catch(err => {\r\n          this.btnLoading = false;\r\n          this.$message.error('保存失败: ' + (err.message || '未知错误'));\r\n        });\r\n    },\r\n    //批量退回PLM\r\n    batchReturnSample(){\r\n      // 检查是否有选中的数据\r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message.error('请至少选择一条需要退回的物料明细记录');\r\n        return;\r\n      }\r\n      // 检查选中的物料是否存在sourceItemId\r\n      if(this.selectedDatas.some(item => !item.sourceItemId)){\r\n        // this.$message.error(\"存在未关联的物料明细行需求，请先关联PLM物料明细行\");\r\n        this.$message.error(\"存在未关联PLM的物料，只允许退回从PLM下发的物料\");\r\n        return;\r\n      }\r\n      // 检查是否已退回\r\n      if(this.selectedDatas.some(item => item.caseStat == 4)){\r\n        this.$message.error('存在已退回的物料明细记录，请勿重复退回');\r\n        return;\r\n      }\r\n      this.$prompt('请输入退回原因', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputType: 'textarea',\r\n        inputPlaceholder: '请输入退回原因'\r\n      }).then(({ value }) => {\r\n        if (!value) {\r\n          this.$message.error('退回原因不能为空');\r\n          return;\r\n        }\r\n        // 构造请求参数\r\n        this.btnLoading = true;\r\n        const ids = this.selectedDatas.map(item => item.itemId); // 物料明细ID\r\n        const params = {\r\n          ids: ids, // 物料明细ID数组\r\n          returnCause: value // 退回原因\r\n        };\r\n        returnSample(params).then(res => {\r\n          this.$message({\r\n            message: \"退回成功\",\r\n            type: 'success',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.btnLoading = false;\r\n              this.search(); // 刷新列表\r\n            }\r\n          });\r\n        }).catch(err => {\r\n          this.btnLoading = false;\r\n          this.$message.error('退回失败: ' + (err.message || '未知错误'));\r\n        });\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消退回'\r\n        });\r\n      });\r\n    },\r\n    //批量本地结案\r\n    batchLocalClosed(){\r\n      // 检查是否有选中的数据\r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message.error('请至少选择一条需要结案的物料明细记录');\r\n        return;\r\n      }\r\n      // 检查选中的物料是否存在sourceItemId\r\n      if(this.selectedDatas.some(item => item.sourceItemId)){\r\n        this.$message.error(\"存在已关联PLM的物料，不允许本地结案\");\r\n        return;\r\n      }\r\n      // 检查是否已结案\r\n      if(this.selectedDatas.some(item => item.caseStat == 5)){\r\n        this.$message.error('存在已结案的物料明细记录，请勿重复结案');\r\n        return;\r\n      }\r\n      this.btnLoading = true;\r\n      const ids = this.selectedDatas.map(item => item.itemId); // 物料明细ID\r\n      this.$confirm(`确认结案选中的${this.selectedDatas.length}条记录？`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        localClosed(ids).then(res => {\r\n          this.$message({\r\n            message: \"结案成功\",\r\n            type: 'success',\r\n            duration: 1500,\r\n            onClose: () => {\r\n              this.btnLoading = false;\r\n              this.search(); // 刷新列表\r\n            }\r\n          });\r\n        }).catch(err => {\r\n          this.btnLoading = false;\r\n          this.$message.error('结案失败: ' + (err.message || '未知错误'));\r\n        });\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消结案'\r\n        });\r\n      });\r\n    },\r\n    // 打开采购员选择弹窗\r\n    openBuyerDialog() {\r\n      if (this.selectedDatas.length === 0) {\r\n        this.$message.error('请至少选择一条物料明细记录');\r\n        return;\r\n      }\r\n      \r\n      // 检查是否已退回\r\n      if(this.selectedDatas.some(item => item.caseStat == 4)){\r\n        this.$message.error('存在已退回的物料明细记录，不允许更换采购员');\r\n        return;\r\n      }\r\n      \r\n      this.$nextTick(() => {\r\n        this.$refs.userProp.init();\r\n      });\r\n    },\r\n    \r\n    // 处理用户选择结果\r\n    userSelect(userData) {\r\n      if (!userData || userData.length === 0) {\r\n        this.$message.error('未选择采购员');\r\n        return;\r\n      }\r\n      \r\n      const selectedUser = userData[0];\r\n      console.log('选择的采购员:', selectedUser);\r\n      \r\n      // 弹出确认框\r\n      this.$confirm(`确认将选中的${this.selectedDatas.length}条记录的采购员更换为 ${selectedUser.userName}?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.updateBuyer(selectedUser);\r\n      }).catch(() => {\r\n        this.$message({\r\n          type: 'info',\r\n          message: '已取消更换采购员'\r\n        });\r\n      });\r\n    },\r\n    \r\n    // 更新采购员信息\r\n    updateBuyer(selectedUser) {\r\n      this.btnLoading = true;\r\n      \r\n      // 获取选中记录的ID列表\r\n      const ids = this.selectedDatas.map(item => item.itemId); // 物料明细行ID\r\n      \r\n      // 构造请求参数\r\n      const params = {\r\n        ids: ids,\r\n        purId: selectedUser.id,\r\n        purCode: selectedUser.userCode,\r\n        purName: selectedUser.userName\r\n      };\r\n      \r\n      batchUpdateBuyer(params).then(res => {\r\n        this.$message({\r\n          message: \"采购员更新成功\",\r\n          type: 'success',\r\n          duration: 1500,\r\n          onClose: () => {\r\n            this.btnLoading = false;\r\n            this.search(); // 刷新列表\r\n          }\r\n        });\r\n      }).catch(err => {\r\n        this.btnLoading = false;\r\n        this.$message.error('采购员更新失败: ' + (err.message || '未知错误'));\r\n      });\r\n    },\r\n  }\r\n}\r\n", null]}