{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\index.vue?vue&type=template&id=89d60164&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\base\\sample\\tenant\\index.vue", "mtime": 1754403824838}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1683164318812}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"DIAN-common-layout DIAN-flex-main\" }, [\n    _c(\n      \"div\",\n      { staticClass: \"DIAN-common-layout-center\" },\n      [\n        _c(\n          \"el-row\",\n          { staticClass: \"DIAN-common-search-box\", attrs: { gutter: 16 } },\n          [\n            _c(\n              \"el-form\",\n              {\n                nativeOn: {\n                  submit: function ($event) {\n                    $event.preventDefault()\n                  },\n                },\n              },\n              [\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: {\n                        placeholder: \"请输入PLM打样单号\",\n                        clearable: \"\",\n                      },\n                      model: {\n                        value: _vm.queryParam.sourceNo,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"sourceNo\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.sourceNo\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: { placeholder: \"请输入送样单号\", clearable: \"\" },\n                      model: {\n                        value: _vm.queryParam.sampleNo,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"sampleNo\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.sampleNo\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: {\n                        placeholder: \"请输入供应商编码/名称\",\n                        clearable: \"\",\n                      },\n                      model: {\n                        value: _vm.queryParam.vendor,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"vendor\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.vendor\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: {\n                        placeholder: \"请输入物料编码/名称/型号\",\n                        clearable: \"\",\n                      },\n                      model: {\n                        value: _vm.queryParam.goods,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"goods\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.goods\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\"el-input\", {\n                      attrs: { placeholder: \"请输入采购员名称\", clearable: \"\" },\n                      model: {\n                        value: _vm.queryParam.pur,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.queryParam,\n                            \"pur\",\n                            typeof $$v === \"string\" ? $$v.trim() : $$v\n                          )\n                        },\n                        expression: \"queryParam.pur\",\n                      },\n                    }),\n                  ],\n                  1\n                ),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 4 } },\n                  [\n                    _c(\n                      \"el-form-item\",\n                      [\n                        _c(\n                          \"el-select\",\n                          {\n                            attrs: { placeholder: \"单据状态\", clearable: \"\" },\n                            model: {\n                              value: _vm.queryParam.sampleStat,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.queryParam, \"sampleStat\", $$v)\n                              },\n                              expression: \"queryParam.sampleStat\",\n                            },\n                          },\n                          _vm._l(_vm.sampleStatOptions, function (item) {\n                            return _c(\"el-option\", {\n                              key: item.key,\n                              attrs: { label: item.value, value: item.key },\n                            })\n                          }),\n                          1\n                        ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n                _vm.showAll\n                  ? [\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 3 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\"el-input\", {\n                                attrs: {\n                                  placeholder: \"采购组织编码/名称\",\n                                  clearable: \"\",\n                                },\n                                model: {\n                                  value: _vm.queryParam.dept,\n                                  callback: function ($$v) {\n                                    _vm.$set(\n                                      _vm.queryParam,\n                                      \"dept\",\n                                      typeof $$v === \"string\" ? $$v.trim() : $$v\n                                    )\n                                  },\n                                  expression: \"queryParam.dept\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 3 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\n                                \"el-select\",\n                                {\n                                  attrs: {\n                                    placeholder: \"请选择单据类型\",\n                                    clearable: \"\",\n                                  },\n                                  model: {\n                                    value: _vm.queryParam.demandClassType,\n                                    callback: function ($$v) {\n                                      _vm.$set(\n                                        _vm.queryParam,\n                                        \"demandClassType\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"queryParam.demandClassType\",\n                                  },\n                                },\n                                _vm._l(_vm.demandTypeOption, function (item) {\n                                  return _c(\"el-option\", {\n                                    key: item.key,\n                                    attrs: {\n                                      label: item.value,\n                                      value: item.key,\n                                    },\n                                  })\n                                }),\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 6 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\"el-date-picker\", {\n                                attrs: {\n                                  type: \"daterange\",\n                                  placeholder: \"请选择要求送样日期\",\n                                  \"range-separator\": \"至\",\n                                  \"start-placeholder\": \"（送样）开始日期\",\n                                  \"end-placeholder\": \"（送样）结束日期\",\n                                },\n                                model: {\n                                  value: _vm.sampleDates,\n                                  callback: function ($$v) {\n                                    _vm.sampleDates = $$v\n                                  },\n                                  expression: \"sampleDates\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 6 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\"el-date-picker\", {\n                                attrs: {\n                                  type: \"daterange\",\n                                  placeholder: \"请选择需求日期\",\n                                  \"range-separator\": \"至\",\n                                  \"start-placeholder\": \"（需求）开始日期\",\n                                  \"end-placeholder\": \"（需求）结束日期\",\n                                },\n                                model: {\n                                  value: _vm.demandDates,\n                                  callback: function ($$v) {\n                                    _vm.demandDates = $$v\n                                  },\n                                  expression: \"demandDates\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 6 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\"el-date-picker\", {\n                                attrs: {\n                                  type: \"daterange\",\n                                  placeholder: \"请选择回复日期\",\n                                  \"range-separator\": \"至\",\n                                  \"start-placeholder\": \"（回复）开始日期\",\n                                  \"end-placeholder\": \"（回复）结束日期\",\n                                },\n                                model: {\n                                  value: _vm.replyDates,\n                                  callback: function ($$v) {\n                                    _vm.replyDates = $$v\n                                  },\n                                  expression: \"replyDates\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ]\n                  : _vm._e(),\n                _c(\n                  \"el-col\",\n                  { attrs: { span: 6 } },\n                  [\n                    _c(\n                      \"el-form-item\",\n                      [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"primary\", icon: \"el-icon-search\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.search()\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.$t(\"common.search\")) +\n                                \"\\n            \"\n                            ),\n                          ]\n                        ),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { icon: \"el-icon-refresh-right\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.reset()\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.$t(\"common.reset\")) +\n                                \"\\n            \"\n                            ),\n                          ]\n                        ),\n                        !_vm.showAll\n                          ? _c(\n                              \"el-button\",\n                              {\n                                attrs: {\n                                  type: \"text\",\n                                  icon: \"el-icon-arrow-down\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    _vm.showAll = true\n                                  },\n                                },\n                              },\n                              [_vm._v(\"展开\\n            \")]\n                            )\n                          : _c(\n                              \"el-button\",\n                              {\n                                attrs: {\n                                  type: \"text\",\n                                  icon: \"el-icon-arrow-up\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    _vm.showAll = false\n                                  },\n                                },\n                              },\n                              [_vm._v(\"\\n              收起\\n            \")]\n                            ),\n                      ],\n                      1\n                    ),\n                  ],\n                  1\n                ),\n              ],\n              2\n            ),\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          { staticClass: \"DIAN-common-layout-main DIAN-flex-main\" },\n          [\n            _c(\"div\", { staticClass: \"DIAN-common-head\" }, [\n              _c(\"div\"),\n              _c(\n                \"div\",\n                { staticClass: \"DIAN-common-head-right\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"has-per\",\n                          rawName: \"v-has-per\",\n                          value: \"base:sample:confirmRecSample\",\n                          expression: \"'base:sample:confirmRecSample'\",\n                        },\n                      ],\n                      attrs: {\n                        type: \"primary\",\n                        loading: _vm.btnLoading,\n                        icon: \"el-icon-check\",\n                        disabled: _vm.selectedDatas.length === 0,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchConfirmRecSample()\n                        },\n                      },\n                    },\n                    [_vm._v(\"批量确认收样\")]\n                  ),\n                  _c(\n                    \"el-tooltip\",\n                    {\n                      attrs: {\n                        effect: \"dark\",\n                        content: _vm.$t(\"common.refresh\"),\n                        placement: \"top\",\n                      },\n                    },\n                    [\n                      _c(\"el-link\", {\n                        attrs: {\n                          icon: \"icon-ym icon-ym-Refresh DIAN-common-head-icon\",\n                          underline: false,\n                        },\n                        on: {\n                          click: function ($event) {\n                            return _vm.search()\n                          },\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\"d-screen-full\"),\n                ],\n                1\n              ),\n            ]),\n            _c(\n              \"d-table\",\n              {\n                directives: [\n                  {\n                    name: \"loading\",\n                    rawName: \"v-loading\",\n                    value: _vm.listLoading,\n                    expression: \"listLoading\",\n                  },\n                ],\n                attrs: { data: _vm.list, hasNO: false, hasC: true },\n                on: { \"selection-change\": _vm.handleSelectionChange },\n              },\n              [\n                _c(\"el-table-column\", {\n                  attrs: {\n                    type: \"index\",\n                    width: \"50\",\n                    label: \"序号\",\n                    align: \"center\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"deptName\",\n                    label: \"采购组织\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"115\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"applicant\",\n                    label: \"申请人\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"115\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"applyDeptName\",\n                    label: \"申请部门\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"115\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"applyDate\",\n                    label: \"申请日期\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"125\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"sourceNo\",\n                    label: \"PLM打样单号\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          scope.row.sourceNo\n                            ? _c(\n                                \"el-link\",\n                                {\n                                  attrs: { type: \"primary\", underline: false },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.goToSampleDemand(\n                                        scope.row.sourceNo\n                                      )\n                                    },\n                                  },\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n              \" +\n                                      _vm._s(scope.row.sourceNo) +\n                                      \"\\n            \"\n                                  ),\n                                ]\n                              )\n                            : _c(\"span\", [_vm._v(\"-\")]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"sampleNo\",\n                    label: \"送样单号\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"125\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"caseDate\",\n                    label: \"送样日期\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"125\",\n                    align: \"center\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(_vm._s(_vm._f(\"date\")(scope.row.caseDate))),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"vendorCode\",\n                    label: \"供应商编码\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"vendorName\",\n                    label: \"供应商名称\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandClassType\",\n                    label: \"单据类型\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"115\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(\n                                _vm._f(\"commonEnumsTurn\")(\n                                  scope.row.demandClassType,\n                                  \"base.DemandClassTypeEnum\"\n                                )\n                              )\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"caseStat\",\n                    label: \"单据状态\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"115\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(\n                                _vm._f(\"commonEnumsTurn\")(\n                                  scope.row.caseStat,\n                                  \"base.SampleEnums\"\n                                )\n                              )\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsErpCode\",\n                    label: \"物料编码\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsName\",\n                    label: \"物料名称\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsModel\",\n                    label: \"物料型号\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"120\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandQty\",\n                    label: \"需求数量\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"demandDate\",\n                    label: \"需求日期\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(_vm._f(\"date\")(scope.row.demandDate))\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"replyQuantity\",\n                    label: \"回复数量\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"replyDeliveryDate\",\n                    label: \"回复交期\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(\n                                _vm._f(\"date\")(scope.row.replyDeliveryDate)\n                              )\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"replyState\",\n                    label: \"答交状态\",\n                    align: \"center\",\n                    width: \"150\",\n                  },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(\n                                _vm._f(\"commonEnumsTurn\")(\n                                  scope.row.replyState,\n                                  \"base.ReplyStateEnum\"\n                                )\n                              )\n                            ),\n                          ]),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"purName\",\n                    label: \"采购员\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"goodsNum\",\n                    label: \"送样数量\",\n                    align: \"center\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"100\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"vendorRemark\",\n                    label: \"供应商说明\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"200\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"creater\",\n                    label: \"创建人\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"createDate\",\n                    label: \"创建时间\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"modifier\",\n                    label: \"更新人\",\n                    \"show-overflow-tooltip\": \"\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: {\n                    prop: \"modifyDate\",\n                    label: \"更新时间\",\n                    width: \"150\",\n                    sortable: \"\",\n                  },\n                }),\n                _c(\"el-table-column\", {\n                  attrs: { label: \"操作\", fixed: \"right\", width: \"100\" },\n                  scopedSlots: _vm._u([\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return [\n                          _c(\n                            \"el-button\",\n                            {\n                              directives: [\n                                {\n                                  name: \"has-per\",\n                                  rawName: \"v-has-per\",\n                                  value: \"base:sample:info\",\n                                  expression: \"'base:sample:info'\",\n                                },\n                              ],\n                              attrs: { size: \"mini\", type: \"text\" },\n                              on: {\n                                click: function ($event) {\n                                  return _vm.addOrUpdateHandle(scope.row.id)\n                                },\n                              },\n                            },\n                            [\n                              scope.row.sampleStat == 1\n                                ? _c(\"span\", [\n                                    _vm._v(\n                                      \"\\n                \" +\n                                        _vm._s(_vm.$t(\"common.editBtn\")) +\n                                        \"\\n              \"\n                                    ),\n                                  ])\n                                : _vm._e(),\n                              scope.row.sampleStat > 1\n                                ? _c(\"span\", [\n                                    _vm._v(\n                                      \"\\n                \" +\n                                        _vm._s(_vm.$t(\"common.lookBtn\")) +\n                                        \"\\n              \"\n                                    ),\n                                  ])\n                                : _vm._e(),\n                            ]\n                          ),\n                        ]\n                      },\n                    },\n                  ]),\n                }),\n              ],\n              1\n            ),\n            _c(\"d-pagination\", {\n              attrs: {\n                total: _vm.total,\n                page: _vm.queryParam.page,\n                limit: _vm.queryParam.limit,\n              },\n              on: {\n                \"update:page\": function ($event) {\n                  return _vm.$set(_vm.queryParam, \"page\", $event)\n                },\n                \"update:limit\": function ($event) {\n                  return _vm.$set(_vm.queryParam, \"limit\", $event)\n                },\n                pagination: _vm.initData,\n              },\n            }),\n          ],\n          1\n        ),\n        _c(\"Form\", {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.detailVisible,\n              expression: \"detailVisible\",\n            },\n          ],\n          ref: \"detail\",\n          on: { callRefreshList: _vm.callRefreshList },\n        }),\n        _c(\"QualityForm\", {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.qualityVisible,\n              expression: \"qualityVisible\",\n            },\n          ],\n          ref: \"quality\",\n          on: { callRefreshList: _vm.callRefreshList },\n        }),\n      ],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}