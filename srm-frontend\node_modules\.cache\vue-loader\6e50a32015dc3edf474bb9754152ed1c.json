{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue?vue&type=style&index=0&id=a361ec26&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1754405086353}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\css-loader\\index.js", "mtime": 1683164317321}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1683164318717}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1683164317478}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1683164318847}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.sale {\r\n  background: #fff;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n  .sale_title {\r\n    padding-top: 16px;\r\n    padding-left: 22px;\r\n    span {\r\n      &:first-child {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #333;\r\n      }\r\n      &:nth-child(2) {\r\n        color: #999;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n  .sale_items {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    padding: 34px 0;\r\n    li {\r\n      display: flex;\r\n      flex-direction: column;\r\n      text-align: center;\r\n      .nums {\r\n        font-size: 26px !important;\r\n        padding-top: 13px;\r\n        padding-bottom: 9px;\r\n        color: #1890ff;\r\n        font-weight: 600;\r\n        span {\r\n          font-size: 26px !important;\r\n          padding-top: 13px;\r\n          padding-bottom: 9px;\r\n          color: #1890ff !important;\r\n          font-weight: 600;\r\n        }\r\n      }\r\n      .lastnums {\r\n        font-size: 26px !important;\r\n        padding-top: 13px;\r\n        padding-bottom: 9px;\r\n        font-weight: 600;\r\n        color: #333;\r\n      }\r\n      &:first-child {\r\n        span:last-child {\r\n          color: #08b41f;\r\n        }\r\n      }\r\n      &:nth-child(4) {\r\n        span {\r\n          &:last-child {\r\n            color: #08b41f;\r\n          }\r\n        }\r\n      }\r\n      &:nth-child(2) {\r\n        span {\r\n          &:last-child {\r\n            color: #b40808;\r\n          }\r\n        }\r\n      }\r\n      &:nth-child(3) {\r\n        span {\r\n          &:last-child {\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n      &:last-child {\r\n        span {\r\n          &:last-child {\r\n            color: #000;\r\n          }\r\n        }\r\n      }\r\n      span {\r\n        &:nth-child(2) {\r\n          font-size: 26px !important;\r\n          padding-top: 13px;\r\n          padding-bottom: 9px;\r\n          color: #1890ff;\r\n          font-weight: 600;\r\n        }\r\n        &:nth-child(3) {\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.notice {\r\n  padding-bottom: 20px;\r\n  .notice_title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    height: 60px;\r\n    padding: 0 25px;\r\n    .title_left {\r\n      display: flex;\r\n      align-items: center;\r\n      span {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #333;\r\n      }\r\n    }\r\n    .title_right {\r\n      font-size: 12px;\r\n      color: #999;\r\n      line-height: 30px;\r\n    }\r\n  }\r\n  .notice_right {\r\n    background: #fff;\r\n    height: 400px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n    .no_right_massage {\r\n      li {\r\n        display: flex;\r\n        background: #fff;\r\n        border: 1;\r\n        height: 170px;\r\n        border-top: 1px solid #f2f2f5;\r\n        .right_item {\r\n          width: 50%;\r\n          padding: 16px 20px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-between;\r\n          .right-top {\r\n            font-size: 14px;\r\n            display: flex;\r\n            align-items: center;\r\n            img {\r\n              margin-right: 10px;\r\n            }\r\n          }\r\n          p {\r\n            font-size: 14px;\r\n            line-height: 22px;\r\n            padding: 20px 0;\r\n            color: #999;\r\n            display: -webkit-box;\r\n            line-clamp: 3;\r\n            -webkit-line-clamp: 3;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            /*! autoprefixer: off */\r\n            -webkit-box-orient: vertical;\r\n            /* autoprefixer: on */\r\n          }\r\n          .bt {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            font-size: 12px;\r\n            color: #999;\r\n          }\r\n        }\r\n        .linet {\r\n          border-left: 1px solid #f2f2f5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .notice_left {\r\n    background: #fff;\r\n    height: 400px;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n    .no_left_massage {\r\n      height: 340px;\r\n      padding: 10px 20px;\r\n      border-top: 1px solid #f2f2f5;\r\n      ul {\r\n        li {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          font-size: 14px;\r\n          color: #303133;\r\n          padding: 10px 0;\r\n          cursor: pointer;\r\n          p {\r\n            width: calc(100% - 80px);\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n          }\r\n          .time {\r\n            width: 80px;\r\n            display: inline-block;\r\n            text-align: right;\r\n            color: #999999;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.componey {\r\n  font-size: 14px;\r\n  text-align: center;\r\n  color: #999;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.dashboard-editor-container {\r\n  background-color: #ebeef5;\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: hidden;\r\n\r\n  .github-corner {\r\n    position: absolute;\r\n    top: 0px;\r\n    border: 0;\r\n    right: 0;\r\n  }\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 20px;\r\n  }\r\n}\r\n.dateSelect {\r\n  position: absolute;\r\n  right: 67px;\r\n  top: 38px;\r\n  z-index: 100;\r\n  .el-date-editor {\r\n    width: 160px;\r\n  }\r\n}\r\n@media (max-width: 1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n", null]}