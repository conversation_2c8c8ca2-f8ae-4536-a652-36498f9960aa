2025-08-05 09:04:24.267 [main] INFO  com.dian.SimpleAppliction - Starting SimpleAppliction on lf with PID 16716 (E:\Desktop\srm\srm-backend\5000-cloud-simple-start\target\classes started by l'f in E:\Desktop\srm\srm-backend)
2025-08-05 09:04:24.314 [main] INFO  com.dian.SimpleAppliction - The following profiles are active: local
2025-08-05 09:04:47.183 [main] INFO  com.dian.config.ShiroConfig - 忽略请求地址:[/sys/ssoLogin, /sys/user/login/updatePwd, /sys/wechatLogin, /sys/wxUser/security, /sysLog/logging, /sys/captcha.jpg, /base/vendor/client/**, /im/ws/**]
2025-08-05 09:04:48.068 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7003"]
2025-08-05 09:04:48.068 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 09:04:48.068 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.33]
2025-08-05 09:04:48.068 [main] INFO  org.apache.catalina.core.AprLifecycleListener - An older version [1.2.16] of the APR based Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.23]
2025-08-05 09:04:48.068 [main] INFO  org.apache.catalina.core.AprLifecycleListener - Loaded APR based Apache Tomcat Native library [1.2.16] using APR version [1.6.3].
2025-08-05 09:04:48.083 [main] INFO  org.apache.catalina.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true].
2025-08-05 09:04:48.084 [main] INFO  org.apache.catalina.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-08-05 09:04:49.169 [main] INFO  org.apache.catalina.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.0.2m  2 Nov 2017]
2025-08-05 09:04:49.592 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-05 09:04:50.332 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-05 09:04:52.909 [main] INFO  p6spy - select '1' from dual
2025-08-05 09:04:53.123 [main] INFO  p6spy - select '1' from dual
2025-08-05 09:04:53.280 [main] INFO  p6spy - select '1' from dual
2025-08-05 09:04:53.424 [main] INFO  p6spy - select '1' from dual
2025-08-05 09:04:53.587 [main] INFO  p6spy - select '1' from dual
2025-08-05 09:04:53.616 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-05 09:04:54.376 [main] INFO  com.dian.config.WebSocketConfig - WebSocket 初始化成功
2025-08-05 09:04:54.411 [main] INFO  com.dian.config.XxlJobConfig - >>>>>>>>>>> xxl-job config init.
2025-08-05 09:06:11.353 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullGoodsByErpHandlerOfDay, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@258274cb[class com.dian.job.base.GoodsJob$$EnhancerBySpringCGLIB$$1d0f0006#pullGoodsByErpHandlerOfDay]
2025-08-05 09:06:11.378 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:batchUpdateVendorIsValidByEffEndDate, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5084cf1c[class com.dian.job.base.VendorJob$$EnhancerBySpringCGLIB$$56370b62#batchUpdateVendorIsValidByEffEndDate]
2025-08-05 09:06:11.393 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:timedPushDeliveryToWmsJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6a82d4b4[class com.dian.job.dm.DmJob$$EnhancerBySpringCGLIB$$c2234834#timedPushDeliveryToWmsJob]
2025-08-05 09:06:11.393 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:timedPushInspectionToWmsJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@492a54b4[class com.dian.job.dm.DmJob$$EnhancerBySpringCGLIB$$c2234834#timedPushInspectionToWmsJob]
2025-08-05 09:06:11.422 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullReceiptByErpHandlerOfDay, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@470bef04[class com.dian.job.dm.ReceiptDocJob$$EnhancerBySpringCGLIB$$737a9142#pullReceiptByErpHandlerOfDay]
2025-08-05 09:06:11.438 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullReturnByErpHandlerOfDay, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d34a88a[class com.dian.job.dm.ReturnDocJob$$EnhancerBySpringCGLIB$$9e759aa8#pullReturnByErpHandlerOfDay]
2025-08-05 09:06:11.454 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:quaAbnormalConfirm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@38485cee[class com.dian.job.dm.quaAbnormalJob$$EnhancerBySpringCGLIB$$938cb851#automaticConfirm]
2025-08-05 09:06:11.468 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:timedPushNoOpeningDataToSapJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@b213994[class com.dian.job.im.ImJob$$EnhancerBySpringCGLIB$$d11cf5d4#timedPushNoOpeningDataToSap]
2025-08-05 09:06:11.469 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:imBidOpen, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@70d2a981[class com.dian.job.im.ImJob$$EnhancerBySpringCGLIB$$d11cf5d4#imBidOpenJobHandler]
2025-08-05 09:06:11.469 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:timedPushOpeningPriceDataToSapJob, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@21820cd1[class com.dian.job.im.ImJob$$EnhancerBySpringCGLIB$$d11cf5d4#timedPushOpeningPriceDataToSapJob]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullUpdateOrderByErpHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@43c16e17[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullUpdateOrderByErpHandler]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullUpdateSubPpBomByErpHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6e5091b0[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullUpdateSubPpBomByErpHandler]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullAddSubPpBomByErpHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6fec6b75[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullAddSubPpBomByErpHandler]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullClosedOrderByErpHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@74cca90[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullClosedOrderByErpHandler]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullChangeOrderByErpHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4fb5adb8[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullChangeOrderByErpHandler]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullOrderByErpHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@37e44608[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullOrderByErpHandler]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:pullOrderByErpHandlerOfYearAndMonth, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4afd8f16[class com.dian.job.order.OrderJob$$EnhancerBySpringCGLIB$$e2277fa7#pullOrderByErpHandlerOfYearAndMonth]
2025-08-05 09:06:11.485 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:timingGenerateNextMonthAppraisal, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@12c53d97[class com.dian.job.pr.PrJob$$EnhancerBySpringCGLIB$$b9f7d414#timingGenerateNextMonthAppraisal]
2025-08-05 09:06:11.517 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:orderJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7ef60643[class com.dian.job.test.AppJob$$EnhancerBySpringCGLIB$$4150ef0f#demoJobHandler]
2025-08-05 09:06:11.517 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:subcontractingJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1fd49ed3[class com.dian.job.test.AppJob$$EnhancerBySpringCGLIB$$4150ef0f#subcontractingJobHandler]
2025-08-05 09:06:11.532 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job register jobhandler success, name:myTestJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a987a5d[class com.dian.job.test.MyTestJob$$EnhancerBySpringCGLIB$$8c9fcf43#myTestJobHandler]
2025-08-05 09:06:25.714 [main] INFO  org.apache.activemq.broker.BrokerService - Loaded the Bouncy Castle security provider at position: 11
2025-08-05 09:06:26.082 [JMX connector] INFO  org.apache.activemq.broker.jmx.ManagementContext - JMX consoles can connect to service:jmx:rmi:///jndi/rmi://localhost:1099/jmxrmi
2025-08-05 09:06:26.086 [main] INFO  org.apache.activemq.broker.BrokerService - Using Persistence Adapter: MemoryPersistenceAdapter
2025-08-05 09:06:27.036 [main] INFO  org.apache.activemq.broker.BrokerService - Apache ActiveMQ 5.15.12 (localhost, ID:lf-10269-1754355986265-0:1) is starting
2025-08-05 09:06:27.202 [main] INFO  org.apache.activemq.broker.BrokerService - Apache ActiveMQ 5.15.12 (localhost, ID:lf-10269-1754355986265-0:1) started
2025-08-05 09:06:27.202 [main] INFO  org.apache.activemq.broker.BrokerService - For help or more information please see: http://activemq.apache.org
2025-08-05 09:06:27.576 [Thread-213] INFO  com.xxl.job.core.server.EmbedServer - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
2025-08-05 09:06:27.815 [main] INFO  org.apache.activemq.broker.TransportConnector - Connector vm://localhost started
2025-08-05 09:06:29.573 [main] INFO  com.gitee.easyopen.register.ApiRegister - ******** 开始注册操作 ********
2025-08-05 09:06:29.581 [main] INFO  com.gitee.easyopen.message.ErrorFactory - 加载错误码国际化资源：i18n/isv/goods_error,i18n/open/error,i18n/isv/error
2025-08-05 09:06:29.621 [main] INFO  com.gitee.easyopen.register.ApiRegister - 开始注册Api接口...
2025-08-05 09:06:29.735 [main] INFO  com.gitee.easyopen.register.ApiRegister - 注册Api接口完毕，共24个接口
2025-08-05 09:06:29.743 [main] INFO  com.gitee.easyopen.register.ApiRegister - 生成接口文档
2025-08-05 09:06:29.765 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:06:31.809 [main] INFO  com.gitee.easyopen.register.ApiRegister - 执行Api注册回调
2025-08-05 09:06:32.399 [nioEventLoopGroup-4-1] INFO  com.gitee.easyopen.config.NettyClientHandler - 连接配置中心成功
2025-08-05 09:06:33.113 [nioEventLoopGroup-4-1] INFO  com.gitee.easyopen.config.NettyClientHandler - 同步本地API到配置中心
2025-08-05 09:06:33.121 [nioEventLoopGroup-4-1] INFO  com.gitee.easyopen.config.NettyClientHandler - ----开始同步配置中心数据----
2025-08-05 09:06:33.121 [nioEventLoopGroup-4-1] INFO  com.gitee.easyopen.config.NettyClientHandler - 下载秘钥配置
2025-08-05 09:06:33.121 [nioEventLoopGroup-4-1] INFO  com.gitee.easyopen.config.NettyClientHandler - 下载权限配置
2025-08-05 09:06:33.122 [nioEventLoopGroup-4-1] INFO  com.gitee.easyopen.config.NettyClientHandler - 下载限流配置
2025-08-05 09:06:33.784 [nioEventLoopGroup-4-1] INFO  c.g.e.c.processor.DownloadSecretConfigProcessor - 秘钥配置下载成功，保存路径：E:\Desktop\srm\srm-backend\local-config\gve-srm-local-secret.json
2025-08-05 09:06:33.901 [nioEventLoopGroup-4-1] INFO  c.g.e.c.p.DownloadPermissionConfigProcessor - 权限配置下载成功，保存路径：E:\Desktop\srm\srm-backend\local-config\gve-srm-local-permission.json
2025-08-05 09:06:33.901 [nioEventLoopGroup-4-1] INFO  c.g.e.c.processor.DownloadLimitConfigProcessor - 限流配置下载成功，保存路径：E:\Desktop\srm\srm-backend\local-config\gve-srm-local-limit.json
2025-08-05 09:06:33.918 [main] INFO  com.gitee.easyopen.register.ApiRegister - 执行Api注册回调完毕
2025-08-05 09:06:33.918 [main] INFO  com.gitee.easyopen.register.ApiRegister - ******** 注册操作结束 ********
2025-08-05 09:06:33.935 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7003"]
2025-08-05 09:06:34.035 [main] INFO  com.dian.SimpleAppliction - Started SimpleAppliction in 131.53 seconds (JVM running for 134.741)
2025-08-05 09:06:49.804 [main] INFO  p6spy - select '1' from dual
2025-08-05 09:06:50.628 [main] INFO  p6spy - SELECT id, tenant_p_id, tenant_id, enquiry_code, enquiry_name, enquiry_explain, bid_plan_id, bid_plan_code, enquiry_rule, dept_name, tenant_name, remark, enquiry_type, goods_type, enquiry_mode, enquiry_stat, enquiry_way, ask_date, complete_date, order_date, is_urgent, delete_flag, wf_id, wf_status, version_num, dep_name, pur_org_name, info_type, werks, plans, allow_bid_count, rate_code, bstae, auto_source, tenant_sees_rank_and_price, vendor_sees_rank_and_price, vendor_sees_final_rank_price, is_part_update, enquiry_round, expand_remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM im_enquiry WHERE (enquiry_mode = 3 AND enquiry_stat = 2 AND ask_date > '2025-08-05 09:06:48')
2025-08-05 09:06:50.755 [main] INFO  p6spy - SELECT id, tenant_p_id, tenant_id, enquiry_code, enquiry_name, enquiry_explain, bid_plan_id, bid_plan_code, enquiry_rule, dept_name, tenant_name, remark, enquiry_type, goods_type, enquiry_mode, enquiry_stat, enquiry_way, ask_date, complete_date, order_date, is_urgent, delete_flag, wf_id, wf_status, version_num, dep_name, pur_org_name, info_type, werks, plans, allow_bid_count, rate_code, bstae, auto_source, tenant_sees_rank_and_price, vendor_sees_rank_and_price, vendor_sees_final_rank_price, is_part_update, enquiry_round, expand_remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM im_enquiry WHERE (enquiry_mode = 3 AND (enquiry_stat = 2 OR enquiry_stat = 3) AND complete_date > '2025-08-05 09:06:50')
2025-08-05 09:06:50.755 [main] INFO  com.dian.task.TaskCommandRunnerImpl - 任务清单:0
2025-08-05 09:06:50.761 [main] INFO  com.dian.SimpleAppliction - <===========[simple-start]单体启动服务启动完成！运行环境:[local] IP:[*************] PORT:[7003]===========>
2025-08-05 09:06:51.322 [RMI TCP Connection(10)-*************] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 09:06:53.096 [RMI TCP Connection(13)-*************] INFO  p6spy - /* ping */ SELECT 1
2025-08-05 09:07:01.781 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:07:33.801 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:08:05.852 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:08:37.927 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:09:09.956 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:09:42.005 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:10:14.070 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:10:46.130 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:11:18.187 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:11:50.233 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:12:22.252 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:12:54.304 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:13:26.338 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:13:46.139 [http-nio-7003-exec-4] INFO  p6spy - <0><1952538484100780032> select '1' from dual
2025-08-05 09:13:46.183 [http-nio-7003-exec-4] INFO  p6spy - <0><1952538484100780032> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
2025-08-05 09:13:46.269 [http-nio-7003-exec-4] INFO  p6spy - <0><1952538484100780032> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
2025-08-05 09:13:46.369 [http-nio-7003-exec-4] INFO  p6spy - <0><1952538484100780032> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
2025-08-05 09:13:46.753 [http-nio-7003-exec-4] INFO  com.dian.modules.sys.redis.SysRedisServer - <0><1952538484100780032> 过期时间:43199
2025-08-05 09:13:47.454 [http-nio-7003-exec-6] INFO  p6spy - <0><1952538491264651264> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
2025-08-05 09:13:47.487 [http-nio-7003-exec-6] INFO  p6spy - <0><1952538491264651264> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
2025-08-05 09:13:48.112 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
2025-08-05 09:13:48.131 [http-nio-7003-exec-2] INFO  p6spy - <0><1952538493563129856> select '1' from dual
2025-08-05 09:13:48.131 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
2025-08-05 09:13:48.131 [http-nio-7003-exec-2] INFO  p6spy - <0><1952538493563129856> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
2025-08-05 09:13:48.147 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:13:48.165 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
2025-08-05 09:13:48.179 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:13:48.198 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
2025-08-05 09:13:48.198 [http-nio-7003-exec-3] INFO  p6spy - <0><1952538494536208384> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
2025-08-05 09:13:48.214 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:13:48.214 [http-nio-7003-exec-3] INFO  p6spy - <0><1952538494536208384> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
2025-08-05 09:13:48.214 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
2025-08-05 09:13:48.231 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:13:48.248 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
2025-08-05 09:13:48.267 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:13:48.281 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
2025-08-05 09:13:48.299 [http-nio-7003-exec-1] INFO  p6spy - <0><1952538493563129857> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:13:49.098 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
2025-08-05 09:13:49.098 [http-nio-7003-exec-4] INFO  p6spy - <0><1952538497690324992> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
2025-08-05 09:13:49.114 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
2025-08-05 09:13:49.130 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
2025-08-05 09:13:49.176 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
2025-08-05 09:13:49.176 [http-nio-7003-exec-4] INFO  p6spy - <0><1952538497690324992> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 09:13:49.192 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
2025-08-05 09:13:49.208 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
2025-08-05 09:13:49.224 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
2025-08-05 09:13:49.259 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
2025-08-05 09:13:49.275 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
2025-08-05 09:13:49.283 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
2025-08-05 09:13:49.498 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
2025-08-05 09:13:49.515 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
2025-08-05 09:13:49.532 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
2025-08-05 09:13:49.548 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 09:13:49.564 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
2025-08-05 09:13:49.582 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
2025-08-05 09:13:49.598 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
2025-08-05 09:13:49.666 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.681 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.698 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.715 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.732 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.750 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.774 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.783 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.810 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:13:49.878 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
2025-08-05 09:13:49.899 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
2025-08-05 09:13:49.921 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
2025-08-05 09:13:49.940 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
2025-08-05 09:13:49.949 [http-nio-7003-exec-5] INFO  p6spy - <0><1952538497715490816> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
2025-08-05 09:13:58.389 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:14:30.468 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:15:02.532 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:15:34.592 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:16:06.620 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:16:38.666 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:17:10.724 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:17:42.766 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:18:14.813 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:18:46.870 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:19:18.944 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:19:50.989 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:20:23.041 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:20:55.099 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:21:27.153 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:21:59.192 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:22:31.242 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:23:03.298 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:23:35.367 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:24:07.412 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:24:39.483 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:25:11.558 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:25:43.602 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:26:15.679 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:26:47.741 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:27:19.785 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:27:24.727 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541919260921856> select '1' from dual
2025-08-05 09:27:25.242 [http-nio-7003-exec-7] INFO  p6spy - <0><1952541921269993472> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
2025-08-05 09:27:25.306 [http-nio-7003-exec-7] INFO  p6spy - <0><1952541921269993472> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
2025-08-05 09:27:25.941 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
2025-08-05 09:27:25.973 [http-nio-7003-exec-3] INFO  p6spy - <0><1952541924294086658> select '1' from dual
2025-08-05 09:27:25.977 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
2025-08-05 09:27:25.988 [http-nio-7003-exec-3] INFO  p6spy - <0><1952541924294086658> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
2025-08-05 09:27:25.992 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:27:26.018 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
2025-08-05 09:27:26.075 [http-nio-7003-exec-4] INFO  p6spy - <0><1952541924822568960> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
2025-08-05 09:27:26.076 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:27:26.090 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
2025-08-05 09:27:26.091 [http-nio-7003-exec-4] INFO  p6spy - <0><1952541924822568960> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
2025-08-05 09:27:26.103 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:27:26.152 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
2025-08-05 09:27:26.181 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:27:26.195 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
2025-08-05 09:27:26.211 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:27:26.284 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
2025-08-05 09:27:26.299 [http-nio-7003-exec-1] INFO  p6spy - <0><1952541924294086660> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 09:27:26.875 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
2025-08-05 09:27:26.875 [http-nio-7003-exec-5] INFO  p6spy - <0><1952541928198983681> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
2025-08-05 09:27:26.890 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
2025-08-05 09:27:26.890 [http-nio-7003-exec-5] INFO  p6spy - <0><1952541928198983681> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 09:27:26.917 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
2025-08-05 09:27:26.967 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
2025-08-05 09:27:26.978 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
2025-08-05 09:27:26.995 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
2025-08-05 09:27:27.049 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
2025-08-05 09:27:27.062 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
2025-08-05 09:27:27.083 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
2025-08-05 09:27:27.137 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
2025-08-05 09:27:27.238 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
2025-08-05 09:27:27.268 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
2025-08-05 09:27:27.328 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
2025-08-05 09:27:27.347 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 09:27:27.381 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
2025-08-05 09:27:27.411 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
2025-08-05 09:27:27.458 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
2025-08-05 09:27:27.601 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.623 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.681 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.704 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.770 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.789 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.843 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.872 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.915 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:27:27.949 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
2025-08-05 09:27:27.967 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
2025-08-05 09:27:28.024 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
2025-08-05 09:27:28.055 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
2025-08-05 09:27:28.113 [http-nio-7003-exec-6] INFO  p6spy - <0><1952541928198983680> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
2025-08-05 09:27:51.844 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:28:23.890 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:28:55.946 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:29:28.005 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:30:00.052 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:30:32.108 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:31:04.147 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:31:36.225 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:32:08.267 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:32:40.323 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:33:12.381 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:33:44.487 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:34:16.571 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:34:48.630 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:35:20.671 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:35:52.720 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:36:24.762 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:36:56.814 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:37:28.873 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:38:00.990 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:38:33.060 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:39:05.121 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:39:37.184 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:40:09.243 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:40:41.285 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:41:13.340 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:41:45.379 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:42:17.610 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:42:49.737 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:43:21.773 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:43:22.212 [http-nio-7003-exec-7] INFO  p6spy - <0><1952545935365722112> select '1' from dual
2025-08-05 09:43:24.710 [http-nio-7003-exec-9] INFO  p6spy - <0><1952545945780178944> select * from sys_user where user_code = 'SU20250604641_Admin' and is_deleted=0 and is_valid=1
2025-08-05 09:43:24.724 [http-nio-7003-exec-9] INFO  p6spy - <0><1952545945780178944> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
2025-08-05 09:43:24.740 [http-nio-7003-exec-9] INFO  p6spy - <0><1952545945780178944> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
2025-08-05 09:43:24.863 [http-nio-7003-exec-9] INFO  com.dian.modules.sys.redis.SysRedisServer - <0><1952545945780178944> 过期时间:43199
2025-08-05 09:43:25.685 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545948586168320> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
2025-08-05 09:43:25.718 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545948586168320> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
2025-08-05 09:43:26.154 [http-nio-7003-exec-5] INFO  p6spy - <0><1952545951891279876> select '1' from dual
2025-08-05 09:43:26.154 [http-nio-7003-exec-10] INFO  p6spy - <0><1952545951891279873> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 09:43:26.166 [http-nio-7003-exec-5] INFO  p6spy - <0><1952545951891279876> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 09:43:26.167 [http-nio-7003-exec-6] INFO  p6spy - <0><1952545951891279874> select '1' from dual
2025-08-05 09:43:26.197 [http-nio-7003-exec-6] INFO  p6spy - <0><1952545951891279874> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 09:43:26.266 [http-nio-7003-exec-7] INFO  p6spy - <0><1952545952356847616> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND parent_id = 0)
2025-08-05 09:43:26.287 [http-nio-7003-exec-7] INFO  p6spy - <0><1952545952356847616> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND is_valid = 1 AND dept_type = 1)
2025-08-05 09:43:26.705 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
2025-08-05 09:43:26.715 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
2025-08-05 09:43:26.730 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
2025-08-05 09:43:26.736 [http-nio-7003-exec-9] INFO  p6spy - <0><1952545954185564160> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
2025-08-05 09:43:26.741 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
2025-08-05 09:43:26.758 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
2025-08-05 09:43:26.770 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
2025-08-05 09:43:26.771 [http-nio-7003-exec-9] INFO  p6spy - <0><1952545954185564160> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 09:43:26.783 [http-nio-7003-exec-2] INFO  p6spy - <0><1952545954185564161> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
2025-08-05 09:43:26.785 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
2025-08-05 09:43:26.842 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
2025-08-05 09:43:26.865 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
2025-08-05 09:43:26.871 [http-nio-7003-exec-3] INFO  p6spy - <0><1952545954303004672> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25409 AND bvni.is_read = '0'
2025-08-05 09:43:26.876 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
2025-08-05 09:43:26.889 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 09:43:26.906 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
2025-08-05 09:43:26.923 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
2025-08-05 09:43:26.957 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
2025-08-05 09:43:26.989 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.007 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.024 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.043 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.062 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.077 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.094 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.112 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.130 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 09:43:27.151 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
2025-08-05 09:43:27.168 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
2025-08-05 09:43:27.189 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
2025-08-05 09:43:27.205 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
2025-08-05 09:43:27.222 [http-nio-7003-exec-8] INFO  p6spy - <0><1952545954189758464> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
2025-08-05 09:43:30.018 [http-nio-7003-exec-1] INFO  p6spy - <0><1952545967699611648> SELECT COUNT(*) FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905'
2025-08-05 09:43:30.112 [http-nio-7003-exec-1] INFO  p6spy - <0><1952545967699611648> SELECT op.tenant_id, op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name, op.dept_name, op.order_date, op.dep_code, op.bsart, op.pur_name, op.order_flag, op.sync_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.tenant_name, op.currency_name, op.total_amount, op.order_remark, op.publish_date, op.purchasing_group, op.reserved06, op.purchasing_group, opi.seq, opi.item_stat, op.id sale_id, opi.id sale_item_id, op.pur_no sale_no, (opi.order_num - opi.make_num) dev_num, opi.is_close, opi.erp_change_type, opi.goods_id, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model, opi.delivery_stat, opi.order_num, opi.matched_plan_num, opi.make_num, opi.fix_num, opi.wait_num, opi.receive_num, opi.refund_num, opi.ref_ded_num, opi.erp_master_num, opi.erp_reject_num, opi.ret_ded_num, opi.rate_name, opi.delivery_date, opi.tax_price, opi.gst_price, opi.gst_amount, opi.source_id, opi.uom_id, opi.uom_code, opi.uom_name, op.pur_id, op.pur_Name, op.dept_id, op.dept_name, op.dept_code, opi.delivery_type, opi.rate_id, opi.rate_val, opi.currency_id, opi.currency_name, opi.big_pack_standard_num, opi.small_pack_standard_num, opi.big_pack_label_num, opi.small_pack_label_num, opi.big_pack_mantissa, opi.small_pack_mantissa FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905' ORDER BY op.id DESC, opi.seq ASC LIMIT 20
2025-08-05 09:43:30.261 [http-nio-7003-exec-1] INFO  p6spy - <0><1952545967699611648> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1067290, 1067312, 1065924, 1066065, 1065924, 1066065, 1103821, 1103810, 244986, 244983))
2025-08-05 09:43:30.660 [http-nio-7003-exec-1] INFO  p6spy - <0><1952545967699611648> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1065924, 1065924, 1066065, 245043, 1065924, 1066065, 1065924, 1066065, 1066070))
2025-08-05 09:43:53.809 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:44:25.840 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:44:57.903 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:45:30.255 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:46:02.439 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:46:34.495 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:46:36.685 [http-nio-7003-exec-4] INFO  p6spy - <0><1952546751006855168> select '1' from dual
2025-08-05 09:46:36.708 [http-nio-7003-exec-4] INFO  p6spy - <0><1952546751006855168> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 09:46:36.976 [http-nio-7003-exec-10] INFO  p6spy - <0><1952546752109957120> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'order_pur' AND head_id = '24377' AND line_id = '0')
2025-08-05 09:46:37.196 [http-nio-7003-exec-5] INFO  p6spy - <0><1952546752688771072> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE (pur_id = 24377 AND delete_flag = 0) ORDER BY seq ASC
2025-08-05 09:46:37.214 [http-nio-7003-exec-5] INFO  p6spy - <0><1952546752688771072> select * from base_config where param_key = 'orderReply' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 09:46:37.284 [http-nio-7003-exec-5] INFO  p6spy - <0><1952546752688771072> select '1' from dual
2025-08-05 09:46:37.996 [http-nio-7003-exec-5] INFO  p6spy - <0><1952546752688771072> INSERT INTO base_oper_log (tenant_id, oper_bill, oper_type, soure_head_id, attr1, attr2, remark, create_id, creater, create_date, modifi_id, modifier, modify_date) VALUES (25409, 3, 10, 24377, 'PO2507310005', 'SU20250604641_admin', '查看采购订单PO2507310005', 30410, 'SU20250604641_admin', '2025-08-05 09:46:37', 30410, 'SU20250604641_admin', '2025-08-05 09:46:37')
2025-08-05 09:47:06.694 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:47:38.767 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:48:10.906 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:48:42.964 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:49:15.059 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:49:47.185 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:50:19.249 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:50:51.305 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:51:23.354 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:51:55.427 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:52:27.495 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:52:59.680 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:53:31.777 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:54:03.832 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:54:35.860 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:55:08.422 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:55:40.520 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:56:12.563 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:56:44.615 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:57:16.683 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:57:48.735 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:58:20.780 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:58:52.830 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:59:24.886 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 09:59:57.033 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:00:29.100 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:01:01.154 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:01:33.222 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:02:05.284 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:02:37.337 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:03:09.426 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:03:41.535 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:04:13.652 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:04:45.760 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:05:17.836 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:05:49.918 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:06:21.959 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:06:54.035 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:07:26.087 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:07:58.151 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:08:30.194 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:09:02.250 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:09:34.305 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:10:06.352 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:10:38.414 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:11:10.459 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:11:42.498 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:12:14.562 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:12:46.594 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:13:18.654 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:13:50.720 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:14:22.955 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:14:55.005 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:15:27.083 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:15:59.143 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:16:31.231 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:17:03.315 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:17:35.412 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:18:07.458 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:18:39.501 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:19:11.661 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:19:43.714 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:20:15.766 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:20:47.804 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:21:19.858 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:21:51.891 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:22:23.941 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:22:56.012 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:23:28.248 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:24:00.356 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:24:32.652 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:25:04.906 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:25:36.975 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:26:09.036 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:26:41.101 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:27:13.185 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:27:45.242 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:28:17.324 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:28:49.400 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:29:21.425 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:29:53.519 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:30:25.586 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:30:57.707 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:31:29.844 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:32:01.903 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:32:33.958 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:33:06.020 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:33:38.071 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:34:10.132 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:34:42.168 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:35:14.221 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:35:46.274 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:36:04.838 [http-nio-7003-exec-6] INFO  p6spy - <0><1952559200359632896> select '1' from dual
2025-08-05 10:36:05.120 [http-nio-7003-exec-7] INFO  p6spy - <0><1952559201479512064> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
2025-08-05 10:36:05.168 [http-nio-7003-exec-7] INFO  p6spy - <0><1952559201479512064> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
2025-08-05 10:36:05.708 [http-nio-7003-exec-2] INFO  p6spy - <0><1952559203991900160> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 10:36:05.715 [http-nio-7003-exec-1] INFO  p6spy - <0><1952559204017065986> select '1' from dual
2025-08-05 10:36:05.723 [http-nio-7003-exec-4] INFO  p6spy - <0><1952559204017065985> select '1' from dual
2025-08-05 10:36:05.738 [http-nio-7003-exec-1] INFO  p6spy - <0><1952559204017065986> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 10:36:05.743 [http-nio-7003-exec-4] INFO  p6spy - <0><1952559204017065985> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 10:36:06.104 [http-nio-7003-exec-10] INFO  p6spy - <0><1952559205388603392> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND parent_id = 0)
2025-08-05 10:36:06.120 [http-nio-7003-exec-10] INFO  p6spy - <0><1952559205388603392> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND is_valid = 1 AND dept_type = 1)
2025-08-05 10:36:07.283 [http-nio-7003-exec-6] INFO  p6spy - <0><1952559210585346048> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25409 AND bvni.is_read = '0'
2025-08-05 10:36:07.299 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559210585346049> SELECT COUNT(*) FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905'
2025-08-05 10:36:07.346 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559210585346049> SELECT op.tenant_id, op.id, op.pur_no, op.vendor_id, op.vendor_code, op.vendor_name, op.dept_name, op.order_date, op.dep_code, op.bsart, op.pur_name, op.order_flag, op.sync_date, op.order_type, op.stat, op.reply_stat, op.change_count, op.remark, op.tenant_name, op.currency_name, op.total_amount, op.order_remark, op.publish_date, op.purchasing_group, op.reserved06, op.purchasing_group, opi.seq, opi.item_stat, op.id sale_id, opi.id sale_item_id, op.pur_no sale_no, (opi.order_num - opi.make_num) dev_num, opi.is_close, opi.erp_change_type, opi.goods_id, opi.goods_erp_code, opi.goods_code, opi.goods_name, opi.goods_model, opi.delivery_stat, opi.order_num, opi.matched_plan_num, opi.make_num, opi.fix_num, opi.wait_num, opi.receive_num, opi.refund_num, opi.ref_ded_num, opi.erp_master_num, opi.erp_reject_num, opi.ret_ded_num, opi.rate_name, opi.delivery_date, opi.tax_price, opi.gst_price, opi.gst_amount, opi.source_id, opi.uom_id, opi.uom_code, opi.uom_name, op.pur_id, op.pur_Name, op.dept_id, op.dept_name, op.dept_code, opi.delivery_type, opi.rate_id, opi.rate_val, opi.currency_id, opi.currency_name, opi.big_pack_standard_num, opi.small_pack_standard_num, opi.big_pack_label_num, opi.small_pack_label_num, opi.big_pack_mantissa, opi.small_pack_mantissa FROM order_pur op LEFT JOIN order_pur_item opi ON op.id = opi.pur_id WHERE op.delete_flag = 0 AND opi.delete_flag = 0 AND opi.is_close = 0 AND op.stat > 2 AND op.vendor_id = 25409 AND op.dept_id = '28905' ORDER BY op.id DESC, opi.seq ASC LIMIT 20
2025-08-05 10:36:07.402 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559210585346049> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1067290, 1067312, 1065924, 1066065, 1065924, 1066065, 1103821, 1103810, 244986, 244983))
2025-08-05 10:36:07.455 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559210585346049> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1065924, 1065924, 1066065, 245043, 1065924, 1066065, 1065924, 1066065, 1066070))
2025-08-05 10:36:18.329 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:36:26.211 [http-nio-7003-exec-7] INFO  p6spy - <0><1952559289681530880> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:36:26.270 [http-nio-7003-exec-7] INFO  p6spy - <0><1952559289681530880> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:36:27.271 [http-nio-7003-exec-8] INFO  p6spy - <0><1952559294333014016> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2434' AND line_id = '0')
2025-08-05 10:36:27.583 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559295624859648> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
2025-08-05 10:36:27.598 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559295624859648> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:36:27.625 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559295624859648> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
2025-08-05 10:36:33.924 [http-nio-7003-exec-2] INFO  p6spy - <0><1952559322292244480> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:36:33.961 [http-nio-7003-exec-2] INFO  p6spy - <0><1952559322292244480> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:36:50.373 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:37:22.418 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:37:34.459 [http-nio-7003-exec-1] INFO  p6spy - <0><1952559576269934592> select '1' from dual
2025-08-05 10:37:37.034 [http-nio-7003-exec-4] INFO  p6spy - <0><1952559587003158528> select * from sys_user where user_code = 'sys' and is_deleted=0 and is_valid=1
2025-08-05 10:37:37.051 [http-nio-7003-exec-4] INFO  p6spy - <0><1952559587003158528> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 1)
2025-08-05 10:37:37.069 [http-nio-7003-exec-4] INFO  p6spy - <0><1952559587003158528> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = NULL
2025-08-05 10:37:37.326 [http-nio-7003-exec-4] INFO  com.dian.modules.sys.redis.SysRedisServer - <0><1952559587003158528> 过期时间:43199
2025-08-05 10:37:37.963 [http-nio-7003-exec-10] INFO  p6spy - <0><1952559590916444160> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 1)
2025-08-05 10:37:38.011 [http-nio-7003-exec-10] INFO  p6spy - <0><1952559590916444160> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = NULL
2025-08-05 10:37:38.436 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559592938098688> select '1' from dual
2025-08-05 10:37:38.436 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559592933904386> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 1 ORDER BY id ASC
2025-08-05 10:37:38.437 [http-nio-7003-exec-2] INFO  p6spy - <0><1952559592938098689> select '1' from dual
2025-08-05 10:37:38.453 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559592938098688> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 1 ORDER BY id ASC
2025-08-05 10:37:38.453 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559592933904386> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 1 AND status = 1) AND tenant_id = 1 ORDER BY id ASC
2025-08-05 10:37:38.453 [http-nio-7003-exec-2] INFO  p6spy - <0><1952559592938098689> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 1 AND status = 1) AND tenant_id = 1 ORDER BY id ASC
2025-08-05 10:37:38.598 [http-nio-7003-exec-1] INFO  p6spy - <0><1952559593625964544> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 1 AND parent_id = 0)
2025-08-05 10:37:38.615 [http-nio-7003-exec-1] INFO  p6spy - <0><1952559593625964544> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 1 AND is_valid = 1 AND dept_type = 1)
2025-08-05 10:37:39.035 [http-nio-7003-exec-4] INFO  p6spy - <0><1952559595425320960> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 1 AND content != '' AND bill_id = 0
2025-08-05 10:37:39.220 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 1 AND bv.vendor_stat = 1
2025-08-05 10:37:39.238 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 1 AND bv.wf_status = 0
2025-08-05 10:37:39.254 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 1 AND bv.wf_status = 2
2025-08-05 10:37:39.270 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 1
2025-08-05 10:37:39.284 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 2
2025-08-05 10:37:39.297 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 3
2025-08-05 10:37:39.310 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs WHERE bs.tenant_id = 1 and bs.sample_stat = 4
2025-08-05 10:37:39.324 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 1 and bsi.item_stat = 1
2025-08-05 10:37:39.340 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 1 and bsi.item_stat = 2
2025-08-05 10:37:39.354 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 1 and bsi.item_stat = 3
2025-08-05 10:37:39.411 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 1 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 1
2025-08-05 10:37:39.435 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 1 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 1 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 1 AND op.pur_id = 1
2025-08-05 10:37:39.454 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 3
2025-08-05 10:37:39.473 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 10:37:39.490 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 4
2025-08-05 10:37:39.510 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1 AND op.stat = 5
2025-08-05 10:37:39.528 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 1
2025-08-05 10:37:39.573 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.594 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.614 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.634 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.651 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.670 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.689 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.712 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.733 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 1 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:37:39.755 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 1
2025-08-05 10:37:39.778 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 1 AND dis.tenant_id = 1
2025-08-05 10:37:39.796 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 2 AND dis.tenant_id = 1
2025-08-05 10:37:39.814 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 3 AND dis.tenant_id = 1
2025-08-05 10:37:39.835 [http-nio-7003-exec-9] INFO  p6spy - <0><1952559596234821632> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 1 AND disi.inspection_results = 4 AND dis.tenant_id = 1
2025-08-05 10:37:43.997 [http-nio-7003-exec-10] INFO  p6spy - <0><1952559615767695360> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = '0') ORDER BY order_num ASC
2025-08-05 10:37:44.736 [http-nio-7003-exec-6] INFO  p6spy - <0><1952559617843875840> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = '0') ORDER BY order_num ASC
2025-08-05 10:37:54.471 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:38:08.169 [http-nio-7003-exec-2] INFO  p6spy - <0><1952559717647339520> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE id = 14383
2025-08-05 10:38:08.334 [http-nio-7003-exec-7] INFO  p6spy - <0><1952559717647339521> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = 0 AND is_valid = 1) ORDER BY parent_id, order_num ASC
2025-08-05 10:38:21.646 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE id = 14383
2025-08-05 10:38:21.690 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (menu_path_ids LIKE '/12947/13437/14383/%') ORDER BY menu_type ASC
2025-08-05 10:38:21.706 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE id = 13437
2025-08-05 10:38:21.744 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> UPDATE sys_menu SET parent_id = 13437, menu_name = '批量确认收样', menu_perms = 'base:sample:confirmRecSample', menu_type = 2, is_system = 0, is_back = 0, is_blank = 0, order_num = 5, short_cut_url = '', is_valid = 1, is_hand = 1, ent_type = '1', menu_path_ids = '/12947/13437/14383/', menu_path_names = '/基础管理/送样通知单/批量确认收样/', create_id = 1, creater = 'sys', create_date = '2025-08-04 16:26:42', modifi_id = 1, modifier = 'sys', modify_date = '2025-08-05 10:38:21' WHERE id = 14383
2025-08-05 10:38:21.777 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> UPDATE sys_menu SET is_back = 0, is_valid = 1, ent_type = '1', menu_path_ids = getMenuId(id), menu_path_names = getMenuName(id) WHERE id = 14383
2025-08-05 10:38:21.805 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> UPDATE sys_menu SET is_back = 0, is_valid = 1, ent_type = '1', menu_path_ids = getMenuId(id), menu_path_names = getMenuName(id) WHERE id = 14383
2025-08-05 10:38:21.836 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> UPDATE sys_menu_ent sme SET menu_path_ids = (SELECT menu_path_ids FROM sys_menu WHERE id = sme.menu_id), menu_path_names = (SELECT menu_path_names FROM sys_menu WHERE id = sme.menu_id) WHERE tenant_id = 0
2025-08-05 10:38:21.862 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_valid = 1 AND id = 14383)
2025-08-05 10:38:21.953 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> DELETE FROM sys_menu_ent WHERE (menu_path_ids LIKE '/12947/13437/14383/%')
2025-08-05 10:38:26.527 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:38:58.592 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:39:04.206 [http-nio-7003-exec-3] INFO  p6spy - <0><1952559773985230848> INSERT INTO sys_menu_ent (tenant_id, menu_id, menu_path_ids, menu_path_names, create_date) SELECT se.id, sm.id, sm.menu_path_ids, menu_path_names, now() FROM sys_menu sm, sys_ent se WHERE se.item_code != 'sys' AND INSTR(sm.ent_type, se.ent_type) > 0 AND sm.is_valid = 1 AND sm.menu_path_ids LIKE CONCAT('/12947/13437/14383/', '%') AND se.is_valid = 1
2025-08-05 10:39:06.616 [http-nio-7003-exec-5] INFO  p6spy - <0><1952559962280120320> SELECT id, parent_id, parent_name, menu_name, menu_url, menu_perms, menu_type, is_system, is_back, is_blank, menu_icon, order_num, short_cut_url, short_cut_name, is_valid, is_hand, ent_type, menu_path_ids, menu_path_names, create_id, creater, create_date, modifi_id, modifier, modify_date, remark FROM sys_menu WHERE (is_system = '0') ORDER BY order_num ASC
2025-08-05 10:39:30.648 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:40:02.687 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:40:34.737 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:41:06.786 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:41:38.836 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:42:10.890 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:42:42.931 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:43:05.091 [http-nio-7003-exec-9] INFO  p6spy - <0><1952560926089236480> select '1' from dual
2025-08-05 10:43:08.323 [http-nio-7003-exec-10] INFO  p6spy - <0><1952560944636448768> select '1' from dual
2025-08-05 10:43:13.174 [http-nio-7003-exec-6] INFO  p6spy - <0><1952560996805201920> select * from sys_user where user_code = 'GVE_Admin' and is_deleted=0 and is_valid=1
2025-08-05 10:43:13.211 [http-nio-7003-exec-6] INFO  p6spy - <0><1952560996805201920> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
2025-08-05 10:43:13.277 [http-nio-7003-exec-6] INFO  p6spy - <0><1952560996805201920> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
2025-08-05 10:43:14.259 [http-nio-7003-exec-6] INFO  com.dian.modules.sys.redis.SysRedisServer - <0><1952560996805201920> 过期时间:43198
2025-08-05 10:43:14.974 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:43:15.290 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561005495799808> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
2025-08-05 10:43:15.380 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561005495799808> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 28904
2025-08-05 10:43:16.265 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 24739 ORDER BY id ASC
2025-08-05 10:43:16.349 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 141 AND tenant_id = 24739
2025-08-05 10:43:16.355 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561009371336704> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 24739 AND status = 1) AND tenant_id = 24739 ORDER BY id ASC
2025-08-05 10:43:16.389 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 141 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 10:43:16.409 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 143 AND tenant_id = 24739
2025-08-05 10:43:16.426 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 143 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 10:43:16.442 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 146 AND tenant_id = 24739
2025-08-05 10:43:16.459 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 146 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 10:43:16.474 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 148 AND tenant_id = 24739
2025-08-05 10:43:16.492 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 148 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 10:43:16.492 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561010843537408> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND parent_id = 0)
2025-08-05 10:43:16.513 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561010843537408> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 24739 AND is_valid = 1 AND dept_type = 1)
2025-08-05 10:43:16.513 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 152 AND tenant_id = 24739
2025-08-05 10:43:16.557 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 152 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 10:43:16.574 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE id = 156 AND tenant_id = 24739
2025-08-05 10:43:16.599 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561009333587968> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 156 AND is_valid = 1) AND tenant_id = 24739 ORDER BY conf_seq ASC
2025-08-05 10:43:17.071 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
2025-08-05 10:43:17.073 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561013251067905> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
2025-08-05 10:43:17.093 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561013251067905> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 10:43:17.095 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
2025-08-05 10:43:17.129 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
2025-08-05 10:43:17.156 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
2025-08-05 10:43:17.172 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
2025-08-05 10:43:17.197 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
2025-08-05 10:43:17.226 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
2025-08-05 10:43:17.265 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
2025-08-05 10:43:17.306 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
2025-08-05 10:43:17.362 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
2025-08-05 10:43:17.626 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
2025-08-05 10:43:17.676 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
2025-08-05 10:43:17.728 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
2025-08-05 10:43:17.749 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 10:43:17.779 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
2025-08-05 10:43:17.850 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
2025-08-05 10:43:17.933 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
2025-08-05 10:43:18.145 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.187 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.220 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.266 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.326 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.388 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.466 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.555 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.642 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:18.706 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
2025-08-05 10:43:18.742 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
2025-08-05 10:43:18.810 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
2025-08-05 10:43:18.895 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
2025-08-05 10:43:19.007 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561013251067904> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
2025-08-05 10:43:21.446 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561031504678912> SELECT COUNT(*) FROM base_sample bs WHERE bs.tenant_id = 24739
2025-08-05 10:43:21.552 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561031504678912> SELECT bs.id, bs.tenant_id, bs.vendor_id, bs.vendor_code, bs.vendor_erp_code, bs.vendor_name, bs.dept_id, bs.dept_code, bs.dept_name, bs.sample_no, bs.sample_date, bs.sample_stat, bs.source_id, bs.source_no, bs.source_type, bs.demand_class_type, bs.is_need_up_file, bs.is_valid, bs.delete_flag, bs.return_remark, bs.is_return, bs.return_date, bs.remark, bs.create_id, bs.creater, bs.create_date, bs.modifi_id, bs.modifier, bs.modify_date, bs.tenant_name, bs.applicant, bs.apply_dept_name, bs.apply_date, bsi.id item_id, bsi.goods_id, bsi.goods_code, bsi.goods_erp_code, bsi.goods_name, bsi.goods_model, bsi.demand_date, bsi.demand_qty, bsi.reply_quantity, bsi.reply_delivery_date, bsi.reply_state, bsi.pur_name, bsi.goods_num, bsi.item_stat, bsi.case_stat, bsi.case_date, bsi.vendor_remark, bsi.remark item_remark, (SELECT count(*) FROM dm_inspection_sheet_item WHERE source_id = bs.id) if_inspection FROM base_sample bs LEFT JOIN base_sample_item bsi ON bs.id = bsi.sample_id WHERE bs.tenant_id = 24739 ORDER BY bs.id DESC LIMIT 20
2025-08-05 10:43:26.404 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.vendor_stat = 1
2025-08-05 10:43:26.409 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561052379729920> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0
2025-08-05 10:43:26.424 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 0
2025-08-05 10:43:26.425 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561052379729920> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 24739 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 10:43:26.457 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) vendorCount FROM base_vendor bv WHERE bv.delete_flag = 0 AND bv.tenant_id = 24739 AND bv.wf_status = 2
2025-08-05 10:43:26.494 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 1
2025-08-05 10:43:26.537 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 2
2025-08-05 10:43:26.586 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 3
2025-08-05 10:43:26.655 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs WHERE bs.tenant_id = 24739 and bs.sample_stat = 4
2025-08-05 10:43:26.724 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 1
2025-08-05 10:43:26.801 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 2
2025-08-05 10:43:26.851 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.tenant_id = 24739 and bsi.item_stat = 3
2025-08-05 10:43:26.938 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739
2025-08-05 10:43:26.969 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 26442 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 24739 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.tenant_id = 24739 AND op.pur_id = 26442
2025-08-05 10:43:26.990 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3
2025-08-05 10:43:27.010 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 10:43:27.031 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 4
2025-08-05 10:43:27.051 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739 AND op.stat = 5
2025-08-05 10:43:27.070 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.tenant_id = 24739
2025-08-05 10:43:27.117 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.137 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.162 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.180 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.199 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.221 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.244 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.264 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.282 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:43:27.302 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.tenant_id = 24739
2025-08-05 10:43:27.321 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 1 AND dis.tenant_id = 24739
2025-08-05 10:43:27.340 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 2 AND dis.tenant_id = 24739
2025-08-05 10:43:27.363 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 3 AND dis.tenant_id = 24739
2025-08-05 10:43:27.383 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561052379729921> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.tenant_id = 24739 AND disi.inspection_results = 4 AND dis.tenant_id = 24739
2025-08-05 10:43:47.019 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:44:00.470 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561195237724160> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 26442)
2025-08-05 10:44:00.521 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561195237724160> SELECT id, user_id, tenant_id, is_main, dept_id, dept_name, dept_code, dept_path_id, dept_path_code, dept_path_name, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user_dept WHERE (user_id = 26442) ORDER BY dept_id ASC
2025-08-05 10:44:05.883 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561217555615744> SELECT * FROM (SELECT reply_date AS startDate FROM order_pur_item WHERE wait_num > 0 AND item_stat = 4 AND pur_id IN (SELECT id FROM order_pur WHERE tenant_id = 24739) UNION SELECT plan_date AS startDate FROM dm_delivery_plan_item WHERE delete_flag = 0 AND plan_date IS NOT NULL AND sale_id IN (SELECT id FROM order_pur WHERE tenant_id = 24739)) od ORDER BY od.startDate ASC LIMIT 1
2025-08-05 10:44:06.072 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561218658717696> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
2025-08-05 10:44:06.109 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561218658717696> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:44:06.547 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561220470657024> SELECT COUNT(*) FROM (SELECT '2025-08-05' AS startDate, so.pur_id, so.pur_name, so.dept_id, so.dept_name, so.dept_code, soi.order_price_uom, soi.werks, soi.price_uom, so.tenant_name tenantName, soi.delivery_type, so.id saleId, soi.id saleItemId, so.pur_no saleNo, it.plan_no planNo, so.order_date orderDate, soi.goods_id goodsId, soi.goods_code goodsCode, soi.goods_name goodsName, soi.goods_model goodsModel, soi.goods_erp_code goodsErpCode, soi.goods_class_name goodsClassName, soi.class_code goodsClassCode, soi.seq saleSeq, ifnull(it.drawing_no, '-') AS drawingNo, it.match_num AS orderNum, (SELECT IFNULL(SUM(dev_num), 0) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum, so.vendor_id, so.vendor_code vendorCode, so.vendor_name vendorName, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) waitNum, soi.uom_id uomId, soi.uom_code, soi.uom_name uomName, soi.gst_price gstPrice, soi.tax_price taxPrice, soi.gst_price * soi.order_num gstAmount, soi.currency_name currencyName, soi.currency_id currencyId, soi.rate_id rateId, soi.rate_val rateVal, soi.rate_name rateName, soi.taxes_type taxesType, soi.invoice_type invoiceType, so.order_type orderType, soi.purchase_remark remark, soi.change_count changeCount, so.stat, soi.seq, soi.is_close, it.plan_date replyDate, barcode_type barcodeType, it.purchaser_name purEmployee, soi.sale_employee_name saleEmployee, soi.soure_no sourceNo, it.delivery_date deliveryDate, it.warehouse_code warehouseCode, soi.warehouse_name warehouseName, soi.item_stat itemStat, soi.delivery_type deliveryType, it.collect_flag collectFlag, it.receiving_control receivingControl, so.vendor_delivery_address, so.shipping_address, it.id, it.id AS list_id, it.id AS plan_srm_line_id, IFNULL(it.refund_num, 0) refund_num, IFNULL(it.un_competent_num, 0) un_competent_num, soi.tenant_id tenantId, ifnull(it.make_num, 0) makeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) canMakeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) devNum, ifnull(it.make_num, 0) tempFixNum, so.order_type, soi.aux_uom_id, soi.aux_uom_code, soi.aux_uom_name, soi.aux_uom_num, it.uom_num, 1 is_source_to_plan, 0 convert_numerator, (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num FROM order_pur so, order_pur_item soi, dm_delivery_plan_item it WHERE so.id = soi.pur_id AND it.sale_item_id = soi.id AND it.sale_id = so.id AND it.delete_Flag <> 2 AND ((so.change_count = 0 AND item_stat = 4) OR so.change_count > 0 AND item_stat IN (1, 4)) AND soi.delivery_type = 2 AND (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) > 0 AND soi.is_close = 0 AND soi.return_mark = 0 AND so.dept_id = 28905) so WHERE so.canMakeNum > 0
2025-08-05 10:44:06.594 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561220470657024> SELECT * FROM (SELECT '2025-08-05' AS startDate, so.pur_id, so.pur_name, so.dept_id, so.dept_name, so.dept_code, soi.order_price_uom, soi.werks, soi.price_uom, so.tenant_name tenantName, soi.delivery_type, so.id saleId, soi.id saleItemId, so.pur_no saleNo, it.plan_no planNo, so.order_date orderDate, soi.goods_id goodsId, soi.goods_code goodsCode, soi.goods_name goodsName, soi.goods_model goodsModel, soi.goods_erp_code goodsErpCode, soi.goods_class_name goodsClassName, soi.class_code goodsClassCode, soi.seq saleSeq, ifnull(it.drawing_no, '-') AS drawingNo, it.match_num AS orderNum, (SELECT IFNULL(SUM(dev_num), 0) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.de_stat = 2 AND dd.delete_flag = 0 AND ddi.plan_srm_line_id = it.id) fixNum, so.vendor_id, so.vendor_code vendorCode, so.vendor_name vendorName, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) waitNum, soi.uom_id uomId, soi.uom_code, soi.uom_name uomName, soi.gst_price gstPrice, soi.tax_price taxPrice, soi.gst_price * soi.order_num gstAmount, soi.currency_name currencyName, soi.currency_id currencyId, soi.rate_id rateId, soi.rate_val rateVal, soi.rate_name rateName, soi.taxes_type taxesType, soi.invoice_type invoiceType, so.order_type orderType, soi.purchase_remark remark, soi.change_count changeCount, so.stat, soi.seq, soi.is_close, it.plan_date replyDate, barcode_type barcodeType, it.purchaser_name purEmployee, soi.sale_employee_name saleEmployee, soi.soure_no sourceNo, it.delivery_date deliveryDate, it.warehouse_code warehouseCode, soi.warehouse_name warehouseName, soi.item_stat itemStat, soi.delivery_type deliveryType, it.collect_flag collectFlag, it.receiving_control receivingControl, so.vendor_delivery_address, so.shipping_address, it.id, it.id AS list_id, it.id AS plan_srm_line_id, IFNULL(it.refund_num, 0) refund_num, IFNULL(it.un_competent_num, 0) un_competent_num, soi.tenant_id tenantId, ifnull(it.make_num, 0) makeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) canMakeNum, (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) devNum, ifnull(it.make_num, 0) tempFixNum, so.order_type, soi.aux_uom_id, soi.aux_uom_code, soi.aux_uom_name, soi.aux_uom_num, it.uom_num, 1 is_source_to_plan, 0 convert_numerator, (SELECT COUNT(*) FROM order_material_list oml WHERE pur_order_item_id = soi.id) material_list_num FROM order_pur so, order_pur_item soi, dm_delivery_plan_item it WHERE so.id = soi.pur_id AND it.sale_item_id = soi.id AND it.sale_id = so.id AND it.delete_Flag <> 2 AND ((so.change_count = 0 AND item_stat = 4) OR so.change_count > 0 AND item_stat IN (1, 4)) AND soi.delivery_type = 2 AND (ifnull(it.match_num, 0) - ifnull(it.make_num, 0)) > 0 AND soi.is_close = 0 AND soi.return_mark = 0 AND so.dept_id = 28905) so WHERE so.canMakeNum > 0 ORDER BY so.deliveryDate ASC LIMIT 20
2025-08-05 10:44:06.613 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561220470657024> select * from base_config where param_key = 'deliveryHomeDays' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:44:06.653 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561220470657024> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1065924, 1066065, 1123394, 1123530, 1078086))
2025-08-05 10:44:08.537 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561229144477696> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2435' AND line_id = '0')
2025-08-05 10:44:08.545 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561229157060608> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2435
2025-08-05 10:44:08.561 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561229157060608> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:44:08.588 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561229157060608> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2435 AND delete_flag = 0)
2025-08-05 10:44:10.628 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561237818298368> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
2025-08-05 10:44:10.666 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561237818298368> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:44:19.083 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:44:19.527 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561275227295744> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2416' AND line_id = '0')
2025-08-05 10:44:19.531 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561275239878656> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2416
2025-08-05 10:44:19.551 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561275239878656> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:44:19.619 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561275239878656> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2416 AND delete_flag = 0)
2025-08-05 10:44:21.155 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561281992708096> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905'
2025-08-05 10:44:21.204 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561281992708096> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.tenant_id = 24739 AND dd.dept_id = '28905' ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:44:25.215 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561299042553856> select * from sys_user where user_code = 'SU20250604641_Admin' and is_deleted=0 and is_valid=1
2025-08-05 10:44:25.234 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561299042553856> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
2025-08-05 10:44:25.254 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561299042553856> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
2025-08-05 10:44:25.599 [http-nio-7003-exec-3] INFO  com.dian.modules.sys.redis.SysRedisServer - <0><1952561299042553856> 过期时间:43199
2025-08-05 10:44:26.244 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561303362686976> SELECT id, user_code, user_name, pass_word, salt, user_email, user_mobile, remark, user_ext, tenant_id, dept_id, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_user WHERE (id = 30410)
2025-08-05 10:44:26.288 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561303362686976> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE id = 29703
2025-08-05 10:44:26.737 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561305472421890> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 10:44:26.737 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561305472421889> SELECT id, parent_id, dist_code, dist_name, conf_seq, conf_key, conf_value, is_system, is_valid, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_dist WHERE (parent_id = 0) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 10:44:26.741 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561305472421892> select '1' from dual
2025-08-05 10:44:26.760 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561305472421892> SELECT id, param_key, param_value, is_system, tenant_id, status, remark, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_config WHERE (tenant_id = 25409 AND status = 1) AND tenant_id = 25409 ORDER BY id ASC
2025-08-05 10:44:26.869 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561306030264320> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND parent_id = 0)
2025-08-05 10:44:26.889 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561306030264320> SELECT id, parent_id, parent_code, parent_name, dept_name, dept_code, dept_type, order_num, tenant_id, dept_path_name, dept_path_code, dept_path_id, is_leaf, this_leader_id, this_leader_code, this_leader_name, super_leader_id, super_leader_code, super_leader_name, attr1, attr2, attr3, attr4, attr5, is_valid, is_deleted, create_id, creater, create_date, modifi_id, modifier, modify_date FROM sys_dept WHERE (tenant_id = 25409 AND is_valid = 1 AND dept_type = 1)
2025-08-05 10:44:27.276 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
2025-08-05 10:44:27.283 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561307754123264> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
2025-08-05 10:44:27.291 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
2025-08-05 10:44:27.296 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561307749928960> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
2025-08-05 10:44:27.297 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561307804454912> select '1' from dual
2025-08-05 10:44:27.305 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
2025-08-05 10:44:27.316 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561307804454912> SELECT COUNT(*) FROM base_vendor_notice bvn, base_vendor_notice_item bvni WHERE bvn.id = bvni.notice_id AND bvn.stat = 2 AND bvni.vendor_id = 25409 AND bvni.is_read = '0'
2025-08-05 10:44:27.318 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561307754123264> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 10:44:27.320 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
2025-08-05 10:44:27.337 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
2025-08-05 10:44:27.351 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
2025-08-05 10:44:27.367 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
2025-08-05 10:44:27.426 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
2025-08-05 10:44:27.449 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
2025-08-05 10:44:27.467 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
2025-08-05 10:44:27.488 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 10:44:27.506 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
2025-08-05 10:44:27.523 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
2025-08-05 10:44:27.540 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
2025-08-05 10:44:27.587 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.607 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.626 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.646 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.671 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.691 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.711 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.733 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.753 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 10:44:27.777 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
2025-08-05 10:44:27.795 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
2025-08-05 10:44:27.814 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
2025-08-05 10:44:27.833 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
2025-08-05 10:44:27.856 [http-nio-7003-exec-10] INFO  p6spy - <0><1952561307749928961> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
2025-08-05 10:44:34.501 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561337726619648> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:44:34.572 [http-nio-7003-exec-2] INFO  p6spy - <0><1952561337726619648> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:44:36.831 [http-nio-7003-exec-8] INFO  p6spy - <0><1952561347818115072> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2416' AND line_id = '0')
2025-08-05 10:44:37.144 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561349126737920> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2416
2025-08-05 10:44:37.160 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561349126737920> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:44:37.186 [http-nio-7003-exec-5] INFO  p6spy - <0><1952561349126737920> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2416 AND delete_flag = 0)
2025-08-05 10:44:51.131 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:45:15.420 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561509177184256> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:45:15.468 [http-nio-7003-exec-6] INFO  p6spy - <0><1952561509177184256> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:45:21.291 [http-nio-7003-exec-7] INFO  p6spy - <0><1952561534296870912> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2392' AND line_id = '0')
2025-08-05 10:45:21.609 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561535630659584> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
2025-08-05 10:45:21.626 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561535630659584> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:45:21.648 [http-nio-7003-exec-4] INFO  p6spy - <0><1952561535630659584> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
2025-08-05 10:45:23.179 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:45:25.835 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
2025-08-05 10:45:25.863 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
2025-08-05 10:45:25.906 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE (id = 125930)
2025-08-05 10:45:25.970 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE id = 125930
2025-08-05 10:45:26.057 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> UPDATE order_pur_item SET tenant_p_id = 24739, tenant_id = 24739, source_id = 185265, source_item_id = 368469, pur_id = 24359, seq = '1', erp_change_type = '', goods_id = 1103821, goods_erp_code = '8501000000052', goods_code = '8501000000052', goods_name = '输出线', goods_model = 'R输出线(35094/CK01-03456客供线)(剥线45MM上锡5MM 无SR)B:KG S:KG/旧编码:85668077 ROHS 2.0、REACH', uom_id = 617, uom_code = 'Pcs', uom_name = 'Pcs', aux_uom_id = 617, aux_uom_code = 'Pcs', aux_uom_name = 'Pcs', rate_id = 65, rate_name = '13%增值税', rate_val = 13.000000, matched_plan_num = 0.000000, gst_amount = 31800.000000, tax_amount = 28141.590000, delivery_type = 2, taxes_type = 0, gst_price = 3.180000, tax_price = 2.814159, barcode_type = 0, soure_no = 'PO2507230003', change_count = 0, item_stat = 4, delivery_date = '2025-07-23 15:39:38', reply_date = '2025-07-23 15:40:00', make_num = 2001.000000, order_num = 10000.000000, purchase_confirm = 0, vendor_confirm = 0, fix_num = 1001.000000, refund_num = 0.000000, erp_master_num = 0.000000, erp_reject_num = 0.000000, wait_num = 8999.000000, goods_class_name = 'DC线材', delivery_stat = 1, is_busy = 0, delete_flag = 0, is_print = 0, is_close = 0, return_mark = 0, free_mark = 0, print_count = 0, is_return_po = 0, order_price_uom = 'Pcs', price_uom = 10000.00, un_competent_num = 0.000000, mrp_region = '101', create_id = 0, creater = '平台账号', create_date = '2025-07-23 15:38:37', modifi_id = 30410, modifier = 'SU20250604641_admin', modify_date = '2025-08-05 10:45:25' WHERE id = 125930
2025-08-05 10:45:26.084 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE (id = 125931)
2025-08-05 10:45:26.129 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, source_id, source_item_id, pur_id, seq, erp_change_type, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, self_num, small_set_number, matched_plan_num, gst_amount, tax_amount, sign, delivery_type, currency_id, currency_name, taxes_type, invoice_type, gst_price, tax_price, barcode_type, warehouse_id, warehouse_code, warehouse_name, soure_no, change_count, item_stat, delivery_date, reply_date, purchase_remark, vendor_remark, line_remark, make_num, order_num, purchase_confirm, vendor_confirm, purchase_doc_url, purchase_doc_name, vendor_doc_url, vendor_doc_name, fix_num, receive_num, refund_num, ref_ded_num, erp_master_num, erp_reject_num, ret_ded_num, wait_num, is_main, main_item_id, main_item_code, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, pur_employee_name, sale_employee_name, class_code, goods_class_name, delivery_stat, is_busy, closing_time, delete_flag, is_print, is_close, return_mark, free_mark, print_count, is_return_po, order_price_uom, price_uom, sap_tax_price, sap_tax_amount, sap_gst_price, sap_gst_amount, sap_price_uom, werks, plans, address, un_competent_num, drawing_no, true_reply_date, mrp_region, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM order_pur_item WHERE id = 125931
2025-08-05 10:45:26.182 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> UPDATE order_pur_item SET tenant_p_id = 24739, tenant_id = 24739, source_id = 185265, source_item_id = 368470, pur_id = 24359, seq = '2', erp_change_type = '', goods_id = 1103810, goods_erp_code = '8501000000040', goods_code = '8501000000040', goods_name = '输出线', goods_model = 'R输出线(21306客供线)无SR B:KG S:KG/旧编码:85668066 ROHS 2.0、REACH', uom_id = 617, uom_code = 'Pcs', uom_name = 'Pcs', aux_uom_id = 617, aux_uom_code = 'Pcs', aux_uom_name = 'Pcs', rate_id = 65, rate_name = '13%增值税', rate_val = 13.000000, matched_plan_num = 0.000000, gst_amount = 0.000000, tax_amount = 0.000000, delivery_type = 2, taxes_type = 0, gst_price = 0.000000, tax_price = 0.000000, barcode_type = 0, soure_no = 'PO2507230003', change_count = 0, item_stat = 4, delivery_date = '2025-07-23 15:40:56', reply_date = '2025-07-23 15:40:00', make_num = 1001.000000, order_num = 10000.000000, purchase_confirm = 0, vendor_confirm = 0, fix_num = 1.000000, refund_num = 0.000000, erp_master_num = 0.000000, erp_reject_num = 0.000000, wait_num = 9999.000000, goods_class_name = 'DC线材', delivery_stat = 1, is_busy = 0, delete_flag = 0, is_print = 0, is_close = 0, return_mark = 0, free_mark = 0, print_count = 0, is_return_po = 0, order_price_uom = 'Pcs', price_uom = 10000.00, un_competent_num = 0.000000, mrp_region = '101', create_id = 0, creater = '平台账号', create_date = '2025-07-23 15:38:37', modifi_id = 30410, modifier = 'SU20250604641_admin', modify_date = '2025-08-05 10:45:26' WHERE id = 125931
2025-08-05 10:45:26.240 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> UPDATE dm_delivery SET tenant_p_id = 0, tenant_id = 24739, de_no = 'SH202507232707', delivery_type = 2, vendor_id = 25409, vendor_code = 'SA0116', vendor_name = '测试供应商A', dept_id = 28905, dept_code = '101', dept_name = '佛山市顺德区冠宇达电源有限公司', is_print = 0, print_count = 0, is_call = 0, de_stat = 2, is_ele = 0, logistics_name = '', ele_no = '', delivery_date = '2025-08-05 10:45:25', audit_time = '2025-08-05 10:45:25', delete_flag = 0, see = 0, tenant_name = '', is_compromise = 0, is_compromise_create = 0, address = '', shipping_address = '总厂材料仓', order_type = 1, create_id = 30410, creater = 'SU20250604641_admin', create_date = '2025-07-23 19:52:05', modifi_id = 30410, modifier = 'SU20250604641_admin', modify_date = '2025-08-05 10:45:26' WHERE id = 2392
2025-08-05 10:45:26.314 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> SELECT id, tenant_p_id, tenant_id, goods_code, goods_erp_code, goods_name, goods_model, min_size, supply_cycle, is_valid, bar_code, class_code, purchuser, delete_flag, material_source, remark, class_id, class_name, class_id_paths, class_name_paths, inventory_category_erp_id, inventory_category_erp_code, inventory_category_erp_name, uom_id, uom_code, uom_name, aux_uom_id, aux_uom_code, aux_uom_name, pur_uom_id, pur_uom_code, pur_uom_name, price_uom, cat_code, cat_name, goods_desc, min_box_num, collect_flag, warehouse_code, warehouse_name, drawing_no, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM base_goods WHERE (tenant_id = 24739 AND id IN (1103821, 1103810))
2025-08-05 10:45:26.841 [http-nio-7003-exec-9] INFO  c.d.modules.gve.wms.service.impl.GveWmsServiceImpl - <0><1952561553041215488> WMS接口请求地址=http://192.168.1.119:9005/sync/order/srmPurchaseDelivery/standart
2025-08-05 10:45:26.845 [http-nio-7003-exec-9] INFO  c.d.modules.gve.wms.service.impl.GveWmsServiceImpl - <0><1952561553041215488> WMS接口请求参数={"corpId":"gyd","data":{"supplierName":"测试供应商A","creator":"SRM","code":"SH202507232707","voucherType":"标准采购","createTime":"2025-08-05 10:45:25","supplierCode":"SA0116","rows":[{"quantity":1.00,"productionBatchNo":"1","unitName":"Pcs","sourceItemId":"368469","reserve1":"2392","manufactureDate":"2025-07-23","rowCode":"5728","materialCode":"8501000000052","sourceVoucherCode":"PO2507230003","reserve2":"5728","warehouseCode":"CL01"},{"quantity":1.00,"productionBatchNo":"1","unitName":"Pcs","sourceItemId":"368470","reserve1":"2392","manufactureDate":"2025-07-23","rowCode":"5729","materialCode":"8501000000040","sourceVoucherCode":"PO2507230003","reserve2":"5729","warehouseCode":"CL01"}]},"sign":"268DFBF7FA8DB910E374475CC1E76E39","timestamp":"1754361926338"}
2025-08-05 10:45:36.565 [http-nio-7003-exec-9] INFO  c.d.modules.gve.wms.service.impl.GveWmsServiceImpl - <0><1952561553041215488> WMS接口返回信息={"code":1,"msg":"数据同步成功"}
2025-08-05 10:45:36.676 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> INSERT INTO sys_intfsap_log (send_msg, tenant_id, return_msg, intf_name, send_url, exe_second, create_id, creater, create_date, modifi_id, modifier, modify_date) VALUES ('{"corpId":"gyd","data":{"supplierName":"测试供应商A","creator":"SRM","code":"SH202507232707","voucherType":"标准采购","createTime":"2025-08-05 10:45:25","supplierCode":"SA0116","rows":[{"quantity":1.00,"productionBatchNo":"1","unitName":"Pcs","sourceItemId":"368469","reserve1":"2392","manufactureDate":"2025-07-23","rowCode":"5728","materialCode":"8501000000052","sourceVoucherCode":"PO2507230003","reserve2":"5728","warehouseCode":"CL01"},{"quantity":1.00,"productionBatchNo":"1","unitName":"Pcs","sourceItemId":"368470","reserve1":"2392","manufactureDate":"2025-07-23","rowCode":"5729","materialCode":"8501000000040","sourceVoucherCode":"PO2507230003","reserve2":"5729","warehouseCode":"CL01"}]},"sign":"268DFBF7FA8DB910E374475CC1E76E39","timestamp":"1754361926338"}', 24739, '{"code":1,"msg":"数据同步成功"}', '送货单同步至WMS接口', 'http://192.168.1.119:9005/sync/order/srmPurchaseDelivery/standart', 10, 30410, 'SU20250604641_admin', '2025-08-05 10:45:36', 30410, 'SU20250604641_admin', '2025-08-05 10:45:36')
2025-08-05 10:45:36.712 [http-nio-7003-exec-9] INFO  c.d.m.k.s.impl.PurchaseOrderJoggleServiceImpl - <0><1952561553041215488> 修改采购订单参数：{"NeedUpDateFields":["F_UXTH_deliqty"],"NeedReturnFields":[],"IsDeleteEntry":false,"SubSystemId":"","IsVerifyBaseDataField":false,"IsEntryBatchFill":true,"ValidateFlag":true,"NumberSearch":true,"IsAutoAdjustField":false,"InterationFlags":"","IgnoreInterationFlag":"true","IsControlPrecision":false,"ValidateRepeatJson":false,"model":[{"FID":"185265","FPOOrderEntry":[{"FEntryID":"368469","F_UXTH_deliqty":1001.000000},{"FEntryID":"368470","F_UXTH_deliqty":1.000000}]}]}
2025-08-05 10:45:36.966 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561599744790528> select '1' from dual
2025-08-05 10:45:36.996 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561599744790528> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:45:37.038 [http-nio-7003-exec-3] INFO  p6spy - <0><1952561599744790528> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:45:38.794 [http-nio-7003-exec-9] INFO  c.d.m.k.s.impl.PurchaseOrderJoggleServiceImpl - <0><1952561553041215488> 修改采购订单返回结果: {"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":185265,"Number":"PO2507230003","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"NeedReturnData":[]}}
2025-08-05 10:45:38.841 [http-nio-7003-exec-9] INFO  p6spy - <0><1952561553041215488> INSERT INTO sys_intfsap_log (send_msg, tenant_id, return_msg, intf_name, send_url, exe_second, create_id, creater, create_date, modifi_id, modifier, modify_date) VALUES ('{"NeedUpDateFields":["F_UXTH_deliqty"],"NeedReturnFields":[],"IsDeleteEntry":false,"SubSystemId":"","IsVerifyBaseDataField":false,"IsEntryBatchFill":true,"ValidateFlag":true,"NumberSearch":true,"IsAutoAdjustField":false,"InterationFlags":"","IgnoreInterationFlag":"true","IsControlPrecision":false,"ValidateRepeatJson":false,"model":[{"FID":"185265","FPOOrderEntry":[{"FEntryID":"368469","F_UXTH_deliqty":1001.000000},{"FEntryID":"368470","F_UXTH_deliqty":1.000000}]}]}', 24739, '{"result":{"needReturnData":[],"responseStatus":{"errors":[],"isSuccess":true,"msgCode":0,"successEntitys":[{"dIndex":0,"id":"185265","number":"PO2507230003"}]}}}', '批量修改采购订单', 'PUR_PurchaseOrder', 1754361938814, 30410, 'SU20250604641_admin', '2025-08-05 10:45:38', 30410, 'SU20250604641_admin', '2025-08-05 10:45:38')
2025-08-05 10:45:41.826 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561620074582016> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
2025-08-05 10:45:41.922 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561620074582016> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:45:41.957 [http-nio-7003-exec-1] INFO  p6spy - <0><1952561620074582016> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
2025-08-05 10:45:55.222 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:46:27.272 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:46:59.307 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:47:31.344 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:48:03.399 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:48:35.444 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:49:08.409 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:49:40.510 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:49:42.850 [http-nio-7003-exec-10] INFO  p6spy - <0><1952562631245778944> select '1' from dual
2025-08-05 10:49:42.994 [http-nio-7003-exec-10] INFO  p6spy - <0><1952562631245778944> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2392
2025-08-05 10:49:43.018 [http-nio-7003-exec-10] INFO  p6spy - <0><1952562631245778944> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:49:43.177 [http-nio-7003-exec-10] INFO  p6spy - <0><1952562631245778944> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2392 AND delete_flag = 0)
2025-08-05 10:50:12.755 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:50:44.911 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:51:17.324 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:51:49.365 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:51:58.420 [http-nio-7003-exec-2] INFO  p6spy - <0><1952563199875960832> select '1' from dual
2025-08-05 10:51:58.438 [http-nio-7003-exec-2] INFO  p6spy - <0><1952563199875960832> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:51:58.557 [http-nio-7003-exec-2] INFO  p6spy - <0><1952563199875960832> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:52:03.897 [http-nio-7003-exec-8] INFO  p6spy - <0><1952563222940438528> SELECT COUNT(*) FROM sys_oss WHERE (table_name = 'dm_delivery' AND head_id = '2434' AND line_id = '0')
2025-08-05 10:52:03.899 [http-nio-7003-exec-5] INFO  p6spy - <0><1952563222957215744> select '1' from dual
2025-08-05 10:52:03.920 [http-nio-7003-exec-5] INFO  p6spy - <0><1952563222957215744> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
2025-08-05 10:52:03.939 [http-nio-7003-exec-5] INFO  p6spy - <0><1952563222957215744> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 10:52:03.963 [http-nio-7003-exec-5] INFO  p6spy - <0><1952563222957215744> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
2025-08-05 10:52:21.759 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:52:53.818 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:53:25.873 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:53:57.924 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:54:29.990 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:55:02.047 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:55:34.111 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:56:06.159 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:56:38.213 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:57:10.274 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:57:42.336 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:57:52.608 [http-nio-7003-exec-6] INFO  p6spy - <0><1952564685305499648> select '1' from dual
2025-08-05 10:57:52.633 [http-nio-7003-exec-6] INFO  p6spy - <0><1952564685305499648> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:57:52.717 [http-nio-7003-exec-6] INFO  p6spy - <0><1952564685305499648> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:58:13.466 [http-nio-7003-exec-7] INFO  p6spy - <0><1952564772920315904> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:58:13.512 [http-nio-7003-exec-7] INFO  p6spy - <0><1952564772920315904> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:58:14.385 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:58:26.485 [http-nio-7003-exec-4] INFO  p6spy - <0><1952564827404324864> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 10:58:26.528 [http-nio-7003-exec-4] INFO  p6spy - <0><1952564827404324864> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 10:58:46.440 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:59:18.490 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 10:59:50.534 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:00:22.582 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:00:54.735 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:01:26.855 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:01:58.906 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:02:31.351 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:02:56.418 [http-nio-7003-exec-3] INFO  p6spy - <0><1952565959778324480> select '1' from dual
2025-08-05 11:02:56.459 [http-nio-7003-exec-3] INFO  p6spy - <0><1952565959778324480> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
2025-08-05 11:02:56.510 [http-nio-7003-exec-3] INFO  p6spy - <0><1952565959778324480> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 11:02:56.556 [http-nio-7003-exec-3] INFO  p6spy - <0><1952565959778324480> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
2025-08-05 11:03:03.403 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:03:35.768 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:03:56.330 [http-nio-7003-exec-1] INFO  p6spy - <0><1952566211109408770> select '1' from dual
2025-08-05 11:03:56.330 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select '1' from dual
2025-08-05 11:03:56.346 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
2025-08-05 11:03:56.346 [http-nio-7003-exec-9] INFO  p6spy - <0><1952566211109408769> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
2025-08-05 11:03:56.363 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
2025-08-05 11:03:56.371 [http-nio-7003-exec-1] INFO  p6spy - <0><1952566211109408770> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
2025-08-05 11:03:56.380 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
2025-08-05 11:03:56.383 [http-nio-7003-exec-9] INFO  p6spy - <0><1952566211109408769> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 11:03:56.396 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
2025-08-05 11:03:56.411 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
2025-08-05 11:03:56.426 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
2025-08-05 11:03:56.443 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
2025-08-05 11:03:56.568 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
2025-08-05 11:03:56.602 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
2025-08-05 11:03:56.623 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
2025-08-05 11:03:56.644 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 11:03:56.664 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
2025-08-05 11:03:56.686 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
2025-08-05 11:03:56.709 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
2025-08-05 11:03:56.769 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.792 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.811 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.830 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.850 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.871 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.894 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.914 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.934 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:03:56.966 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
2025-08-05 11:03:56.988 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
2025-08-05 11:03:57.007 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
2025-08-05 11:03:57.028 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
2025-08-05 11:03:57.047 [http-nio-7003-exec-10] INFO  p6spy - <0><1952566211109408768> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
2025-08-05 11:04:07.810 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:04:39.903 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:05:11.974 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:05:42.017 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting fail, StatusCode(-1) invalid. for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:05:43.636 [http-nio-7003-exec-2] INFO  p6spy - <0><1952566661120479232> select '1' from dual
2025-08-05 11:05:43.655 [http-nio-7003-exec-2] INFO  p6spy - <0><1952566661120479232> SELECT COUNT(*) FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409
2025-08-05 11:05:43.748 [http-nio-7003-exec-2] INFO  p6spy - <0><1952566661120479232> SELECT dd.id, dd.de_no deNo, dd.tenant_id deliveryTenantId, dd.vendor_id deliveryVendorId, ddi.seq, ddi.id deItemId, dd.delivery_date deDate, dd.de_stat deStat, ddi.sale_id, ddi.sale_item_id, ddi.sale_no, ddi.sale_seq purSeq, dd.vendor_code vendorCode, dd.vendor_name vendorName, ddi.goods_id goodsId, ddi.goods_code goodsCode, ddi.goods_erp_code goodsErpCode, ddi.goods_name goodsName, ddi.goods_model goodsModel, ddi.goods_class_code, ddi.goods_class_name, ddi.order_num, ddi.uom_id uomId, ddi.uom_name uomName, ddi.uom_num, ddi.aux_uom_id, ddi.aux_uom_code, ddi.aux_uom_name, ddi.rate_id rateId, ddi.rate_val rateVal, ddi.currency_id currencyId, ddi.currency_name currencyName, ddi.taxes_type taxesType, ddi.invoice_type invoiceType, ddi.gst_price gstPrice, ddi.tax_price taxPrice, ddi.tem_date temDate, ddi.dev_num devNum, ddi.tem_num temNum, ddi.un_num unNum, ddi.inv_num invNum, ddi.un_inv_num unInvNum, ddi.order_num orderNum, dd.see see, ddi.vendor_doc_url vendorDocUrl, ddi.delivery_date deliveryDate, dd.delivery_date reality_delivery_date, ddi.reply_date replyDate, ddi.rate_name rateName, ddi.drawing_no, ddi.pur_employee_name purEmployee, ddi.sale_employee_name saleEmployee, ddi.barcode_type barcodeType, dd.tenant_name tenantName, ddi.warehouse_name warehouseName, dd.is_compromise isCompromise, dd.is_compromise_create isComproiseCreate, ddi.is_compromise itemIsCompromise, ddi.is_compromise_create itemIsCompromiseCreate, ddi.compromise_num compromiseNum, ddi.ret_num retNum, ddi.arrival_date arrivalDate, ddi.qua_sheet_stat quaSheetStat, ddi.qua_check_stat quaCheckStat, ddi.submit_stat submitStat, dd.create_date createDate, dd.is_print isPrint, dd.print_count printCount, ddi.plan_date, dd.dept_id, dd.dept_code, dd.dept_name, dd.audit_time, ddi.remark AS line_remark FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq LIMIT 20
2025-08-05 11:06:14.220 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:06:46.370 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:07:18.417 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:07:20.747 [http-nio-7003-exec-8] INFO  p6spy - <0><1952567068504838145> select '1' from dual
2025-08-05 11:07:20.751 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select '1' from dual
2025-08-05 11:07:20.757 [http-nio-7003-exec-5] INFO  p6spy - <0><1952567068504838144> select '1' from dual
2025-08-05 11:07:20.767 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 1
2025-08-05 11:07:20.772 [http-nio-7003-exec-8] INFO  p6spy - <0><1952567068504838145> SELECT COUNT(*) FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0
2025-08-05 11:07:20.782 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 2
2025-08-05 11:07:20.793 [http-nio-7003-exec-5] INFO  p6spy - <0><1952567068504838144> SELECT * FROM base_mail WHERE menu_title = '公告列表' AND tenant_id = 25409 ORDER BY id DESC LIMIT 10
2025-08-05 11:07:20.797 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 3
2025-08-05 11:07:20.806 [http-nio-7003-exec-8] INFO  p6spy - <0><1952567068504838145> SELECT id, tenant_id, user_id, url, stat, bill_id, content, create_id, creater, create_date FROM base_mail WHERE tenant_id = 25409 AND content != '' AND bill_id = 0 ORDER BY id DESC LIMIT 20
2025-08-05 11:07:20.811 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs WHERE bs.vendor_id = 25409 and bs.sample_stat = 4
2025-08-05 11:07:20.829 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 1
2025-08-05 11:07:20.846 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 2
2025-08-05 11:07:20.861 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> select count(*) from base_sample bs,base_sample_item bsi WHERE bs.id = bsi.sample_id and bs.vendor_id = 25409 and bsi.item_stat = 3
2025-08-05 11:07:20.920 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = NULL AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409
2025-08-05 11:07:20.948 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT IFNULL(count(*), 0) orderCount FROM order_pur op INNER JOIN order_pur_item opl ON op.id = opl.pur_Id AND op.delete_flag = 0 AND opl.delete_flag = 0 AND op.stat > 2 LEFT JOIN (SELECT jof.order_id, sum(CASE WHEN jof.follow_id = 30410 AND jof.oper_type = 1 AND jof.order_type = 1 AND jof.delete_flag = 0 THEN 1 ELSE 0 END) isFollow FROM order_follow jof WHERE jof.tenant_id = 25409 GROUP BY jof.order_id) x1 ON x1.order_id = opl.id WHERE op.vendor_id = 25409 AND op.pur_id = 30410
2025-08-05 11:07:20.965 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3
2025-08-05 11:07:20.983 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 3 AND TIMESTAMPDIFF(MINUTE, publish_date, now()) > NULL
2025-08-05 11:07:21.002 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 4
2025-08-05 11:07:21.023 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409 AND op.stat = 5
2025-08-05 11:07:21.042 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) orderCount FROM order_pur op WHERE op.stat > 2 AND op.delete_flag = 0 AND op.vendor_id = 25409
2025-08-05 11:07:21.093 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND inv_num = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.115 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.ret_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.136 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.159 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.tem_num > 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.182 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.202 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.232 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.is_compromise_create = 0 AND ddi.qua_check_stat = 2 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.254 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND dd.de_stat = 2 AND ddi.qua_sheet_stat = 0 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.275 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) AS deliveryCount FROM dm_delivery dd, dm_delivery_item ddi WHERE dd.id = ddi.de_id AND dd.delete_flag = 0 AND ddi.delete_flag = 0 AND dd.vendor_id = 25409 AND ddi.is_compromise = 1 AND ddi.defective_manual_return_type = 1 ORDER BY dd.id DESC, ddi.seq DESC
2025-08-05 11:07:21.296 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) FROM dm_master dm LEFT JOIN dm_master_item dmi ON dm.id = dmi.master_id WHERE dm.master_type = 3 AND ISNULL((SELECT id FROM dm_inspection_sheet_item WHERE source_item_id = dmi.id LIMIT 1)) AND dm.vendor_id = 25409
2025-08-05 11:07:21.314 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 1 AND dis.tenant_id = 25409
2025-08-05 11:07:21.334 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 2 AND dis.tenant_id = 25409
2025-08-05 11:07:21.444 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 3 AND dis.tenant_id = 25409
2025-08-05 11:07:21.466 [http-nio-7003-exec-6] INFO  p6spy - <0><1952567068509032448> SELECT count(*) FROM dm_inspection_sheet dis, dm_inspection_sheet_item disi WHERE dis.id = disi.inspection_sheet_id AND dis.source_type = 2 AND dis.vendor_id = 25409 AND disi.inspection_results = 4 AND dis.tenant_id = 25409
2025-08-05 11:07:50.539 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:08:22.572 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:08:54.639 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:09:26.693 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:09:58.753 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:10:30.829 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:11:02.879 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:11:34.921 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:12:07.037 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:12:39.102 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:13:11.160 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:13:43.226 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:14:15.275 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:14:47.347 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:15:19.419 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:15:51.487 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:16:23.581 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:16:55.824 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:17:27.878 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:17:59.941 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:18:32.003 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:19:04.396 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:19:36.499 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:20:08.563 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:20:40.622 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:21:12.690 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:21:44.731 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:22:06.647 [http-nio-7003-exec-7] INFO  p6spy - <0><1952570784180031488> select '1' from dual
2025-08-05 11:22:06.677 [http-nio-7003-exec-7] INFO  p6spy - <0><1952570784180031488> SELECT id, tenant_p_id, tenant_id, de_no, delivery_type, is_out, vendor_id, vendor_code, vendor_name, dept_id, dept_code, dept_name, is_print, print_count, is_call, de_stat, is_ele, logistics_name, ele_no, delivery_date, document_date, audit_time, delete_flag, see, tenant_name, is_compromise, is_compromise_create, remark, warehouse_code, collect_flag, receiving_control, address, shipping_address, order_type, purchaser_name, purchaser_id, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery WHERE id = 2434
2025-08-05 11:22:06.712 [http-nio-7003-exec-7] INFO  p6spy - <0><1952570784180031488> select * from base_config where param_key = 'ifQuality' and `status`=1 and tenant_id='24739' limit 0,1
2025-08-05 11:22:06.746 [http-nio-7003-exec-7] INFO  p6spy - <0><1952570784180031488> SELECT id, tenant_p_id, tenant_id, dept_id, dept_code, dept_name, de_id, sale_id, sale_no, sale_seq, sale_item_id, goods_id, goods_erp_code, goods_code, goods_name, goods_model, uom_id, uom_code, uom_name, uom_num, aux_uom_id, aux_uom_code, aux_uom_name, aux_uom_num, rate_id, rate_name, rate_val, currency_id, currency_name, remark, taxes_type, invoice_type, gst_price, tax_price, delivery_date, plan_date, reply_date, tem_date, dev_num, tem_num, un_num, inv_num, ret_qty, un_inv_num, order_num, barcode_type, doc_name, doc_url, is_print, print_count, delete_flag, seq, big_pack_standard_num, small_pack_standard_num, big_pack_label_num, small_pack_label_num, big_pack_mantissa, small_pack_mantissa, purchase_confirm, vendor_doc_url, vendor_doc_name, gst_amount, tax_amount, soure_no, pur_employee_name, sale_employee_name, goods_class_code, goods_class_name, warehouse_code, werks, warehouse_name, order_price_uom, price_uom, order_type, quality_type, qr_code, drawing_no, is_compromise, warranty_period, prod_batch_no, prod_date, is_compromise_create, qua_num, qua_check_stat, qua_sheet_stat, competent_num, un_competent_num, submit_stat, compromise_num, ret_num, defind_stat, arrival_date, reserved01, reserved02, reserved03, reserved04, reserved05, reserved06, reserved07, reserved08, reserved09, reserved10, plan_id, plan_srm_line_id, plan_line_id, quality_completed_time, defective_manual_return_type, mrp_region, is_source_to_plan, create_id, creater, create_date, modifi_id, modifier, modify_date FROM dm_delivery_item WHERE (de_id = 2434 AND delete_flag = 0)
2025-08-05 11:22:16.848 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:22:49.016 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:23:21.100 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:23:53.168 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:24:25.245 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:24:57.300 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:25:29.355 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:26:01.422 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:26:33.461 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:27:05.564 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:27:37.728 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:28:09.948 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:28:42.197 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:29:14.257 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:29:46.458 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:30:18.515 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:30:50.716 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:31:22.779 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:31:54.847 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:32:26.954 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:32:59.004 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:33:31.056 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:34:03.111 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:34:35.164 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:35:07.223 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:35:39.285 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:36:11.352 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:36:43.427 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:37:15.463 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:37:47.515 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:38:19.572 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:38:51.621 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:39:23.673 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:39:55.728 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:40:27.789 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:40:59.839 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:41:31.886 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:42:03.944 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:42:35.999 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:43:08.077 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:43:40.132 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:44:12.215 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:44:44.248 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:45:16.567 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:45:48.604 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:46:20.659 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:46:52.704 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:47:24.763 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:47:56.821 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:48:28.874 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:49:00.917 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:49:32.961 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:50:05.022 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:50:37.070 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:51:09.129 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:51:41.186 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:52:13.246 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:52:45.293 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:53:17.334 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:53:49.398 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:54:21.455 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:54:53.510 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:55:25.547 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:55:57.594 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:56:29.642 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:57:01.697 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:57:33.752 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:58:05.806 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:58:37.860 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:59:09.910 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 11:59:41.959 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:00:14.009 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:00:46.086 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:01:18.147 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:01:50.206 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:02:22.259 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:02:54.315 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:03:26.382 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:03:58.432 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:04:30.471 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:05:02.530 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:05:34.603 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:06:06.671 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:06:38.725 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:07:10.764 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:07:42.836 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:08:14.883 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:08:46.967 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:09:19.025 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:09:51.095 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:10:23.171 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:10:55.244 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:11:27.310 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:11:59.390 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:12:31.437 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:13:03.517 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:13:35.586 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:14:07.639 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:14:39.689 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:15:11.746 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:15:43.805 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:16:15.864 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:16:47.919 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:17:19.972 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:17:52.029 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:18:24.057 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:18:56.114 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:19:28.167 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:20:00.231 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:20:32.282 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:21:04.328 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:21:36.389 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:22:08.453 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:22:40.498 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:23:12.551 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:23:44.581 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:24:16.647 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:24:48.687 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:25:20.755 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:25:52.813 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:26:24.863 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:26:56.922 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:27:28.985 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:28:01.059 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:28:33.120 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:29:05.187 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:29:37.240 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:30:09.299 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:30:41.364 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:31:13.411 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:31:45.462 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:32:17.504 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:32:49.557 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:33:21.618 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:33:53.669 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:34:25.729 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:34:57.787 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:35:29.844 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:36:01.900 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:36:33.957 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:37:06.007 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:37:38.071 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:38:10.107 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:38:42.175 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:39:14.248 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:39:46.309 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:40:18.369 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:40:50.425 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:41:22.486 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:41:54.555 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:42:26.615 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:42:58.662 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:43:30.729 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:44:02.778 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:44:34.844 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:45:06.881 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:45:38.934 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:46:10.977 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:46:43.020 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:47:15.082 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:47:47.145 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:48:19.190 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:48:51.216 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:49:23.286 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:49:55.346 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:50:27.415 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:50:59.467 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:51:31.539 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:52:03.587 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:52:35.650 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:53:07.728 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:53:39.797 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:54:11.858 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:54:43.934 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:55:15.991 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:55:48.056 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:56:20.121 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:56:52.164 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:57:24.227 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:57:56.289 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:58:28.337 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:59:00.406 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 12:59:32.451 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:00:04.515 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:00:36.570 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:01:08.629 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:01:40.693 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:02:12.745 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:02:44.797 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:03:16.862 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:03:48.930 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:04:20.987 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:04:53.067 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:05:25.126 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:05:57.208 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:06:29.261 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:07:01.312 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:07:33.360 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:08:05.403 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:08:37.453 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:09:09.528 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:09:41.573 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:10:13.641 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:10:45.700 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:11:17.762 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:11:49.823 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:12:21.880 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:12:53.957 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:13:26.017 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:13:58.090 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:14:30.150 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:15:02.202 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:15:34.248 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:16:06.307 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:16:38.364 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:17:10.430 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:17:42.484 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:18:14.546 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:18:46.601 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:19:18.666 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:19:50.729 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:20:22.790 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:20:54.847 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:21:26.904 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:21:58.960 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:22:31.014 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:23:03.073 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:23:35.117 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:24:07.190 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:24:39.243 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:25:11.312 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:25:43.376 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:26:15.436 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:26:47.500 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:27:19.573 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:27:51.636 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:28:23.680 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:28:55.737 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:29:27.797 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:29:59.851 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:30:32.004 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:31:04.025 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
2025-08-05 13:31:36.660 [xxl-job, executor ExecutorRegistryThread] INFO  com.xxl.job.core.thread.ExecutorRegistryThread - >>>>>>>>>>> xxl-job registry fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='srm-dev', registryValue='http://*************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-rpc remoting error(Connection refused: connect), for url : http://127.0.0.1:8888/api/registry, content=null]
