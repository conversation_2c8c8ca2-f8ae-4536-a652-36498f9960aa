<template>
  <div class="DIAN-common-layout">
    <div class="DIAN-common-layout-center">

      <!-- 搜索框 -->
      <el-row class="DIAN-common-search-box" :gutter="24">
        <el-form @submit.native.prevent>
          <el-col :span="5">
            <el-form-item>
              <el-date-picker
                v-model="queryDates"
                type="daterange"
                placeholder="请选择送货日期"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item>
              <el-input v-model.trim="queryParam.planNo" placeholder="计划单号" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item>
              <el-input v-model.trim="queryParam.vendor" placeholder="供应商编码/名称" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item>
              <el-input v-model.trim="queryParam.goods" placeholder="物料编码/名称/规格" @keyup.enter.native="search()" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="search()">{{$t('common.search')}}</el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()">{{$t('common.reset')}}</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <!-- body -->
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <!-- 表头工具栏 -->
        <div class="DIAN-common-head">
          <div>
            <el-button-group >
              <el-button size="small" :type="buttonFrom.count1" @click="changeCountsButton(1 , 'count1')">送货计划</el-button>
              <el-button size="small" :type="buttonFrom.count2" @click="changeCountsButton(2 , 'count2')">历史记录</el-button>
            </el-button-group>
          </div>
          <div class="DIAN-common-head-right">
<!--            <el-button type="primary" @click="exportHandle()" icon="el-icon-download"-->
<!--                       v-has-per="'dm:deliveryPlanItem:export'">-->
<!--              {{ $t('common.exportBtn') }}-->
<!--            </el-button>-->
            <el-button type="primary" @click="openReplyReport()">
              打开交期确认报表
            </el-button>
            <!--v-has-per="'dm:deliveryPlanItem:closePlanItemByIds'"-->
<!--            <el-button type="danger" @click="closePlanItemByIds()" icon="el-icon-delete-solid" :loading="btnLoading">-->
<!--              关闭-->
<!--            </el-button>-->
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false" @click="search()" />
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>
        <!-- 表格 -->
        <d-table ref="listTable"
                 v-loading="listLoading"
                 :data="list"
                 hasC
                 @selection-change="handleSelectionChange"
                 show-summary
                 v-if="tabIdx === 1">
          <el-table-column prop="vendorCode" label="供应商编码" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="vendorName" label="供应商名称" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="goodsErpCode" label="物料编码" align="center" show-overflow-tooltip width="120"/>
          <el-table-column prop="goodsName" label="物料名称" align="center" show-overflow-tooltip width="120"/>
          <el-table-column prop="goodsModel" label="规格型号" align="center" show-overflow-tooltip/>
          <el-table-column prop="planNum" label="计划数" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="planDate" label="计划日期" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.planDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="replyQty" label="回复数量" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="replyDate" label="回复日期" align="center" show-overflow-tooltip width="150">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.replyDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="modifier" label="更新人" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="modifyDate" label="更新时间" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.modifyDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
        </d-table>
        <el-table ref="hisListTable" v-loading="listLoading" :data="hisList" v-if="tabIdx === 2" height="600vh" border>
          <el-table-column prop="vendorCode" label="供应商编码" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="vendorName" label="供应商名称" align="center" show-overflow-tooltip width="150"/>
          <el-table-column prop="goodsErpCode" label="物料编码" align="center" show-overflow-tooltip width="120"/>
          <el-table-column prop="goodsName" label="物料名称" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="goodsModel" label="规格型号" align="center" show-overflow-tooltip/>
          <el-table-column prop="planNum" label="计划数" align="center" show-overflow-tooltip width="100"/>
          <el-table-column prop="planDate" label="计划日期" align="center" show-overflow-tooltip width="100">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.planDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="replyQty" label="回复数量" align="center" show-overflow-tooltip width="90"/>
          <el-table-column prop="replyDate" label="回复日期" align="center" show-overflow-tooltip width="150">
            <template slot-scope="scope">
              <span>{{ $dian.dateFormat(scope.row.replyDate, 'YYYY-MM-DD') }}</span>
            </template>
          </el-table-column>
        </el-table>
        <d-pagination :total="total" :page.sync="queryParam.page" :limit.sync="queryParam.limit" @pagination="initData"/>
      </div>
      <d-export title="送货计划导入模板下载" ref="tempDownload" :exports="false"></d-export>
      <d-export title="送货计划" ref="export"></d-export>
      <d-import ref="upload" @callData="importHandle()" ></d-import>
      <Download ref="download"></Download>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import Download from "./downloadDynaTemplate";
import {
  getDeliveryPlanList,
  getDeliveryPlanItemInfo,
  queryDeliveryPlanHisPage,
} from "@/api/dm/deliveryPlan";
import dian from "@/utils/dian";

export default {
  name: "dm-deliveryPlan-tenant",
  components: {
    Download
  },
  data() {
    return {
      PlanDeleteFlagOptions: store.getters.commonEnums['dm.DeliveryPlanDeleteFlagEnum'], // 订单类型
      ValidTypeOptions:store.getters.commonEnums['comm.ValidEnum'],
      queryParam: {
        page: 1,
        limit: 20,
        planNo: '',
        tenantIds:'tenantIds',//用于区分是否为供应商
        orderNo : '', // 订单号
        sortObj: 'dd.id DESC',//排序
        whereType:1,//气泡查询条件 默认为1 - 全部
        vendor : '', // 供应商编码|名称
        goods : '', // 物料编码|名称|规格
        keyword : '',  // 物料编码|名称|描述|图号
        queryDate:'',//要求送货日期
        startDate : '',  // 开始日期
        orderType : '', // 订单类型
        validType: '',
        creater : '', // 创建人
        deptId:'',//组织机构id
        deleteFlag:'5',//排序方式(1-全部；2-按物料；3-按可制单数量>0)
      },
      planItemFromVisible:false,
      queryDates:[],//要求送货日期
      deliveryCount:{},//气泡数
      listLoading: false,
      btnLoading: false,
      formVisible:false,
      list: [],//列表数据
      hisList: [],//历史列表数据
      tabIdx: 1,
      total: 0,//条数
      hisTotal: 0,
      selectedDatas: [],/*选择的数据*/
      selectedNum: 0,/*选择数据的条数*/
      userInfo:store.getters.userInfo,/*获取当前用户*/
      dataForm:{
        id:'',
        saleNo:'',
        deptName:'',
        vendorName:'',
        goodsErpCode:'',
        goodsName:'',
        goodsModel:'',
        matchNum:'',
        makeNum:'',
        unCompetentNum:'',
        refundNum:'',
        planDate:'',
      },
      buttonFrom: {
        count1: 'primary',
        count2: '',
        count3: '',
        count4: '',
        count5: '',
        count6:'',
      },
    }
  },
  created() {
    this.initData();
  },
  watch:{
    $route:{
      handler(to,from){
        if(to.path === "/dm/deliveryPlan/tenant" && to.query){
          // this.queryParam.goods = to.query.goodsErpCode;
          this.queryParam.vendor = to.query.
          this.initData();
        }
      },
      immediate: true
    }
  },
  methods: {
    initData() {
      if (this.tabIdx === 1){
        this.listLoading = true;
        if (this.queryDates.length !== 0) {
          const startDate = this.$dian.dateFormat(this.queryDates[0], 'YYYY-MM-DD');
          const endDate = this.$dian.dateFormat(this.queryDates[1], 'YYYY-MM-DD');
          this.queryParam.queryDate = startDate +" 至 "+endDate;
        }
        let subCompanyInfoData = dian.storageGet('subCompanyInfo');
        if (subCompanyInfoData){
          this.queryParam.deptId = subCompanyInfoData.id;
        }
        getDeliveryPlanList(this.queryParam).then(res => {
          this.total = res.data.totalCount;
          this.list = res.data.list;
          this.listLoading = false;
        }).catch(() => {
          this.listLoading = false;
        })
      }
      if (this.tabIdx === 2){
        this.initHisData()
      }
    },
    // 历史数据
    initHisData(){
      this.listLoading = true;
      if (this.queryDates.length !== 0) {
        const startDate = this.$dian.dateFormat(this.queryDates[0], 'YYYY-MM-DD');
        const endDate = this.$dian.dateFormat(this.queryDates[1], 'YYYY-MM-DD');
        this.queryParam.queryDate = startDate +" 至 "+endDate;
      }
      let subCompanyInfoData = dian.storageGet('subCompanyInfo');
      if (subCompanyInfoData){
        this.queryParam.deptId = subCompanyInfoData.id;
      }
      console.log(this.queryParam)
      queryDeliveryPlanHisPage(this.queryParam).then(res => {
        this.total = res.data.totalCount;
        this.hisList = res.data.list;
        this.listLoading = false;
      }).catch(() => {
        this.listLoading = false;
      })
    },
    //点击气泡查询
    changeCountsButton(stat, name){
      // 动态变换
      this.buttonFrom.count1 = '';
      this.buttonFrom.count2 = '';
      this.buttonFrom[name] = 'primary';
      if (stat === 1){
        this.tabIdx = 1;
        this.search();
      }
      if (stat === 2){
        this.tabIdx = 2;
        this.searchHis();
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedDatas = selection.map(item => item)
      //获取所有选中项数组的长度
      this.selectedNum = selection.length
    },
    importHandle(){
      this.search();
    },
    // 搜索方法，并返回到第一页
    search() {
      this.queryParam.page = 1;
      this.initData();
    },
    searchHis() {
      this.queryParam.page = 1;
      this.initHisData();
    },
    // 重置方法
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.queryDates = [];
      this.search();
    },
    //打开送货单详情弹窗
    openInfoForm(id){
      this.dataForm = this.$options.data().dataForm;
      this.planItemFromVisible = true;
      if (id){
        getDeliveryPlanItemInfo(id).then(res => {
          this.dataForm = res.data
        })
      }
    },
    //关闭刷新列表数据
    callDeliveryBoardList(){
      this.formVisible = false;
      this.search();
    },
    // 快速跳转至计划交期确认报表
    openReplyReport() {
      this.$router.push({path: '/dm/report/planReplyReport'})
    },
    //导出
    exportHandle() {
      this.$refs.export.init('/api/dm/deliveryPlanItem/export', '送货计划', this.queryParam);
    },
  }
}
</script>

<style scoped>

</style>
