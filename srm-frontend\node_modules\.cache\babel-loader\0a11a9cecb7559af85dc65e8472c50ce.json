{"remainingRequest": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\Desktop\\srm\\srm-frontend\\src\\views\\dashboard\\admin\\index.vue", "mtime": 1754374677795}, {"path": "E:\\Desktop\\srm\\srm-frontend\\babel.config.js", "mtime": 1749788270807}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1683164318633}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1683164317554}, {"path": "E:\\Desktop\\srm\\srm-frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1683164317835}], "contextDependencies": [], "result": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es6.string.starts-with\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"core-js/modules/es6.regexp.split\");\nvar _slicedToArray2 = _interopRequireDefault(require(\"E:/Desktop/srm/srm-frontend/node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nrequire(\"core-js/modules/es7.array.includes\");\nrequire(\"core-js/modules/es6.string.includes\");\nvar _echarts = _interopRequireDefault(require(\"echarts\"));\nvar _tenantPanelGroup = _interopRequireDefault(require(\"./components/tenantPanelGroup\"));\nvar _vendorPanelGroup = _interopRequireDefault(require(\"./components/vendorPanelGroup\"));\nvar _LineChart = _interopRequireDefault(require(\"./components/LineChart\"));\nvar _vueCountTo = _interopRequireDefault(require(\"vue-count-to\"));\nvar _mail = require(\"@/api/base/mail\");\nvar _notice = require(\"@/api/base/notice\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = {\n  name: 'DashboardAdmin',\n  components: {\n    PanelGroup: _tenantPanelGroup.default,\n    LineChart: _LineChart.default,\n    CountTo: _vueCountTo.default,\n    VendorPanelGroup: _vendorPanelGroup.default\n  },\n  data: function data() {\n    return {\n      value1: '',\n      charts: '',\n      total: 0,\n      queryParam: {\n        page: 1,\n        limit: 20\n      },\n      userInfo: _store.default.getters.userInfo,\n      /*获取当前登录的用户信息*/\n      opinionData: [\"3\", \"2\", \"4\", \"4\", \"5\"],\n      massageList: [],\n      //消息列表数据\n      noticeMsgList: [] //消息列表数据\n    };\n  },\n  created: function created() {\n    this.userInfo = _store.default.getters.userInfo;\n    this.initMassageList();\n    if (this.userInfo.entType != 1) {\n      this.initNoticeMsgList();\n    }\n  },\n  methods: {\n    //初始化查询消息列表数据\n    initMassageList: function initMassageList() {\n      var _this = this;\n      (0, _mail.queryLastMailList)(this.queryParam).then(function (res) {\n        _this.massageList = res.data.list;\n        _this.total = res.data.totalCount;\n      }).catch(function (res) {});\n    },\n    initNoticeMsgList: function initNoticeMsgList() {\n      var _this2 = this;\n      (0, _notice.queryMsgByNotice)({}).then(function (res) {\n        _this2.noticeMsgList = res.data;\n      }).catch(function (res) {});\n    },\n    // //点击打开表单\n    // openForm(data){\n    //   let routerUrl = data.url;\n    //   this.$router.push({path: routerUrl})\n    // }\n    //点击打开表单\n    openForm: function openForm(data) {\n      var routerUrl = data.url;\n      if (routerUrl.includes('?')) {\n        var _routerUrl$split = routerUrl.split('?'),\n          _routerUrl$split2 = (0, _slicedToArray2.default)(_routerUrl$split, 2),\n          path = _routerUrl$split2[0],\n          queryString = _routerUrl$split2[1];\n        var query = {};\n        // 解析查询参数\n        if (queryString) {\n          queryString.split('&').forEach(function (param) {\n            var _param$split = param.split('='),\n              _param$split2 = (0, _slicedToArray2.default)(_param$split, 2),\n              key = _param$split2[0],\n              value = _param$split2[1];\n            if (key && value) {\n              query[key] = decodeURIComponent(value);\n            }\n          });\n        }\n        var fullPath = path.startsWith('/') ? path : \"/\".concat(path);\n        if (fullPath === '/base/sampleDemand/tenant' && query.id && !query.detailVisible) {\n          query.detailVisible = true;\n          query.t = Date.now();\n        }\n        this.$router.push({\n          path: fullPath,\n          query: query\n        });\n      } else {\n        var _fullPath = routerUrl.startsWith('/') ? routerUrl : \"/\".concat(routerUrl);\n        this.$router.push({\n          path: _fullPath\n        });\n      }\n    }\n  }\n};\nexports.default = _default;", null]}