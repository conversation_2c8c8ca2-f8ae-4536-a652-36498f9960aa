<template>
  <div class="DIAN-common-layout DIAN-flex-main">
    <div class="DIAN-common-layout-center">
      <el-row class="DIAN-common-search-box" :gutter="16">
        <el-form @submit.native.prevent>
          <el-col :span="6">
            <!-- v-model.trim 可以去除前后空格 -->
            <el-input v-model.trim="queryParam.demandNo" placeholder="请输入需求单号" clearable/>
          </el-col>
          <el-col :span="4">
            <el-input v-model.trim="queryParam.applicant" placeholder="请输入申请人名称" clearable/>
          </el-col>
          <!-- <el-col :span="4">
            <el-form-item>
              <el-select v-model="queryParam.isNeedUpFile" placeholder="检验状态" clearable>
                <el-option :key="item.key" :label="item.value" :value="item.key"
                           v-for="item in validOps"/>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="4">
            <el-input v-model.trim="queryParam.pur" placeholder="请输入采购员名称或编码" clearable/>
          </el-col>
          <el-col :span="6">
            <el-input v-model.trim="queryParam.goods" placeholder="请输入物料编码/名称/型号" clearable/>
          </el-col>
          <el-col :span="4">
              <el-form-item>
                <el-select v-model="queryParam.caseStat" placeholder="请选择需求状态" clearable>
                  <el-option :key="item.key" :label="item.value" :value="item.key"
                            v-for="item in caseStatOptions"/>
                </el-select>
              </el-form-item>
          </el-col>
          
          <template v-if="showAll">
            <!-- <el-col :span="4">
              <el-form-item>
                <el-input v-model.trim="queryParam.dept" placeholder="采购组织编码/名称" clearable />
              </el-form-item>
            </el-col> -->
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="demandDates"
                  type="daterange"
                  placeholder="请选择需求日期"
                  range-separator="至"
                  start-placeholder="（需求）开始日期"
                  end-placeholder="（需求）结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <el-date-picker
                  v-model="applyDates"
                  type="daterange"
                  placeholder="请选择申请日期"
                  range-separator="至"
                  start-placeholder="（申请）开始日期"
                  end-placeholder="（申请）结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="4">
              <el-form-item>
                <el-select v-model="queryParam.selectType" placeholder="请选择查询类型" clearable>
                  <el-option :key="item.key" :label="item.value" :value="item.key"
                            v-for="item in selectTypeOptions"/>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="4">
              <el-form-item>
                <el-select v-model="queryParam.demandClassType" placeholder="请选择单据类型" clearable>
                  <el-option :key="item.key" :label="item.value" :value="item.key"
                            v-for="item in demandTypeOption"/>
                </el-select>
              </el-form-item>
            </el-col> -->
          </template>
          <el-col :span="6">
            <el-form-item>
              <!-- 查询按钮 -->
              <el-button type="primary" icon="el-icon-search" @click="search()">
                {{ $t('common.search') }}
              </el-button>
              <!-- 重置按钮 -->
              <el-button icon="el-icon-refresh-right" @click="reset()">
                {{ $t('common.reset') }}
              </el-button>
              <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true"
                         v-if="!showAll">展开
              </el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else>
                收起
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div class="DIAN-common-layout-main DIAN-flex-main">
        <div class="DIAN-common-head">
          <div>

          </div>
          <div class="DIAN-common-head-right">
            <el-button type="primary" @click="addOrUpdateHandle()" icon="el-icon-plus"
                       v-has-per="'base:sampleDemand:save'">
              {{ $t('common.addBtn')}}
            </el-button>
            <el-button type="primary" @click="batchReturnSample()" icon="el-icon-back" :loading="btnLoading" :disabled="selectedDatas.length === 0" v-has-per="'base:sampleDemand:returnSample'">退回PLM</el-button>
            <el-button type="primary" @click="openVendorDialog()" icon="el-icon-plus"
                      :disabled="selectedDatas.length === 0" :loading="btnLoading" v-has-per="'base:sampleDemand:issuedSample'">
              添加供应商
            </el-button>
            <el-button type="primary" @click="openBuyerDialog()" icon="el-icon-user"
                      :disabled="selectedDatas.length === 0" :loading="btnLoading" v-has-per="'base:sampleDemand:updateBuyer'">
              更换采购员
            </el-button>
            <el-button type="primary" @click="batchLocalClosed()" icon="el-icon-check" :loading="btnLoading" :disabled="selectedDatas.length === 0" v-has-per="'base:sampleDemand:localClosed'">
              本地结案
            </el-button>
            <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
              <el-link icon="icon-ym icon-ym-Refresh DIAN-common-head-icon" :underline="false"
                       @click="search()"/>
            </el-tooltip>
            <d-screen-full/>
          </div>
        </div>
        <d-table v-loading="listLoading" :data="list" :hasNO="false" :hasC="true" @selection-change="handleSelectionChange">
          <!-- 物料对应供应商数据展开行 -->
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-table
                :data="props.row.sampleDemandVendorEntityList || []"
                style="width: 75%"
                size="mini"
                border>
                <el-table-column type="index" width="100" label="序号" align="center" sortable/>
                <el-table-column prop="goodsErpCode" label="物料编码" align="center" width="150" show-overflow-tooltip/>
                <el-table-column prop="goodsName" label="物料名称" align="center" width="150" show-overflow-tooltip/>
                <el-table-column prop="vendorCode" label="供应商编码" width="120"></el-table-column>
                <el-table-column prop="vendorName" label="供应商名称" width="180"></el-table-column>
                <el-table-column prop="caseStat" label="结案状态" width="120">
                  <template slot-scope="scope">
                    <span>{{ scope.row.caseStat | commonEnumsTurn('base.CaseStatEnum') }}</span>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="caseDate" label="结案日期" width="150"></el-table-column> -->
                <!-- <el-table-column prop="isReturn" label="是否退回" width="100">
                  <template slot-scope="scope">
                    {{ scope.row.isReturn | commonEnumsTurn('comm.ValidEnum') }}
                  </template>
                </el-table-column> -->
                <el-table-column prop="assigner" label="分配人" width="100" />
                <el-table-column prop="assignDate" label="分配时间" width="150" />
                <el-table-column prop="modifier" label="更新人" width="100" />
                <el-table-column prop="modifyDate" label="更新时间" width="150" />
                <!-- <el-table-column prop="returnRemark" label="退回备注" width="200" show-overflow-tooltip></el-table-column> -->
                <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip></el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column type="index" width="50" label="序号" align="center" sortable />
          <el-table-column prop="orgName" label="组织名称" align="center" show-overflow-tooltip width="150" sortable />
          <el-table-column prop="demandNo" label="需求单号" align="center" show-overflow-tooltip width="150" sortable />
          <el-table-column prop="demandClassType" label="单据类型" align="center" show-overflow-tooltip width="120" sortable>
            <template slot-scope="scope">
              <span>{{ scope.row.demandClassType | commonEnumsTurn('base.DemandClassTypeEnum') }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isNeedUpFile" label="是否需要上传文件" align="center" show-overflow-tooltip width="150" sortable>
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isNeedUpFile === 1">是</el-tag>
              <el-tag v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="applicant" label="申请人" align="center" show-overflow-tooltip width="120" sortable />
          <el-table-column prop="applyDeptName" label="申请部门" show-overflow-tooltip width="120" sortable />
          <el-table-column prop="applyDate" label="申请日期" show-overflow-tooltip width="120" sortable />
          <!-- <el-table-column prop="remark" label="备注" align="center" show-overflow-tooltip width="200" sortable /> -->

          <!-- 物料相关信息 -->
          <el-table-column prop="purName" label="采购员" align="center" show-overflow-tooltip width="100" sortable />
          <el-table-column prop="purCode" label="采购员编码" align="center" show-overflow-tooltip width="120" sortable />
          <el-table-column prop="goodsCode" label="物料编码" align="center" show-overflow-tooltip width="120" sortable />
          <!-- <el-table-column prop="goodsErpCode" label="物料ERP编码" align="center" show-overflow-tooltip width="120" sortable /> -->
          <el-table-column prop="goodsName" label="物料名称" align="center" show-overflow-tooltip width="150" sortable />
          <el-table-column prop="goodsModel" label="物料规格型号" align="center" show-overflow-tooltip width="150" sortable />
          <!-- <el-table-column prop="vendorCode" label="供应商编码" align="center" show-overflow-tooltip width="150" sortable /> -->
          <el-table-column prop="vendorName" label="建议供应商" align="center" show-overflow-tooltip width="150" sortable />
          <el-table-column prop="demandQty" label="需求数量" align="center" width="100" sortable />
          <el-table-column prop="uomName" label="单位" align="center" width="80" sortable />
          <el-table-column prop="demandDate" label="需求日期" align="center" show-overflow-tooltip width="120" sortable>
            <template slot-scope="scope">
              {{ scope.row.demandDate | date }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="saleDeptCode" label="销售部门编码" align="center" show-overflow-tooltip width="150" sortable /> -->
          <el-table-column prop="saleDeptName" label="销售部门名称" align="center" show-overflow-tooltip width="150" sortable />
          <el-table-column prop="model" label="机型" align="center" show-overflow-tooltip width="150" sortable />
          <el-table-column prop="purpose" label="用途" align="center" show-overflow-tooltip width="120" sortable />
          <el-table-column prop="caseStat" label="需求状态" align="center" show-overflow-tooltip width="150" sortable>
                  <template slot-scope="scope">
                    <span>{{ scope.row.caseStat | commonEnumsTurn('base.CaseStatEnum') }}</span>
                  </template>
          </el-table-column>
          <el-table-column prop="returnCause" label="退回原因" width="200" show-overflow-tooltip sortable></el-table-column>
          <el-table-column prop="caseDate" label="结案日期" align="center" show-overflow-tooltip width="150" sortable>
            <template slot-scope="scope">
              {{ scope.row.caseDate | date }}
            </template>
          </el-table-column>
          <el-table-column prop="itemRemark" label="行备注" align="center" show-overflow-tooltip width="200" sortable />
          <el-table-column prop="creater" label="创建人" width="100" sortable />
          <el-table-column prop="createDate" label="创建时间" width="150" sortable />
          <el-table-column prop="modifier" label="更新人" width="100" sortable />
          <el-table-column prop="modifyDate" label="更新时间" width="150" sortable />
          <el-table-column label="操作" fixed="right" align="center" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="addOrUpdateHandle(scope.row.id)" v-has-per="'base:sample:info'">
                {{ $t('common.lookBtn')}}
              </el-button>
            </template>
          </el-table-column>
        </d-table>
        <d-pagination :total="total" :page.sync="queryParam.page"
                    :limit.sync="queryParam.limit" @pagination="initData"/>
      </div>
      <!-- FORM表单 -->
      <Form ref="detail" v-show="detailVisible" @callRefreshList="callRefreshList"></Form>
      <!-- 供应商选择弹窗 -->
      <VendorProp ref="vendor" :singleChoice="false" @callData="vendorSelect"></VendorProp>
      <!-- 用户选择弹窗 -->
      <UserProp ref="userProp" :singleChoice="true" :title="'采购员'" @callData="userSelect"></UserProp>
    </div>
  </div>
</template>

<script>
import {
  getSampleDemandList,
  delSampleDemand,
  batchIssuedSample,
  updateSampleDemand,
  returnSample,
  localClosed,
  batchUpdateBuyer
} from '@/api/base/sampleDemand'
import Form from './form'
import store from "@/store";
import VendorProp from '@/views/popup/base/vendor/vendor'
import UserProp from '@/views/popup/sys/user/userProp'

export default {
  components: {Form, VendorProp, UserProp},
  name: 'base-sampleDemand-tenant',
  data() {
    return {
      list: [],
      total: 0,
      showAll: false,
      queryParam: {//查询条件
        demandNo: '',//需求申请单号
        applicant:'',//申请人
        isNeedUpFile:null,//明细表检验状态
        dept: '' , // 采购组织
        applyDate: '', // 申请日期
        startDate: '', // 开始时间
        endDate: '' , // 结束时间
        demandDate: '', // 需求日期
        demandDateStart: '', // 需求开始日期
        demandDateEnd: '', // 需求结束日期
        selectType: '3', // 默认查询显示全部的数据
        demandClassType: '2', // 默认查询显示采购打样单据
        caseStat: '1', // 默认查询待分配的数据
        page: 1,
        limit: 20,
      },
      detailVisible: false,
      qualityVisible: false,
      listLoading: true,
      btnLoading: false, // 按钮加载状态
      selectedDatas: [],
      sampleDates: [],
      demandDates: [], // 需求日期选择器
      applyDates: [], // 申请日期选择器
      sampleItemStatOptions: store.getters.commonEnums['base.SampleItemEnums'], // 明细行检验状态
      sampleStatOptions: store.getters.commonEnums['base.SampleEnums'], // 需求状态
      validOps: store.getters.commonEnums['comm.ValidEnum'], // 检验状态
      demandTypeOption: store.getters.commonEnums['base.DemandClassTypeEnum'], // 单据类型
      selectTypeOptions: [ // 查询类型
        {
          key: '1',
          value: '未完成'
        },
        {
          key: '2',
          value: '已完成'
        },
        {
          key: '3',
          value: '全部'
        }
      ],
      caseStatOptions: [ // 需求状态
        {
          key: '',
          value: '全部'
        },
        {
          key: '1',
          value: '待分配'
        },
        {
          key: '2',
          value: '已分配'
        },
        {
          key: '3',
          value: '已拒绝'
        },
        {
          key: '4',
          value: '退回'
        },
        {
          key: '5',
          value: '已结案'
        }
      ],
    }
  },
  watch: {
    $route: {
      handler (to, from) {
        // 只处理送样需求单页面且包含id参数的情况
        if (to.path === '/base/sampleDemand/tenant' && to.query.id) {
          this.addOrUpdateHandle(to.query.id);
        }
      },
      immediate: true
    }
  },
  created() {
    this.initData()
  },
  filters:{
    date(time){
      if (!time){
        return ''
      }
      let date = new Date(time)
      let year = date.getFullYear();
      let month = date.getMonth()+1;
      let day = date.getDate();
      return year+"-"+month+"-"+day;
    }
  },
  methods: {

    // 页面初始化加载列表数据
    initData() {
      this.listLoading = true
      let query = {
        ...this.queryParam,
        includeVendors: true // 请求包含供应商信息
      }
      getSampleDemandList(query).then(res => {
        this.total = res.data.totalCount
        this.list = res.data.list
        // 处理供应商信息
        if (this.list && this.list.length > 0) {
          this.list.forEach(item => {
            if (!item.sampleDemandVendorEntityList) {
              item.sampleDemandVendorEntityList = [];
            }
          });
        }

        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    //打开新增/修改弹窗页面
    addOrUpdateHandle(id) {
      this.detailVisible = true;
      this.$nextTick(() => {
        this.$refs.detail.init(id);
      })
    },
    //审核
    handleCheck(id) {

    },
    search() {
      this.queryParam.page=1;

      // 处理申请日期
      if (this.applyDates.length === 2) {
        try {
          const startDate = this.$dian.dateFormat(this.applyDates[0], 'YYYY-MM-DD');
          const endDate = this.$dian.dateFormat(this.applyDates[1], 'YYYY-MM-DD');
          this.queryParam.startDate = startDate;
          this.queryParam.endDate = endDate;
        } catch (error) {
          this.queryParam.startDate = '';
          this.queryParam.endDate = '';
        }
      } else {
        this.queryParam.startDate = '';
        this.queryParam.endDate = '';
      }

      // 处理需求日期
      if (this.demandDates.length === 2) {
        try {
          const demandDateStart = this.$dian.dateFormat(this.demandDates[0], 'YYYY-MM-DD');
          const demandDateEnd = this.$dian.dateFormat(this.demandDates[1], 'YYYY-MM-DD');
          this.queryParam.demandDateStart = demandDateStart;
          this.queryParam.demandDateEnd = demandDateEnd;
        } catch (error) {
          this.queryParam.demandDateStart = '';
          this.queryParam.demandDateEnd = '';
        }
      } else {
        this.queryParam.demandDateStart = '';
        this.queryParam.demandDateEnd = '';
      }

      this.initData()
    },
    reset() {
      this.queryParam = this.$options.data().queryParam;
      this.demandDates = [];
      this.applyDates = [];
      this.initData()
    },
    //删除
    handleDel(id) {
      this.$confirm(this.$t('common.delTip'), this.$t('common.tipTitle'), {
        type: 'warning'
      }).then(() => {
        delSampleDemand(id).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功',
            duration: 1500,
            onClose: () => {
              this.search()
            }
          })
        })
      }).catch(() => {
      })
    },
    callRefreshList() {
      this.detailVisible = false;
      this.qualityVisible = false;
      this.search();
    },
    handleSelectionChange(selection){
      this.selectedDatas = selection.map(item => item)
    },
    quality(row){
      this.qualityVisible = true;
      let isAdd = false;
      if(row.ifInspection > 0){
        isAdd = true
      }
      this.$nextTick(() => {
        this.$refs.quality.init(row.id,isAdd);
      })
    },
    // 打开供应商选择弹窗
    openVendorDialog() {
      if(this.selectedDatas.some(item => item.caseStat == 4)){
        this.$message.error('存在已退回的物料明细记录，不允许添加供应商');
        return;
      }
      if (this.selectedDatas.length === 0) {
        this.$message.error('请先选择物料');
        return;
      }
      
      // 获取组织ID，使用第一个选中项的组织ID
      const orgId = this.selectedDatas[0].orgId;
      if (!orgId) {
        this.$message.error('采购组织信息为空');
        return;
      }
      
      let params = {
        deptIds: orgId,
      };
      
      this.$nextTick(() => {
        this.$refs.vendor.init(params);
      });
    },
    // 处理供应商选择结果
    vendorSelect(vendorData) {
      if (!vendorData || vendorData.length === 0) {
        this.$message.error('未选择任何供应商');
        return;
      }
      
      if (this.selectedDatas.length === 0) {
        this.$message.error('未选择任何物料');
        return;
      }
      
      // 处理供应商数据
      this.processVendorData(vendorData, this.selectedDatas);
    },
    // 批量生成送样通知单
    batchGenerateSampleNotice(sampleIds) {
      this.btnLoading = true;
      
      console.log("批量生成送样通知单 =======> ", sampleIds);
      batchIssuedSample(sampleIds).then(res => {
        this.$message({
          message: "送样通知单生成成功",
          type: 'success',
          duration: 1500,
          onClose: () => {
            this.btnLoading = false;
            this.search(); // 刷新列表
          }
        });
      }).catch(err => {
        this.btnLoading = false;
        this.$message.error('生成送样通知单失败: ' + (err.message || '未知错误'));
      });
    },
    
    // 处理供应商数据
    processVendorData(vendorData, materials) {
      // 存储需要更新的数据
      const updateData = [];
      
      // 遍历所有选中的物料
      for (let i = 0; i < materials.length; i++) {
        const material = materials[i];
        
        // 确保供应商列表存在
        if (!material.sampleDemandVendorEntityList) {
          material.sampleDemandVendorEntityList = [];
        }
        // 初始化物料明细列表
        if (!material.sampleDemandItemEntityList) {
          material.sampleDemandItemEntityList = [];
        }
        
        // 为每个物料添加选中的供应商
        for (let j = 0; j < vendorData.length; j++) {
          const vendor = vendorData[j];
          
          // 检查是否已存在相同的供应商
          const isExist = material.sampleDemandVendorEntityList.some(
            item => item.goodsId === material.goodsId && 
                   item.vendorId === vendor.soureId
          );
          
          // 如果不存在，则填充供应商数据
          if (!isExist) {
            const vendorItem = {
              demandId: material.demandId,
              demandItemId: material.id,
              goodsId: material.goodsId,
              goodsErpCode: material.goodsErpCode,
              goodsName: material.goodsName,
              vendorId: vendor.soureId,
              vendorCode: vendor.vendorErpCode,
              vendorName: vendor.vendorFullName,
              isAssigned: 0,
              isReturn: 0,
              // returnRemark: '',
              returnCause: '',
              caseDate: null,
              caseStat: null,
              isValid: 1,
              deleteFlag: 0,
            };
            
            // 填充物料明细
            const materialItem = {
              id: material.itemId,
              demandId: material.demandId,
              goodsId: material.goodsId,
              goodsErpCode: material.goodsErpCode,
              goodsName: material.goodsName,
              goodsModel: material.goodsModel,
              vendorId: material.vendorId,
              vendorCode: material.vendorCode,
              vendorName: material.vendorName,
              demandDate: material.demandDate,
              demandQty: material.demandQty,
              saleDeptId: material.saleDeptId,
              saleDeptCode: material.saleDeptCode,
              saleDeptName: material.saleDeptName,
              model: material.model,
              purpose: material.purpose,
              uomId: material.uomId,
              uomCode: material.uomCode,
              uomName: material.uomName,
              caseDate: material.caseDate,
              sourceItemId: material.sourceItemId,
              caseStat: material.caseStat,
              isValid: material.itemIsValid,
              deleteFlag: material.itemDeleteFlag,
              remark: material.item_remark,
              createId: material.itemCreateId,
              creater: material.itemCreater,
              createDate: material.itemCreateDate,
              modifiId: material.itemModifiId,
              modifier: material.itemModifier,
              modifyDate: material.itemModifyDate

            };
            material.sampleDemandVendorEntityList.push(vendorItem);
            material.sampleDemandItemEntityList.push(materialItem);
            
            // 添加到需要更新的数据中
            if (!updateData.includes(material)) {
              updateData.push(material);
            }
          }
        }
      }
      
      // 如果有数据需要更新
      if (updateData.length > 0) {
        this.$confirm('是否保存供应商数据并生成送样通知单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.saveSampleVendors(updateData);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });
      } else {
        this.$message.error('没有新的供应商数据需要保存');
      }
    },
    saveSampleVendors(materials) {
      this.btnLoading = true;
      const updatePromises = materials.map(material => {
        console.log("保存参数为=====================>", JSON.stringify(material, null, 2));
        return updateSampleDemand(material);
      });
      
      Promise.all(updatePromises)
        .then(() => {
          this.$message({
            message: "供应商数据保存成功",
            type: 'success',
            duration: 1500
          });
          
          // 保存成功后，调用批量生成送样通知单的方法
          const sampleIds = materials.map(item => item.id);
          this.batchGenerateSampleNotice(sampleIds);
        })
        .catch(err => {
          this.btnLoading = false;
          this.$message.error('保存失败: ' + (err.message || '未知错误'));
        });
    },
    //批量退回PLM
    batchReturnSample(){
      // 检查是否有选中的数据
      if (this.selectedDatas.length === 0) {
        this.$message.error('请至少选择一条需要退回的物料明细记录');
        return;
      }
      // 检查选中的物料是否存在sourceItemId
      if(this.selectedDatas.some(item => !item.sourceItemId)){
        // this.$message.error("存在未关联的物料明细行需求，请先关联PLM物料明细行");
        this.$message.error("存在未关联PLM的物料，只允许退回从PLM下发的物料");
        return;
      }
      // 检查是否已退回
      if(this.selectedDatas.some(item => item.caseStat == 4)){
        this.$message.error('存在已退回的物料明细记录，请勿重复退回');
        return;
      }
      this.$prompt('请输入退回原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入退回原因'
      }).then(({ value }) => {
        if (!value) {
          this.$message.error('退回原因不能为空');
          return;
        }
        // 构造请求参数
        this.btnLoading = true;
        const ids = this.selectedDatas.map(item => item.itemId); // 物料明细ID
        const params = {
          ids: ids, // 物料明细ID数组
          returnCause: value // 退回原因
        };
        returnSample(params).then(res => {
          this.$message({
            message: "退回成功",
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.btnLoading = false;
              this.search(); // 刷新列表
            }
          });
        }).catch(err => {
          this.btnLoading = false;
          this.$message.error('退回失败: ' + (err.message || '未知错误'));
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消退回'
        });
      });
    },
    //批量本地结案
    batchLocalClosed(){
      // 检查是否有选中的数据
      if (this.selectedDatas.length === 0) {
        this.$message.error('请至少选择一条需要结案的物料明细记录');
        return;
      }
      // 检查选中的物料是否存在sourceItemId
      if(this.selectedDatas.some(item => item.sourceItemId)){
        this.$message.error("存在已关联PLM的物料，不允许本地结案");
        return;
      }
      // 检查是否已结案
      if(this.selectedDatas.some(item => item.caseStat == 5)){
        this.$message.error('存在已结案的物料明细记录，请勿重复结案');
        return;
      }
      this.btnLoading = true;
      const ids = this.selectedDatas.map(item => item.itemId); // 物料明细ID
      this.$confirm(`确认结案选中的${this.selectedDatas.length}条记录？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        localClosed(ids).then(res => {
          this.$message({
            message: "结案成功",
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.btnLoading = false;
              this.search(); // 刷新列表
            }
          });
        }).catch(err => {
          this.btnLoading = false;
          this.$message.error('结案失败: ' + (err.message || '未知错误'));
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消结案'
        });
      });
    },
    // 打开采购员选择弹窗
    openBuyerDialog() {
      if (this.selectedDatas.length === 0) {
        this.$message.error('请至少选择一条物料明细记录');
        return;
      }
      
      // 检查是否已退回
      if(this.selectedDatas.some(item => item.caseStat == 4)){
        this.$message.error('存在已退回的物料明细记录，不允许更换采购员');
        return;
      }
      
      this.$nextTick(() => {
        this.$refs.userProp.init();
      });
    },
    
    // 处理用户选择结果
    userSelect(userData) {
      if (!userData || userData.length === 0) {
        this.$message.error('未选择采购员');
        return;
      }
      
      const selectedUser = userData[0];
      console.log('选择的采购员:', selectedUser);
      
      // 弹出确认框
      this.$confirm(`确认将选中的${this.selectedDatas.length}条记录的采购员更换为 ${selectedUser.userName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.updateBuyer(selectedUser);
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消更换采购员'
        });
      });
    },
    
    // 更新采购员信息
    updateBuyer(selectedUser) {
      this.btnLoading = true;
      
      // 获取选中记录的ID列表
      const ids = this.selectedDatas.map(item => item.itemId); // 物料明细行ID
      
      // 构造请求参数
      const params = {
        ids: ids,
        purId: selectedUser.id,
        purCode: selectedUser.userCode,
        purName: selectedUser.userName
      };
      
      batchUpdateBuyer(params).then(res => {
        this.$message({
          message: "采购员更新成功",
          type: 'success',
          duration: 1500,
          onClose: () => {
            this.btnLoading = false;
            this.search(); // 刷新列表
          }
        });
      }).catch(err => {
        this.btnLoading = false;
        this.$message.error('采购员更新失败: ' + (err.message || '未知错误'));
      });
    },
  }
}
</script>
<style></style>
