2025-08-04 09:04:25.747 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 09:04:26.004 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 09:04:26.087 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 09:04:26.183 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 09:04:26.273 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 09:04:26.391 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 09:04:26.447 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 09:04:26.611 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 09:04:26.651 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 09:04:26.724 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 09:04:26.777 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 09:04:26.830 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 09:04:26.876 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 09:04:26.929 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 09:04:26.979 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 09:04:27.037 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 09:04:27.097 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 09:04:27.145 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 09:04:27.191 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 09:04:27.253 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 09:04:27.301 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 09:04:27.355 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 09:04:27.425 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 09:04:27.475 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 09:04:27.556 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 09:04:27.610 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 09:04:27.661 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 09:04:27.726 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 09:04:27.838 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 09:04:27.900 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 09:04:27.952 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 09:04:28.009 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 09:04:28.073 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 09:04:28.131 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 09:04:28.191 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 09:04:28.236 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 09:04:28.296 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 09:04:28.357 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 09:04:28.403 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 09:04:28.456 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 09:04:28.502 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 09:04:28.540 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 09:04:28.588 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 09:04:28.631 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 09:04:28.679 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 09:04:28.814 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 09:04:28.902 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 09:04:28.954 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 09:04:29.054 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 09:04:29.119 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 09:04:29.179 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 09:04:29.224 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 09:04:29.266 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 09:04:29.388 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 09:04:29.470 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 09:04:29.527 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 09:04:29.572 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 09:04:29.606 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 09:04:29.671 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 09:04:29.715 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 09:04:29.767 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 09:04:29.811 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 09:04:29.901 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 09:04:29.941 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 09:04:29.976 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 09:04:30.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 09:04:30.057 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 09:04:30.099 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 09:04:30.130 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 09:04:30.162 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 09:04:30.213 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 09:04:30.249 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 09:04:30.331 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 09:04:30.384 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 09:04:30.432 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 09:04:30.482 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 09:04:30.528 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 09:04:30.572 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 09:04:30.619 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 09:04:30.728 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 09:04:30.795 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 09:04:30.851 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 09:04:30.899 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 09:04:31.053 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 09:04:31.171 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 09:04:31.229 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 09:04:31.281 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 09:04:31.326 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 09:04:31.489 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 09:04:31.631 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 09:04:31.694 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 09:04:31.739 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 09:04:31.871 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 09:04:31.971 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 09:04:32.045 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 09:04:32.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 09:04:32.207 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 09:04:32.269 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 09:04:32.337 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 09:04:32.451 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 09:04:32.528 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 09:04:32.589 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 09:04:32.635 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 09:04:32.678 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 09:04:32.796 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 09:04:32.906 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 09:04:32.963 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 09:04:33.010 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 09:04:33.065 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 09:04:33.116 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 09:04:33.149 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 09:04:33.209 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 09:04:33.274 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 09:04:33.330 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 09:04:33.412 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 09:04:33.483 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 09:04:33.535 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 09:04:33.593 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 09:04:33.642 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 09:04:33.687 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 09:04:33.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 09:04:33.791 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 09:04:33.825 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 09:04:33.868 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 09:04:33.911 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 09:04:33.952 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 09:04:33.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 09:04:34.034 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 09:04:34.065 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 09:04:34.145 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 09:04:34.198 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 09:04:34.246 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 09:04:34.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 09:04:34.340 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 09:04:34.391 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 09:04:34.426 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 09:04:34.470 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 09:04:34.517 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 09:04:34.564 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 09:04:34.618 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 09:04:34.783 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 09:04:34.903 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 09:04:34.970 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 09:04:35.021 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 09:04:35.065 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 09:04:35.098 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 09:04:35.147 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 09:04:35.197 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 09:04:35.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 09:04:35.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 09:04:35.328 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 09:04:35.358 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 09:04:35.400 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 09:04:35.445 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 09:04:35.504 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 09:04:35.564 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 09:04:35.620 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 09:04:35.664 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 09:04:35.706 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 09:04:35.769 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 09:04:35.800 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 09:04:35.843 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 09:04:35.907 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 09:04:35.960 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 09:04:36.007 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 09:04:36.078 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 09:04:36.125 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 09:04:36.185 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 09:04:36.227 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 09:04:36.324 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 09:04:36.382 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 09:04:36.423 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 09:04:36.479 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 09:04:36.534 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 09:04:36.594 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 09:04:36.691 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 09:04:36.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 09:04:36.788 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 09:04:36.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 09:04:36.895 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 09:04:36.960 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 09:04:37.004 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 09:04:37.048 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 09:04:37.105 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 09:04:37.160 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 09:04:37.210 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 09:04:37.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 09:04:37.330 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 09:04:37.365 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 09:04:37.406 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 09:04:37.453 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 09:04:37.499 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 09:04:37.542 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 09:04:37.605 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 09:04:37.650 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 09:04:37.726 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 09:04:37.897 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 09:04:37.955 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 09:04:39.321 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 09:04:39.356 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 09:04:39.399 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 09:04:39.439 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 09:04:39.473 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 09:04:39.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 09:04:39.553 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 09:04:39.611 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 09:04:39.642 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 09:04:39.676 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 09:04:39.729 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 09:04:39.791 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 09:04:39.856 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 09:04:39.894 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 09:04:39.928 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 09:04:39.990 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 09:04:40.046 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 09:04:40.100 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 09:04:40.193 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 09:04:40.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 09:04:40.300 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 09:04:40.395 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 09:04:40.473 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 09:04:40.575 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 09:04:40.620 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 09:04:40.660 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 09:04:40.696 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 09:04:40.739 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 09:04:40.782 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 09:04:40.870 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 09:05:52.621 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 09:05:57.334 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 41077 mb of usable space. - resetting to maximum available disk space: 41077 mb
2025-08-04 09:06:48.409 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 13:50:34.379 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-04 13:52:19.072 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 13:52:19.270 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 13:52:19.345 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 13:52:19.422 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 13:52:19.488 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 13:52:19.581 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 13:52:19.633 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 13:52:19.796 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 13:52:19.841 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 13:52:19.892 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 13:52:19.928 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 13:52:19.974 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 13:52:20.020 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 13:52:20.071 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 13:52:20.096 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 13:52:20.161 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 13:52:20.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 13:52:20.246 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 13:52:20.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 13:52:20.329 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 13:52:20.361 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 13:52:20.396 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 13:52:20.446 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 13:52:20.496 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 13:52:20.561 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 13:52:20.596 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 13:52:20.642 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 13:52:20.689 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 13:52:20.790 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 13:52:20.838 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 13:52:20.871 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 13:52:20.911 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 13:52:20.946 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 13:52:20.996 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 13:52:21.046 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 13:52:21.094 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 13:52:21.136 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 13:52:21.186 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 13:52:21.228 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 13:52:21.287 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 13:52:21.346 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 13:52:21.412 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 13:52:21.482 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 13:52:21.546 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 13:52:21.596 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 13:52:21.712 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 13:52:21.782 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 13:52:21.824 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 13:52:21.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 13:52:21.896 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 13:52:21.946 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 13:52:21.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 13:52:22.036 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 13:52:22.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 13:52:22.189 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 13:52:22.223 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 13:52:22.246 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 13:52:22.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 13:52:22.312 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 13:52:22.346 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 13:52:22.385 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 13:52:22.412 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 13:52:22.483 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 13:52:22.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 13:52:22.546 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 13:52:22.591 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 13:52:22.611 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 13:52:22.662 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 13:52:22.694 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 13:52:22.722 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 13:52:22.747 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 13:52:22.777 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 13:52:22.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 13:52:22.845 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 13:52:22.875 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 13:52:22.896 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 13:52:22.941 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 13:52:22.986 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 13:52:23.012 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 13:52:23.097 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 13:52:23.195 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 13:52:23.247 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 13:52:23.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 13:52:23.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 13:52:23.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 13:52:23.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 13:52:23.636 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 13:52:23.689 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 13:52:23.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 13:52:23.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 13:52:23.997 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 13:52:24.038 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 13:52:24.162 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 13:52:24.233 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 13:52:24.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 13:52:24.391 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 13:52:24.447 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 13:52:24.495 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 13:52:24.538 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 13:52:24.613 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 13:52:24.677 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 13:52:24.723 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 13:52:24.762 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 13:52:24.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 13:52:24.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 13:52:25.042 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 13:52:25.097 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 13:52:25.137 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 13:52:25.195 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 13:52:25.243 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 13:52:25.329 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 13:52:25.384 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 13:52:25.428 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 13:52:25.473 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 13:52:25.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 13:52:25.562 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 13:52:25.613 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 13:52:25.662 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 13:52:25.721 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 13:52:25.762 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 13:52:25.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 13:52:25.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 13:52:25.893 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 13:52:25.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 13:52:25.991 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 13:52:26.033 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 13:52:26.072 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 13:52:26.099 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 13:52:26.147 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 13:52:26.213 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 13:52:26.272 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 13:52:26.315 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 13:52:26.347 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 13:52:26.397 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 13:52:26.447 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 13:52:26.495 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 13:52:26.537 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 13:52:26.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 13:52:26.625 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 13:52:26.663 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 13:52:26.797 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 13:52:26.863 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 13:52:26.922 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 13:52:26.963 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 13:52:26.997 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 13:52:27.047 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 13:52:27.090 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 13:52:27.132 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 13:52:27.174 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 13:52:27.197 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 13:52:27.247 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 13:52:27.338 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 13:52:27.379 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 13:52:27.424 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 13:52:27.479 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 13:52:27.531 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 13:52:27.586 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 13:52:27.612 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 13:52:27.647 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 13:52:27.713 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 13:52:27.772 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 13:52:27.813 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 13:52:27.880 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 13:52:27.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 13:52:27.993 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 13:52:28.047 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 13:52:28.097 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 13:52:28.163 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 13:52:28.197 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 13:52:28.313 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 13:52:28.363 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 13:52:28.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 13:52:28.439 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 13:52:28.484 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 13:52:28.527 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 13:52:28.576 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 13:52:28.629 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 13:52:28.683 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 13:52:28.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 13:52:28.763 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 13:52:28.833 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 13:52:28.886 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 13:52:28.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 13:52:28.986 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 13:52:29.035 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 13:52:29.088 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 13:52:29.163 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 13:52:29.198 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 13:52:29.248 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 13:52:29.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 13:52:29.327 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 13:52:29.382 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 13:52:29.424 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 13:52:29.522 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 13:52:29.579 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 13:52:29.648 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 13:52:29.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 13:52:29.855 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 13:52:31.191 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 13:52:31.234 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 13:52:31.319 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 13:52:31.361 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 13:52:31.392 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 13:52:31.434 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 13:52:31.474 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 13:52:31.530 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 13:52:31.559 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 13:52:31.591 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 13:52:31.625 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 13:52:31.664 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 13:52:31.712 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 13:52:31.750 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 13:52:31.782 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 13:52:31.843 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 13:52:31.876 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 13:52:31.912 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 13:52:31.978 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 13:52:32.013 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 13:52:32.057 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 13:52:32.140 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 13:52:32.213 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 13:52:32.310 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 13:52:32.353 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 13:52:32.392 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 13:52:32.426 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 13:52:32.459 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 13:52:32.491 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 13:52:32.554 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 13:53:27.410 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 13:53:30.800 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40041 mb of usable space. - resetting to maximum available disk space: 40041 mb
2025-08-04 13:54:26.089 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 14:08:22.608 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-04 14:09:04.834 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 14:09:05.022 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 14:09:05.096 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 14:09:05.159 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 14:09:05.210 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 14:09:05.295 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 14:09:05.346 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 14:09:05.475 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 14:09:05.508 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 14:09:05.548 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 14:09:05.591 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 14:09:05.627 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 14:09:05.662 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 14:09:05.694 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 14:09:05.733 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 14:09:05.792 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 14:09:05.837 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 14:09:05.873 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 14:09:05.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 14:09:05.965 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 14:09:05.999 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 14:09:06.043 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 14:09:06.075 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 14:09:06.106 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 14:09:06.149 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 14:09:06.181 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 14:09:06.216 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 14:09:06.261 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 14:09:06.336 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 14:09:06.374 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 14:09:06.406 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 14:09:06.443 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 14:09:06.479 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 14:09:06.509 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 14:09:06.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 14:09:06.571 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 14:09:06.601 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 14:09:06.638 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 14:09:06.674 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 14:09:06.720 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 14:09:06.755 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 14:09:06.817 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 14:09:06.859 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 14:09:06.889 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 14:09:06.927 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 14:09:07.031 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 14:09:07.092 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 14:09:07.134 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 14:09:07.182 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 14:09:07.213 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 14:09:07.244 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 14:09:07.274 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 14:09:07.303 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 14:09:07.397 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 14:09:07.434 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 14:09:07.463 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 14:09:07.492 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 14:09:07.523 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 14:09:07.560 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 14:09:07.595 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 14:09:07.631 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 14:09:07.661 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 14:09:07.724 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 14:09:07.793 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 14:09:07.824 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 14:09:07.869 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 14:09:07.896 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 14:09:07.937 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 14:09:07.975 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 14:09:08.002 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 14:09:08.029 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 14:09:08.056 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 14:09:08.087 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 14:09:08.122 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 14:09:08.152 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 14:09:08.187 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 14:09:08.217 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 14:09:08.252 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 14:09:08.278 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 14:09:08.340 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 14:09:08.376 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 14:09:08.417 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 14:09:08.449 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 14:09:08.536 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 14:09:08.632 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 14:09:08.679 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 14:09:08.753 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 14:09:08.785 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 14:09:08.913 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 14:09:08.982 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 14:09:09.026 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 14:09:09.057 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 14:09:09.165 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 14:09:09.237 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 14:09:09.293 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 14:09:09.370 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 14:09:09.418 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 14:09:09.451 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 14:09:09.482 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 14:09:09.545 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 14:09:09.593 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 14:09:09.630 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 14:09:09.664 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 14:09:09.695 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 14:09:09.803 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 14:09:09.910 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 14:09:09.965 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 14:09:10.044 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 14:09:10.089 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 14:09:10.119 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 14:09:10.149 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 14:09:10.191 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 14:09:10.223 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 14:09:10.255 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 14:09:10.291 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 14:09:10.340 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 14:09:10.378 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 14:09:10.415 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 14:09:10.452 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 14:09:10.490 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 14:09:10.527 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 14:09:10.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 14:09:10.588 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 14:09:10.628 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 14:09:10.661 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 14:09:10.690 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 14:09:10.724 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 14:09:10.759 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 14:09:10.789 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 14:09:10.852 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 14:09:10.893 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 14:09:10.929 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 14:09:10.966 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 14:09:11.010 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 14:09:11.051 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 14:09:11.085 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 14:09:11.114 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 14:09:11.190 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 14:09:11.243 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 14:09:11.275 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 14:09:11.400 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 14:09:11.461 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 14:09:11.500 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 14:09:11.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 14:09:11.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 14:09:11.609 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 14:09:11.661 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 14:09:11.693 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 14:09:11.742 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 14:09:11.769 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 14:09:11.801 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 14:09:11.833 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 14:09:11.873 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 14:09:11.907 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 14:09:11.957 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 14:09:12.006 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 14:09:12.055 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 14:09:12.094 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 14:09:12.125 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 14:09:12.173 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 14:09:12.203 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 14:09:12.235 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 14:09:12.286 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 14:09:12.332 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 14:09:12.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 14:09:12.416 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 14:09:12.453 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 14:09:12.501 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 14:09:12.537 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 14:09:12.647 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 14:09:12.684 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 14:09:12.718 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 14:09:12.755 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 14:09:12.788 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 14:09:12.821 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 14:09:12.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 14:09:12.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 14:09:12.916 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 14:09:12.953 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 14:09:12.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 14:09:13.035 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 14:09:13.067 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 14:09:13.099 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 14:09:13.145 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 14:09:13.195 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 14:09:13.235 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 14:09:13.301 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 14:09:13.337 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 14:09:13.370 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 14:09:13.401 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 14:09:13.434 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 14:09:13.467 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 14:09:13.500 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 14:09:13.551 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 14:09:13.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 14:09:13.632 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 14:09:13.739 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 14:09:13.775 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 14:09:14.905 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 14:09:14.939 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 14:09:14.980 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 14:09:15.017 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 14:09:15.051 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 14:09:15.133 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 14:09:15.177 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 14:09:15.234 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 14:09:15.263 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 14:09:15.295 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 14:09:15.329 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 14:09:15.370 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 14:09:15.416 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 14:09:15.456 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 14:09:15.487 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 14:09:15.546 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 14:09:15.579 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 14:09:15.615 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 14:09:15.683 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 14:09:15.719 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 14:09:15.761 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 14:09:15.848 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 14:09:15.922 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 14:09:16.024 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 14:09:16.066 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 14:09:16.106 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 14:09:16.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 14:09:16.175 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 14:09:16.206 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 14:09:16.269 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 14:10:05.670 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 14:10:08.963 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40040 mb of usable space. - resetting to maximum available disk space: 40040 mb
2025-08-04 14:10:41.636 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 14:16:38.231 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-04 14:17:18.252 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 14:17:18.456 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 14:17:18.526 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 14:17:18.594 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 14:17:18.682 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 14:17:18.769 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 14:17:18.811 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 14:17:18.937 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 14:17:18.974 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 14:17:19.012 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 14:17:19.048 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 14:17:19.083 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 14:17:19.116 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 14:17:19.152 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 14:17:19.200 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 14:17:19.243 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 14:17:19.279 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 14:17:19.311 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 14:17:19.342 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 14:17:19.379 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 14:17:19.419 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 14:17:19.458 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 14:17:19.491 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 14:17:19.522 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 14:17:19.567 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 14:17:19.604 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 14:17:19.647 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 14:17:19.682 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 14:17:19.763 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 14:17:19.802 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 14:17:19.846 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 14:17:19.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 14:17:19.949 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 14:17:19.981 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 14:17:20.013 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 14:17:20.041 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 14:17:20.072 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 14:17:20.115 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 14:17:20.145 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 14:17:20.178 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 14:17:20.211 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 14:17:20.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 14:17:20.277 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 14:17:20.312 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 14:17:20.347 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 14:17:20.448 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 14:17:20.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 14:17:20.538 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 14:17:20.572 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 14:17:20.604 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 14:17:20.632 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 14:17:20.661 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 14:17:20.689 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 14:17:20.784 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 14:17:20.821 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 14:17:20.852 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 14:17:20.880 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 14:17:20.908 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 14:17:20.940 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 14:17:20.975 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 14:17:21.013 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 14:17:21.052 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 14:17:21.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 14:17:21.178 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 14:17:21.207 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 14:17:21.256 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 14:17:21.284 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 14:17:21.324 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 14:17:21.353 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 14:17:21.380 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 14:17:21.405 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 14:17:21.432 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 14:17:21.464 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 14:17:21.499 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 14:17:21.532 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 14:17:21.563 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 14:17:21.590 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 14:17:21.626 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 14:17:21.652 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 14:17:21.718 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 14:17:21.752 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 14:17:21.787 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 14:17:21.817 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 14:17:21.903 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 14:17:22.002 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 14:17:22.044 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 14:17:22.085 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 14:17:22.118 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 14:17:22.242 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 14:17:22.320 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 14:17:22.370 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 14:17:22.410 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 14:17:22.555 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 14:17:22.621 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 14:17:22.675 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 14:17:22.750 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 14:17:22.793 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 14:17:22.826 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 14:17:22.856 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 14:17:22.920 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 14:17:22.959 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 14:17:22.992 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 14:17:23.038 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 14:17:23.077 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 14:17:23.194 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 14:17:23.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 14:17:23.339 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 14:17:23.374 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 14:17:23.414 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 14:17:23.446 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 14:17:23.478 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 14:17:23.535 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 14:17:23.578 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 14:17:23.614 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 14:17:23.651 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 14:17:23.696 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 14:17:23.734 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 14:17:23.788 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 14:17:23.829 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 14:17:23.870 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 14:17:23.908 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 14:17:23.940 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 14:17:23.972 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 14:17:24.024 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 14:17:24.105 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 14:17:24.140 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 14:17:24.170 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 14:17:24.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 14:17:24.254 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 14:17:24.345 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 14:17:24.384 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 14:17:24.432 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 14:17:24.469 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 14:17:24.515 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 14:17:24.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 14:17:24.606 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 14:17:24.637 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 14:17:24.669 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 14:17:24.702 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 14:17:24.732 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 14:17:24.862 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 14:17:24.908 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 14:17:24.946 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 14:17:24.979 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 14:17:25.011 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 14:17:25.040 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 14:17:25.071 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 14:17:25.107 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 14:17:25.139 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 14:17:25.167 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 14:17:25.198 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 14:17:25.226 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 14:17:25.254 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 14:17:25.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 14:17:25.343 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 14:17:25.380 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 14:17:25.424 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 14:17:25.455 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 14:17:25.486 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 14:17:25.544 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 14:17:25.577 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 14:17:25.613 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 14:17:25.673 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 14:17:25.756 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 14:17:25.795 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 14:17:25.846 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 14:17:25.883 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 14:17:25.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 14:17:25.961 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 14:17:26.030 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 14:17:26.064 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 14:17:26.099 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 14:17:26.133 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 14:17:26.167 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 14:17:26.205 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 14:17:26.237 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 14:17:26.267 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 14:17:26.297 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 14:17:26.328 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 14:17:26.359 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 14:17:26.405 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 14:17:26.441 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 14:17:26.472 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 14:17:26.515 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 14:17:26.557 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 14:17:26.592 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 14:17:26.663 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 14:17:26.695 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 14:17:26.726 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 14:17:26.756 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 14:17:26.782 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 14:17:26.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 14:17:26.842 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 14:17:26.891 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 14:17:26.927 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 14:17:26.976 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 14:17:27.073 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 14:17:27.103 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 14:17:28.246 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 14:17:28.279 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 14:17:28.319 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 14:17:28.359 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 14:17:28.425 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 14:17:28.462 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 14:17:28.509 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 14:17:28.564 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 14:17:28.596 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 14:17:28.627 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 14:17:28.663 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 14:17:28.705 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 14:17:28.752 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 14:17:28.789 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 14:17:28.820 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 14:17:28.880 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 14:17:28.911 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 14:17:28.949 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 14:17:29.023 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 14:17:29.060 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 14:17:29.106 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 14:17:29.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 14:17:29.268 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 14:17:29.368 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 14:17:29.411 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 14:17:29.451 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 14:17:29.486 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 14:17:29.519 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 14:17:29.551 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 14:17:29.615 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 14:18:21.515 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 14:18:26.594 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40040 mb of usable space. - resetting to maximum available disk space: 40040 mb
2025-08-04 14:19:04.457 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 15:07:22.380 [ActiveMQ Connection Executor: vm://localhost#0] WARN  o.s.jms.connection.CachingConnectionFactory - Could not close shared JMS Connection
javax.jms.JMSException: Disposed due to prior exception
	at org.apache.activemq.util.JMSExceptionSupport.create(JMSExceptionSupport.java:72)
	at org.apache.activemq.ActiveMQConnection.syncSendPacket(ActiveMQConnection.java:1421)
	at org.apache.activemq.ActiveMQConnection.close(ActiveMQConnection.java:688)
	at org.springframework.jms.connection.SingleConnectionFactory.closeConnection(SingleConnectionFactory.java:501)
	at org.springframework.jms.connection.SingleConnectionFactory.resetConnection(SingleConnectionFactory.java:389)
	at org.springframework.jms.connection.CachingConnectionFactory.resetConnection(CachingConnectionFactory.java:205)
	at org.springframework.jms.connection.SingleConnectionFactory.onException(SingleConnectionFactory.java:367)
	at org.springframework.jms.connection.SingleConnectionFactory$AggregatedExceptionListener.onException(SingleConnectionFactory.java:721)
	at org.apache.activemq.ActiveMQConnection$5.run(ActiveMQConnection.java:1967)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.activemq.transport.TransportDisposedIOException: Disposed due to prior exception
	at org.apache.activemq.transport.ResponseCorrelator.onException(ResponseCorrelator.java:125)
	at org.apache.activemq.transport.TransportFilter.onException(TransportFilter.java:114)
	at org.apache.activemq.transport.vm.VMTransport.stop(VMTransport.java:233)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.TransportFilter.stop(TransportFilter.java:72)
	at org.apache.activemq.transport.ResponseCorrelator.stop(ResponseCorrelator.java:132)
	at org.apache.activemq.broker.TransportConnection.doStop(TransportConnection.java:1194)
	at org.apache.activemq.broker.TransportConnection$4.run(TransportConnection.java:1160)
	... 3 common frames omitted
Caused by: org.apache.activemq.transport.TransportDisposedIOException: peer (vm://localhost#1) stopped.
	... 9 common frames omitted
2025-08-04 15:08:04.849 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 15:08:05.039 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 15:08:05.105 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 15:08:05.169 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 15:08:05.222 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 15:08:05.306 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 15:08:05.352 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 15:08:05.504 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 15:08:05.549 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 15:08:05.602 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 15:08:05.642 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 15:08:05.687 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 15:08:05.732 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 15:08:05.773 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 15:08:05.818 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 15:08:05.872 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 15:08:05.915 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 15:08:05.954 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 15:08:05.993 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 15:08:06.038 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 15:08:06.081 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 15:08:06.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 15:08:06.165 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 15:08:06.202 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 15:08:06.285 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 15:08:06.330 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 15:08:06.371 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 15:08:06.414 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 15:08:06.503 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 15:08:06.544 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 15:08:06.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 15:08:06.616 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 15:08:06.655 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 15:08:06.687 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 15:08:06.720 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 15:08:06.747 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 15:08:06.776 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 15:08:06.813 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 15:08:06.849 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 15:08:06.889 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 15:08:06.925 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 15:08:06.960 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 15:08:07.025 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 15:08:07.054 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 15:08:07.092 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 15:08:07.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 15:08:07.256 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 15:08:07.298 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 15:08:07.336 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 15:08:07.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 15:08:07.395 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 15:08:07.425 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 15:08:07.454 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 15:08:07.546 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 15:08:07.586 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 15:08:07.617 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 15:08:07.646 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 15:08:07.701 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 15:08:07.733 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 15:08:07.771 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 15:08:07.810 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 15:08:07.840 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 15:08:07.898 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 15:08:07.931 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 15:08:07.961 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 15:08:08.006 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 15:08:08.039 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 15:08:08.082 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 15:08:08.110 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 15:08:08.136 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 15:08:08.163 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 15:08:08.191 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 15:08:08.224 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 15:08:08.264 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 15:08:08.295 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 15:08:08.326 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 15:08:08.354 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 15:08:08.388 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 15:08:08.415 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 15:08:08.512 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 15:08:08.544 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 15:08:08.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 15:08:08.609 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 15:08:08.704 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 15:08:08.804 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 15:08:08.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 15:08:08.888 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 15:08:08.919 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 15:08:09.050 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 15:08:09.120 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 15:08:09.162 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 15:08:09.216 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 15:08:09.325 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 15:08:09.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 15:08:09.456 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 15:08:09.532 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 15:08:09.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 15:08:09.612 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 15:08:09.643 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 15:08:09.707 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 15:08:09.753 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 15:08:09.791 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 15:08:09.825 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 15:08:09.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 15:08:09.958 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 15:08:10.061 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 15:08:10.105 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 15:08:10.139 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 15:08:10.180 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 15:08:10.217 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 15:08:10.292 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 15:08:10.335 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 15:08:10.367 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 15:08:10.401 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 15:08:10.435 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 15:08:10.481 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 15:08:10.520 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 15:08:10.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 15:08:10.594 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 15:08:10.633 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 15:08:10.671 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 15:08:10.703 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 15:08:10.737 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 15:08:10.779 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 15:08:10.813 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 15:08:10.844 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 15:08:10.880 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 15:08:10.915 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 15:08:10.947 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 15:08:11.012 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 15:08:11.053 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 15:08:11.092 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 15:08:11.123 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 15:08:11.188 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 15:08:11.225 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 15:08:11.256 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 15:08:11.290 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 15:08:11.322 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 15:08:11.354 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 15:08:11.384 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 15:08:11.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 15:08:11.556 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 15:08:11.594 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 15:08:11.628 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 15:08:11.661 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 15:08:11.691 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 15:08:11.725 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 15:08:11.759 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 15:08:11.792 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 15:08:11.819 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 15:08:11.849 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 15:08:11.877 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 15:08:11.904 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 15:08:11.937 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 15:08:11.990 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 15:08:12.025 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 15:08:12.070 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 15:08:12.102 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 15:08:12.131 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 15:08:12.198 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 15:08:12.261 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 15:08:12.293 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 15:08:12.341 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 15:08:12.380 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 15:08:12.416 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 15:08:12.470 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 15:08:12.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 15:08:12.553 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 15:08:12.583 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 15:08:12.644 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 15:08:12.681 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 15:08:12.714 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 15:08:12.747 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 15:08:12.780 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 15:08:12.813 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 15:08:12.845 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 15:08:12.881 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 15:08:12.917 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 15:08:12.950 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 15:08:12.985 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 15:08:13.028 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 15:08:13.060 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 15:08:13.092 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 15:08:13.144 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 15:08:13.188 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 15:08:13.256 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 15:08:13.325 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 15:08:13.365 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 15:08:13.399 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 15:08:13.429 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 15:08:13.456 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 15:08:13.485 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 15:08:13.516 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 15:08:13.573 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 15:08:13.605 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 15:08:13.653 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 15:08:13.751 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 15:08:13.779 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 15:08:14.924 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 15:08:14.958 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 15:08:14.999 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 15:08:15.042 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 15:08:15.080 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 15:08:15.116 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 15:08:15.156 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 15:08:15.217 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 15:08:15.247 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 15:08:15.280 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 15:08:15.315 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 15:08:15.348 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 15:08:15.394 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 15:08:15.434 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 15:08:15.465 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 15:08:15.524 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 15:08:15.557 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 15:08:15.588 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 15:08:15.663 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 15:08:15.700 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 15:08:15.780 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 15:08:15.855 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 15:08:15.934 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 15:08:16.035 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 15:08:16.076 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 15:08:16.112 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 15:08:16.148 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 15:08:16.186 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 15:08:16.221 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 15:08:16.286 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 15:09:07.018 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 15:09:11.259 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40035 mb of usable space. - resetting to maximum available disk space: 40035 mb
2025-08-04 15:09:45.163 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 17:44:47.237 [http-nio-7003-exec-6] WARN  com.aliyun.oss - <0><1952304652952981504> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:44:47.839 [http-nio-7003-exec-6] WARN  com.aliyun.oss - <0><1952304652952981504> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:44:49.053 [http-nio-7003-exec-6] WARN  com.aliyun.oss - <0><1952304652952981504> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:44:51.460 [http-nio-7003-exec-6] WARN  com.aliyun.oss - <0><1952304652952981504> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:45:14.727 [http-nio-7003-exec-4] WARN  com.aliyun.oss - <0><1952304768959041536> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:45:15.343 [http-nio-7003-exec-4] WARN  com.aliyun.oss - <0><1952304768959041536> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:45:16.558 [http-nio-7003-exec-4] WARN  com.aliyun.oss - <0><1952304768959041536> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:45:18.974 [http-nio-7003-exec-4] WARN  com.aliyun.oss - <0><1952304768959041536> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:57:36.307 [http-nio-7003-exec-10] WARN  com.aliyun.oss - <0><1952307877785554944> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:57:36.915 [http-nio-7003-exec-10] WARN  com.aliyun.oss - <0><1952307877785554944> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:57:38.118 [http-nio-7003-exec-10] WARN  com.aliyun.oss - <0><1952307877785554944> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 17:57:40.520 [http-nio-7003-exec-10] WARN  com.aliyun.oss - <0><1952307877785554944> [Client]Unable to execute HTTP request: UnknownHost
2025-08-04 18:05:57.680 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-04 18:07:38.058 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-04 18:09:51.315 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-04 18:11:29.322 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-04 20:00:20.257 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-04 20:11:59.565 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 20:11:59.758 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 20:11:59.851 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 20:11:59.913 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 20:11:59.963 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 20:12:00.052 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 20:12:00.096 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 20:12:00.221 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 20:12:00.256 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 20:12:00.296 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 20:12:00.331 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 20:12:00.366 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 20:12:00.399 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 20:12:00.431 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 20:12:00.469 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 20:12:00.513 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 20:12:00.548 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 20:12:00.585 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 20:12:00.618 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 20:12:00.676 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 20:12:00.710 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 20:12:00.747 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 20:12:00.780 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 20:12:00.815 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 20:12:00.859 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 20:12:00.892 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 20:12:00.925 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 20:12:00.961 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 20:12:01.035 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 20:12:01.071 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 20:12:01.102 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 20:12:01.133 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 20:12:01.163 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 20:12:01.192 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 20:12:01.224 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 20:12:01.253 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 20:12:01.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 20:12:01.318 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 20:12:01.348 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 20:12:01.381 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 20:12:01.421 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 20:12:01.452 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 20:12:01.489 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 20:12:01.519 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 20:12:01.577 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 20:12:01.679 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 20:12:01.740 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 20:12:01.770 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 20:12:01.804 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 20:12:01.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 20:12:01.864 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 20:12:01.893 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 20:12:01.920 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 20:12:02.004 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 20:12:02.048 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 20:12:02.079 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 20:12:02.109 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 20:12:02.137 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 20:12:02.168 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 20:12:02.203 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 20:12:02.238 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 20:12:02.267 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 20:12:02.326 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 20:12:02.358 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 20:12:02.387 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 20:12:02.429 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 20:12:02.462 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 20:12:02.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 20:12:02.532 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 20:12:02.589 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 20:12:02.615 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 20:12:02.641 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 20:12:02.673 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 20:12:02.702 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 20:12:02.730 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 20:12:02.759 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 20:12:02.787 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 20:12:02.819 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 20:12:02.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 20:12:02.904 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 20:12:02.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 20:12:02.971 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 20:12:03.000 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 20:12:03.094 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 20:12:03.186 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 20:12:03.233 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 20:12:03.275 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 20:12:03.306 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 20:12:03.434 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 20:12:03.534 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 20:12:03.576 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 20:12:03.607 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 20:12:03.722 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 20:12:03.789 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 20:12:03.854 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 20:12:03.937 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 20:12:03.981 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 20:12:04.022 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 20:12:04.061 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 20:12:04.139 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 20:12:04.194 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 20:12:04.234 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 20:12:04.279 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 20:12:04.322 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 20:12:04.450 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 20:12:04.566 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 20:12:04.610 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 20:12:04.643 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 20:12:04.683 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 20:12:04.718 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 20:12:04.771 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 20:12:04.873 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 20:12:04.937 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 20:12:04.994 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 20:12:05.055 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 20:12:05.147 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 20:12:05.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 20:12:05.301 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 20:12:05.335 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 20:12:05.373 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 20:12:05.405 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 20:12:05.437 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 20:12:05.468 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 20:12:05.519 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 20:12:05.565 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 20:12:05.624 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 20:12:05.664 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 20:12:05.702 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 20:12:05.752 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 20:12:05.820 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 20:12:05.864 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 20:12:05.899 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 20:12:05.932 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 20:12:05.978 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 20:12:06.018 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 20:12:06.049 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 20:12:06.080 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 20:12:06.109 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 20:12:06.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 20:12:06.170 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 20:12:06.287 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 20:12:06.339 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 20:12:06.377 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 20:12:06.413 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 20:12:06.450 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 20:12:06.487 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 20:12:06.522 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 20:12:06.595 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 20:12:06.627 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 20:12:06.655 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 20:12:06.685 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 20:12:06.712 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 20:12:06.741 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 20:12:06.772 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 20:12:06.816 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 20:12:06.850 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 20:12:06.901 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 20:12:06.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 20:12:06.967 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 20:12:07.015 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 20:12:07.046 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 20:12:07.078 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 20:12:07.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 20:12:07.164 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 20:12:07.199 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 20:12:07.247 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 20:12:07.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 20:12:07.327 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 20:12:07.356 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 20:12:07.414 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 20:12:07.446 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 20:12:07.478 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 20:12:07.511 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 20:12:07.542 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 20:12:07.574 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 20:12:07.604 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 20:12:07.639 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 20:12:07.669 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 20:12:07.701 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 20:12:07.732 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 20:12:07.775 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 20:12:07.808 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 20:12:07.838 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 20:12:07.892 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 20:12:07.986 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 20:12:08.034 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 20:12:08.118 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 20:12:08.163 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 20:12:08.196 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 20:12:08.226 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 20:12:08.253 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 20:12:08.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 20:12:08.318 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 20:12:08.378 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 20:12:08.413 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 20:12:08.464 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 20:12:08.589 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 20:12:08.628 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 20:12:09.777 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 20:12:09.811 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 20:12:09.852 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 20:12:09.891 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 20:12:09.926 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 20:12:09.964 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 20:12:10.002 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 20:12:10.059 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 20:12:10.090 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 20:12:10.124 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 20:12:10.159 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 20:12:10.209 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 20:12:10.262 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 20:12:10.300 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 20:12:10.331 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 20:12:10.389 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 20:12:10.422 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 20:12:10.454 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 20:12:10.519 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 20:12:10.556 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 20:12:10.610 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 20:12:10.686 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 20:12:10.764 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 20:12:10.909 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 20:12:10.952 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 20:12:10.991 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 20:12:11.027 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 20:12:11.061 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 20:12:11.092 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 20:12:11.155 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 20:13:01.135 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 20:13:04.383 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40033 mb of usable space. - resetting to maximum available disk space: 40033 mb
2025-08-04 20:24:59.942 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.dian.common.entity.CommonEntity".
2025-08-04 20:25:00.127 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysBillNoEntity",So @TableField will not work!
2025-08-04 20:25:00.198 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysAreaEntity",So @TableField will not work!
2025-08-04 20:25:00.255 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysEntEntity",So @TableField will not work!
2025-08-04 20:25:00.305 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysSapLogEntity",So @TableField will not work!
2025-08-04 20:25:00.389 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskEntity",So @TableField will not work!
2025-08-04 20:25:00.441 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineVisibleEntity",So @TableField will not work!
2025-08-04 20:25:00.565 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.AssiInfoEntity",So @TableField will not work!
2025-08-04 20:25:00.600 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BusDocImportLogEntity",So @TableField will not work!
2025-08-04 20:25:00.639 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassEntity",So @TableField will not work!
2025-08-04 20:25:00.687 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerEntity",So @TableField will not work!
2025-08-04 20:25:00.722 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassRolePerItemEntity",So @TableField will not work!
2025-08-04 20:25:00.758 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ClassTypeEntity",So @TableField will not work!
2025-08-04 20:25:00.792 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CurrencyEntity",So @TableField will not work!
2025-08-04 20:25:00.855 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitEntity",So @TableField will not work!
2025-08-04 20:25:00.899 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandEntity",So @TableField will not work!
2025-08-04 20:25:00.944 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemEntity",So @TableField will not work!
2025-08-04 20:25:00.980 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandItemTypeEntity",So @TableField will not work!
2025-08-04 20:25:01.012 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandTypeEntity",So @TableField will not work!
2025-08-04 20:25:01.048 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandVendorEntity",So @TableField will not work!
2025-08-04 20:25:01.082 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DocEntity",So @TableField will not work!
2025-08-04 20:25:01.131 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeEntity",So @TableField will not work!
2025-08-04 20:25:01.171 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeFileEntity",So @TableField will not work!
2025-08-04 20:25:01.203 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DrawingChangeVendorEntity",So @TableField will not work!
2025-08-04 20:25:01.249 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ErpInterFaceLogsEntity",So @TableField will not work!
2025-08-04 20:25:01.283 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventItemEntity",So @TableField will not work!
2025-08-04 20:25:01.319 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.EventListEntity",So @TableField will not work!
2025-08-04 20:25:01.364 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsBarcodeEntity",So @TableField will not work!
2025-08-04 20:25:01.462 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelEntity",So @TableField will not work!
2025-08-04 20:25:01.493 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.InspectionPersonnelItemEntity",So @TableField will not work!
2025-08-04 20:25:01.526 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgLogEntity",So @TableField will not work!
2025-08-04 20:25:01.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MsgSetingEntity",So @TableField will not work!
2025-08-04 20:25:01.594 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.OperLogEntity",So @TableField will not work!
2025-08-04 20:25:01.629 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PayTypeEntity",So @TableField will not work!
2025-08-04 20:25:01.665 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneEntity",So @TableField will not work!
2025-08-04 20:25:01.695 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintSceneFieldEntity",So @TableField will not work!
2025-08-04 20:25:01.724 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.PrintTemplateEntity",So @TableField will not work!
2025-08-04 20:25:01.763 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityConfigEntity",So @TableField will not work!
2025-08-04 20:25:01.824 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.RateEntity",So @TableField will not work!
2025-08-04 20:25:01.865 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleEntity",So @TableField will not work!
2025-08-04 20:25:01.900 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandEntity",So @TableField will not work!
2025-08-04 20:25:01.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandItemEntity",So @TableField will not work!
2025-08-04 20:25:01.972 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.UomEntity",So @TableField will not work!
2025-08-04 20:25:02.003 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorContactsEntity",So @TableField will not work!
2025-08-04 20:25:02.038 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorCustomerEntity",So @TableField will not work!
2025-08-04 20:25:02.152 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorEntity",So @TableField will not work!
2025-08-04 20:25:02.209 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorExitEntity",So @TableField will not work!
2025-08-04 20:25:02.239 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorGoodsEntity",So @TableField will not work!
2025-08-04 20:25:02.281 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeEntity",So @TableField will not work!
2025-08-04 20:25:02.311 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorNoticeItemEntity",So @TableField will not work!
2025-08-04 20:25:02.339 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorRectEntity",So @TableField will not work!
2025-08-04 20:25:02.370 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewEntity",So @TableField will not work!
2025-08-04 20:25:02.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.VendorReviewItemEntity",So @TableField will not work!
2025-08-04 20:25:02.481 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehouseEntity",So @TableField will not work!
2025-08-04 20:25:02.527 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.WarehousePositionEntity",So @TableField will not work!
2025-08-04 20:25:02.560 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.ConfigEntity",So @TableField will not work!
2025-08-04 20:25:02.591 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MailEntity",So @TableField will not work!
2025-08-04 20:25:02.619 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.NoticeEntity",So @TableField will not work!
2025-08-04 20:25:02.652 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.oss.entity.SysOssEntity",So @TableField will not work!
2025-08-04 20:25:02.689 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysConfigEntity",So @TableField will not work!
2025-08-04 20:25:02.729 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDeptEntity",So @TableField will not work!
2025-08-04 20:25:02.801 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysDistEntity",So @TableField will not work!
2025-08-04 20:25:02.861 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterEntity",So @TableField will not work!
2025-08-04 20:25:02.893 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysLogEntity",So @TableField will not work!
2025-08-04 20:25:02.923 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntity",So @TableField will not work!
2025-08-04 20:25:02.973 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMenuEntEntity",So @TableField will not work!
2025-08-04 20:25:03.002 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgSetingEntity",So @TableField will not work!
2025-08-04 20:25:03.042 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostEntity",So @TableField will not work!
2025-08-04 20:25:03.070 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPostStaffEntity",So @TableField will not work!
2025-08-04 20:25:03.096 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneEntity",So @TableField will not work!
2025-08-04 20:25:03.122 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintSceneFieldEntity",So @TableField will not work!
2025-08-04 20:25:03.148 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysPrintTemplateEntity",So @TableField will not work!
2025-08-04 20:25:03.183 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleEntity",So @TableField will not work!
2025-08-04 20:25:03.221 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleFilterEntity",So @TableField will not work!
2025-08-04 20:25:03.261 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysRoleMenuEntity",So @TableField will not work!
2025-08-04 20:25:03.292 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserEntity",So @TableField will not work!
2025-08-04 20:25:03.320 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserExtEntity",So @TableField will not work!
2025-08-04 20:25:03.353 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserRoleEntity",So @TableField will not work!
2025-08-04 20:25:03.380 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserTableEntity",So @TableField will not work!
2025-08-04 20:25:03.450 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurEntity",So @TableField will not work!
2025-08-04 20:25:03.484 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.ChangePurItemEntity",So @TableField will not work!
2025-08-04 20:25:03.518 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.FollowEntity",So @TableField will not work!
2025-08-04 20:25:03.548 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.MaterialListEntity",So @TableField will not work!
2025-08-04 20:25:03.634 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurEntity",So @TableField will not work!
2025-08-04 20:25:03.732 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurItemEntity",So @TableField will not work!
2025-08-04 20:25:03.805 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurPlanItemEntity",So @TableField will not work!
2025-08-04 20:25:03.847 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestEntity",So @TableField will not work!
2025-08-04 20:25:03.878 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.PurRequestItemEntity",So @TableField will not work!
2025-08-04 20:25:04.004 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.AbnormalEntity",So @TableField will not work!
2025-08-04 20:25:04.073 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.CompromiseItemEntity",So @TableField will not work!
2025-08-04 20:25:04.116 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultEntity",So @TableField will not work!
2025-08-04 20:25:04.146 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DefaultItemEntity",So @TableField will not work!
2025-08-04 20:25:04.264 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryEntity",So @TableField will not work!
2025-08-04 20:25:04.344 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryItemEntity",So @TableField will not work!
2025-08-04 20:25:04.403 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanEntity",So @TableField will not work!
2025-08-04 20:25:04.498 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryPlanItemEntity",So @TableField will not work!
2025-08-04 20:25:04.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.DeliveryShoppingCartEntity",So @TableField will not work!
2025-08-04 20:25:04.577 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ImgEntity",So @TableField will not work!
2025-08-04 20:25:04.625 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionReviewEntity",So @TableField will not work!
2025-08-04 20:25:04.687 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetEntity",So @TableField will not work!
2025-08-04 20:25:04.736 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InspectionSheetItemEntity",So @TableField will not work!
2025-08-04 20:25:04.778 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryEntity",So @TableField will not work!
2025-08-04 20:25:04.811 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutEntity",So @TableField will not work!
2025-08-04 20:25:04.843 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.InventoryOutItemEntity",So @TableField will not work!
2025-08-04 20:25:04.960 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterEntity",So @TableField will not work!
2025-08-04 20:25:05.082 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.MasterItemEntity",So @TableField will not work!
2025-08-04 20:25:05.169 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickEntity",So @TableField will not work!
2025-08-04 20:25:05.203 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutPickDetailEntity",So @TableField will not work!
2025-08-04 20:25:05.240 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleEntity",So @TableField will not work!
2025-08-04 20:25:05.285 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.OutsourcingSingleItemEntity",So @TableField will not work!
2025-08-04 20:25:05.313 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.ProcurementPlanEntity",So @TableField will not work!
2025-08-04 20:25:05.355 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackItemEntity",So @TableField will not work!
2025-08-04 20:25:05.393 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackListEntity",So @TableField will not work!
2025-08-04 20:25:05.431 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectEntity",So @TableField will not work!
2025-08-04 20:25:05.462 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.RejectItemEntity",So @TableField will not work!
2025-08-04 20:25:05.506 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetHeadEntity",So @TableField will not work!
2025-08-04 20:25:05.543 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetItemEntity",So @TableField will not work!
2025-08-04 20:25:05.580 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SheetLineEntity",So @TableField will not work!
2025-08-04 20:25:05.620 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketEntity",So @TableField will not work!
2025-08-04 20:25:05.662 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TicketReductionEntity",So @TableField will not work!
2025-08-04 20:25:05.700 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResEntity",So @TableField will not work!
2025-08-04 20:25:05.734 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResFileEntity",So @TableField will not work!
2025-08-04 20:25:05.767 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.TrialProdResItemEntity",So @TableField will not work!
2025-08-04 20:25:05.814 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorInvEntity",So @TableField will not work!
2025-08-04 20:25:05.855 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvEntity",So @TableField will not work!
2025-08-04 20:25:05.892 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorLogicInvLogEntity",So @TableField will not work!
2025-08-04 20:25:05.921 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.VendorWarrantyEntity",So @TableField will not work!
2025-08-04 20:25:05.952 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchEntity",So @TableField will not work!
2025-08-04 20:25:05.981 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountBatchLineEntity",So @TableField will not work!
2025-08-04 20:25:06.045 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountEntity",So @TableField will not work!
2025-08-04 20:25:06.085 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountItemEntity",So @TableField will not work!
2025-08-04 20:25:06.126 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.AccountPriceEntity",So @TableField will not work!
2025-08-04 20:25:06.158 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceBillEntity",So @TableField will not work!
2025-08-04 20:25:06.192 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceEntity",So @TableField will not work!
2025-08-04 20:25:06.229 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoiceItemEntity",So @TableField will not work!
2025-08-04 20:25:06.264 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.InvoicePaymentEntity",So @TableField will not work!
2025-08-04 20:25:06.336 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecItemEntity",So @TableField will not work!
2025-08-04 20:25:06.369 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PaymentRecordEntity",So @TableField will not work!
2025-08-04 20:25:06.403 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayEntity",So @TableField will not work!
2025-08-04 20:25:06.433 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.PrepayItemEntity",So @TableField will not work!
2025-08-04 20:25:06.557 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsEntity",So @TableField will not work!
2025-08-04 20:25:06.611 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sm.entity.StatementsItemEntity",So @TableField will not work!
2025-08-04 20:25:06.651 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidHistoryEntity",So @TableField will not work!
2025-08-04 20:25:06.686 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanEntity",So @TableField will not work!
2025-08-04 20:25:06.720 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanDocEntity",So @TableField will not work!
2025-08-04 20:25:06.750 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanItemEntity",So @TableField will not work!
2025-08-04 20:25:06.787 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BidPlanVendorEntity",So @TableField will not work!
2025-08-04 20:25:06.824 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.BulkRawMaterialEntity",So @TableField will not work!
2025-08-04 20:25:06.856 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractClauseEntity",So @TableField will not work!
2025-08-04 20:25:06.884 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractEntity",So @TableField will not work!
2025-08-04 20:25:06.915 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptOperatorEntity",So @TableField will not work!
2025-08-04 20:25:06.944 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractDeptSealPositionEntity",So @TableField will not work!
2025-08-04 20:25:06.972 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ContractItemEntity",So @TableField will not work!
2025-08-04 20:25:07.009 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryEntity",So @TableField will not work!
2025-08-04 20:25:07.062 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryItemEntity",So @TableField will not work!
2025-08-04 20:25:07.098 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.EnquiryVendorEntity",So @TableField will not work!
2025-08-04 20:25:07.143 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorEntity",So @TableField will not work!
2025-08-04 20:25:07.173 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GoodsVendorItemEntity",So @TableField will not work!
2025-08-04 20:25:07.204 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GradeDifferenceEntity",So @TableField will not work!
2025-08-04 20:25:07.259 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjEntity",So @TableField will not work!
2025-08-04 20:25:07.291 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ImPriceAdjDetailEntity",So @TableField will not work!
2025-08-04 20:25:07.324 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialBasisEntity",So @TableField will not work!
2025-08-04 20:25:07.375 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyEntity",So @TableField will not work!
2025-08-04 20:25:07.414 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceArchivesEntity",So @TableField will not work!
2025-08-04 20:25:07.449 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceContractFileEntity",So @TableField will not work!
2025-08-04 20:25:07.505 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelEntity",So @TableField will not work!
2025-08-04 20:25:07.546 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPricelItemEntity",So @TableField will not work!
2025-08-04 20:25:07.593 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanEntity",So @TableField will not work!
2025-08-04 20:25:07.626 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MonthlyPlanItemEntity",So @TableField will not work!
2025-08-04 20:25:07.737 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjEntity",So @TableField will not work!
2025-08-04 20:25:07.772 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceAdjLineEntity",So @TableField will not work!
2025-08-04 20:25:07.806 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelChangeEntity",So @TableField will not work!
2025-08-04 20:25:07.840 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelEntity",So @TableField will not work!
2025-08-04 20:25:07.874 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelHistoryEntity",So @TableField will not work!
2025-08-04 20:25:07.906 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineChangeEntity",So @TableField will not work!
2025-08-04 20:25:07.936 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineEntity",So @TableField will not work!
2025-08-04 20:25:07.974 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceModelLineHistoryEntity",So @TableField will not work!
2025-08-04 20:25:08.011 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrChangeEntity",So @TableField will not work!
2025-08-04 20:25:08.044 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterAttrEntity",So @TableField will not work!
2025-08-04 20:25:08.076 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterChangeEntity",So @TableField will not work!
2025-08-04 20:25:08.117 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterEntity",So @TableField will not work!
2025-08-04 20:25:08.148 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsChangeEntity",So @TableField will not work!
2025-08-04 20:25:08.180 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceParameterGoodsEntity",So @TableField will not work!
2025-08-04 20:25:08.232 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.PriceTranEntity",So @TableField will not work!
2025-08-04 20:25:08.274 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationEntity",So @TableField will not work!
2025-08-04 20:25:08.310 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.QuotationItemEntity",So @TableField will not work!
2025-08-04 20:25:08.373 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioEntity",So @TableField will not work!
2025-08-04 20:25:08.412 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioItemEntity",So @TableField will not work!
2025-08-04 20:25:08.450 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioLogsEntity",So @TableField will not work!
2025-08-04 20:25:08.481 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleEntity",So @TableField will not work!
2025-08-04 20:25:08.508 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioRuleItemEntity",So @TableField will not work!
2025-08-04 20:25:08.537 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradEntity",So @TableField will not work!
2025-08-04 20:25:08.571 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.TradItemEntity",So @TableField will not work!
2025-08-04 20:25:08.621 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ConfEntity",So @TableField will not work!
2025-08-04 20:25:08.657 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.ModelEntity",So @TableField will not work!
2025-08-04 20:25:08.708 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordEntity",So @TableField will not work!
2025-08-04 20:25:08.804 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.ach.entity.RecordItemEntity",So @TableField will not work!
2025-08-04 20:25:08.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysOpenLogEntity",So @TableField will not work!
2025-08-04 20:25:09.966 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskOperatorRecordEntity",So @TableField will not work!
2025-08-04 20:25:10.001 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowTaskCirculateEntity",So @TableField will not work!
2025-08-04 20:25:10.043 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowEngineEntity",So @TableField will not work!
2025-08-04 20:25:10.083 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.module.flow.entity.FlowDelegateEntity",So @TableField will not work!
2025-08-04 20:25:10.156 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.BankEntity",So @TableField will not work!
2025-08-04 20:25:10.198 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.CompanyEntity",So @TableField will not work!
2025-08-04 20:25:10.241 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DemandCommitItemEntity",So @TableField will not work!
2025-08-04 20:25:10.299 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsDeptEntity",So @TableField will not work!
2025-08-04 20:25:10.330 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsUomConvertEntity",So @TableField will not work!
2025-08-04 20:25:10.361 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.GoodsWarehouseEntity",So @TableField will not work!
2025-08-04 20:25:10.398 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldBasicMoldInfoEntity",So @TableField will not work!
2025-08-04 20:25:10.439 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.MoldGoodsComparisonaEntity",So @TableField will not work!
2025-08-04 20:25:10.487 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.QualityVendorEntity",So @TableField will not work!
2025-08-04 20:25:10.526 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleDemandVendorEntity",So @TableField will not work!
2025-08-04 20:25:10.558 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.SampleItemEntity",So @TableField will not work!
2025-08-04 20:25:10.617 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.base.entity.DistEntity",So @TableField will not work!
2025-08-04 20:25:10.651 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobEntity",So @TableField will not work!
2025-08-04 20:25:10.688 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.job.entity.ScheduleJobLogEntity",So @TableField will not work!
2025-08-04 20:25:10.756 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysFilterItemEntity",So @TableField will not work!
2025-08-04 20:25:10.793 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysMsgTemplateEntity",So @TableField will not work!
2025-08-04 20:25:10.836 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.sys.entity.SysUserDeptEntity",So @TableField will not work!
2025-08-04 20:25:10.923 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.order.entity.OfflineWarehousingHistoryEntity",So @TableField will not work!
2025-08-04 20:25:10.998 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.dm.entity.SupplyBlackArchivesEntity",So @TableField will not work!
2025-08-04 20:25:11.099 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.GearPositionEntity",So @TableField will not work!
2025-08-04 20:25:11.141 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceApplyItemEntity",So @TableField will not work!
2025-08-04 20:25:11.182 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.MaterialPriceSapInfoRecordEntity",So @TableField will not work!
2025-08-04 20:25:11.217 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterAttrHistoryEntity",So @TableField will not work!
2025-08-04 20:25:11.250 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterGoodsHistoryEntity",So @TableField will not work!
2025-08-04 20:25:11.282 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.ParameterHistoryEntity",So @TableField will not work!
2025-08-04 20:25:11.346 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - This "id" is the table primary key by default name for `id` in Class: "com.dian.modules.im.entity.SupplyRatioSapInfoRecordEntity",So @TableField will not work!
2025-08-04 20:25:59.371 [main] WARN  com.xxl.job.core.executor.XxlJobExecutor - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
2025-08-04 20:26:02.519 [main] WARN  org.apache.activemq.broker.BrokerService - Temporary Store limit is 51200 mb (current store usage is 0 mb). The data directory: E:\Desktop\srm\srm-backend only has 40033 mb of usable space. - resetting to maximum available disk space: 40033 mb
2025-08-04 20:26:33.310 [registrationTask1] WARN  d.c.b.a.client.registration.ApplicationRegistrator - Failed to register application as Application(name=base-appliction, managementUrl=http://127.0.0.1:8888/api/actuator, healthUrl=http://127.0.0.1:8888/api/actuator/health, serviceUrl=http://127.0.0.1:8888/api) at spring-boot-admin ([http://127.0.0.1:9050/instances]): I/O error on POST request for "http://127.0.0.1:9050/instances": Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:9050 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-08-04 21:33:51.926 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-04 22:57:29.553 [nioEventLoopGroup-4-1] WARN  io.netty.channel.DefaultChannelPipeline - An exceptionCaught() event was fired, and it reached at the tail of the pipeline. It usually means the last handler in the pipeline did not handle the exception.
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
