/**
 * Copyright (c) 2016-2019 九点科技 All rights reserved.
 *
 * http://www.9dyun.cn
 *
 * 版权所有，侵权必究！
 */
package com.dian.modules.dm.controller;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.dian.common.annotation.SysLog;
import com.dian.modules.dm.entity.DeliveryPlanItemEntity;
import com.dian.modules.dm.service.DeliveryItemService;
import com.dian.modules.dm.service.DeliveryPlanItemService;
import com.dian.modules.dm.verify.DeliveryPlanImportExcelVerifyHandler;
import com.dian.modules.dm.verify.NewDeliveryPlanImportExcelVerifyHandler;
import com.dian.modules.dm.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;
import com.dian.common.controller.AbstractController;
import com.dian.modules.dm.entity.DeliveryPlanEntity;
import com.dian.modules.dm.service.DeliveryPlanService;
import com.dian.common.utils.PageUtils;
import com.dian.common.utils.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;


/**
 * 送货计划明细
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-10 10:56:26
 */
@RestController
@Api("送货计划明细接口")
@RequestMapping("dm/deliveryPlan")
public class DeliveryPlanController extends AbstractController{
    @Autowired
    private DeliveryPlanService deliveryPlanService;

    @Autowired
    private DeliveryPlanItemService deliveryPlanItemService;

    @Autowired
    private DeliveryPlanImportExcelVerifyHandler deliveryPlanImportExcelVerifyHandler;

    @Autowired
    private NewDeliveryPlanImportExcelVerifyHandler newDeliveryPlanImportExcelVerifyHandler;

    /**
     * 送货计划明细列表
     */
    @SysLog("送货计划明细列表")
    @GetMapping("/list")
    @ApiOperation("送货计划明细列表")
    @RequiresPermissions("dm:deliveryPlan:list")
    public R list(@RequestParam Map<String, Object> params){

        PageUtils page = deliveryPlanService.queryPage(params);

        return R.ok(page);
    }


    /**
     * 送货计划明细信息
     */
    @SysLog("送货计划明细信息")
    @ApiOperation("送货计划明细信息")
    @GetMapping("/info/{id}")
    @RequiresPermissions("dm:deliveryPlan:info")
    public R info(@PathVariable("id") Long id){

        DeliveryPlanEntity deliveryPlan = deliveryPlanService.getInfo(id);

        return R.ok().put(deliveryPlan);
    }

    @SysLog("送货计划明细信息")
    @ApiOperation("送货计划明细信息")
    @GetMapping("/itemInfo/{id}")
    @RequiresPermissions("dm:deliveryPlan:info")
    public R itemInfo(@PathVariable("id") Long id){

        DeliveryPlanItemEntity info = deliveryPlanItemService.getInfo(id);

        return R.ok(info);
    }

    /**
     * 送货计划明细保存
     */
    @SysLog("送货计划明细保存")
    @ApiOperation("送货计划明细保存")
    @PostMapping("/save")
    @RequiresPermissions("dm:DeliveryPlan:save")
    public R save(@RequestBody DeliveryPlanEntity deliveryPlan){

		deliveryPlanService.saveInfo(deliveryPlan);

        return R.ok();
    }

    /**
     * 送货计划明细修改
     */
    @SysLog("送货计划明细修改")
    @ApiOperation("送货计划明细修改")
    @PostMapping("/update")
    @RequiresPermissions("dm:deliveryPlan:update")
    public R update(@RequestBody DeliveryPlanEntity deliveryPlan){

		deliveryPlanService.updateInfo(deliveryPlan);

        return R.ok();
    }

    /**
     * 送货计划明细删除
     */
    @SysLog("送货计划明细删除")
    @ApiOperation("送货计划明细删除")
    @PostMapping("/delete")
    @RequiresPermissions("dm:deliveryPlan:delete")
    public R delete(@RequestBody Long[] ids){

		deliveryPlanService.deleteInfo(ids);

        return R.ok();
    }


    /**
     * 送货计划明细审核
     */
    @SysLog("送货计划明细审核")
    @ApiOperation("送货计划明细审核")
    @GetMapping("/check/{id}")
    @RequiresPermissions("dm:deliveryPlan:check")
    public R check(@PathVariable("id") Long id){

        deliveryPlanService.checkInfo(id);

        return R.ok();
    }


    /**
     * 送货计划明细导出
     * meng
     */
    @SysLog("送货计划明细导出")
    @ApiOperation("送货计划明细导出")
    @GetMapping("/export")
    @RequiresPermissions("dm:deliveryPlan:export")
    public void export(@RequestParam Map<String, Object> params){

        List<DeliveryPlanAllExportVO> list = deliveryPlanService.exportList(params);

        exportExcel(list,"送货计划明细","送货计划明细",DeliveryPlanAllExportVO.class,"送货计划明细.xlsx");
    }
    /**
     *查询采购订单明细对应送货计划占用数
     */
    @SysLog("查询采购订单明细对应送货计划占用数")
    @ApiOperation("查询采购订单明细对应送货计划占用数")
    @GetMapping("/qualityByPlan")
    @RequiresPermissions("sys:task:run")
    public BigDecimal  qualityByPlan(@RequestParam("saleId")Long saleId, @RequestParam("saleItemId")Long saleItemId){
        return deliveryPlanService.qualityByPlan(saleId,saleItemId);

    }

    /**
     *查询采购订单明细对应送货计划占用
     */
    @SysLog("查询采购订单明细对应送货计划占用")
    @ApiOperation("查询采购订单明细对应送货计划占用")
    @GetMapping("/qualityByPlanlist")
    @RequiresPermissions("sys:task:run")
    public boolean  qualityByPlanlist(@RequestParam("saleId")Long saleId, @RequestParam("saleItemId")Long saleItemId){
        return deliveryPlanService.qualityByPlanlist(saleId,saleItemId);

    }    /**
     *查询采购订单明细
     */
    @SysLog("查询采购订单明细")
    @ApiOperation("查询采购订单明细")
    @GetMapping("/qualityByList")
    @RequiresPermissions("sys:task:run")
    public List<DeliveryPlanItemVO>  qualityByList(@RequestParam("saleId")Long saleId, @RequestParam("saleItemId")Long saleItemId){
        return deliveryPlanService.qualityByList(saleId,saleItemId);

    }

    /**
     *送货计划接口删除
     */
    @SysLog("送货计划接口删除")
    @ApiOperation("送货计划接口删除")
    @PostMapping("/deliveryPlanDelete")
    @RequiresPermissions("sys:task:run")
    public void deliveryPlanDelete(@RequestBody List<DeliveryPlanVO> list){

        deliveryPlanService.deliveryPlanDelete(list);

    }

    /**
     * 下载送货计划导入模版 - 动态生成
     * @param params
     * @param response
     */
    @GetMapping("/downloadPlanTemplate")
    public void downloadPlanTemplate(@RequestParam Map<String, Object> params,HttpServletResponse response){
        deliveryPlanService.downloadPlanTemplate(params,response);
    }

    @PostMapping("/importPlan")
    @RequiresPermissions("dm:deliveryPlan:deliveryPlanImport")
    public R importPlan(@RequestParam("file") MultipartFile file) throws Exception{
        ImportParams params = new ImportParams();
        params.setNeedVerify(true);
        params.setVerifyHandler(newDeliveryPlanImportExcelVerifyHandler);
        ExcelImportResult<Object> objectExcelImportResult = ExcelImportUtil.importExcelMore(file.getInputStream(), Map.class, params);
        if (objectExcelImportResult.isVerifyFail()) {
            Map<String,Object> map = new HashMap<>();
            map.put("errorFileName","送货计划动态导入失败数据.xlsx");
            objectExcelImportResult.setMap(map);
            //导出失败的excel 必须结合前端d-excel-import标签使用
            this.downLoadFailExcel(objectExcelImportResult);
            return R.ok();
        } else {
            if (CollectionUtils.isNotEmpty(objectExcelImportResult.getList())) {
                List<AfterImportErrorPlanVO> afterImportExportErrorPlanList = deliveryPlanService.importDeliveryPlan(objectExcelImportResult.getList());
                //afterImportExportErrorPlanList - 导入送货计划失败错误信息数据集合
                //afterImportExportErrorPlanList不为空则下载文件
                if(CollectionUtil.isNotEmpty(afterImportExportErrorPlanList)){
                    exportExcel(afterImportExportErrorPlanList,"导入失败送货计划数据","导入失败送货计划数据", AfterImportErrorPlanVO.class,"导入失败送货计划数据.xlsx");
                }
            }
            return R.ok();
        }
    }

    /**
     * 送货计划动态交期数据分页查询
     * @param params
     * @return
     */
    @GetMapping("/queryDyanPlanRelyDataPage")
//    @RequiresPermissions("dm:deliveryPlan:queryDyanPlanRelyData")
    public R queryDyanPlanRelyDataPage(@RequestParam Map<String, Object> params){
        return R.ok(deliveryPlanService.queryDyanPlanRelyDataPage(params));
    }

    /**
     * 导出送货计划交期动态数据
     * @param params
     * @param response
     */
    @GetMapping("/exportDynaPlanReplyData")
    public void exportDynaPlanReplyData(@RequestParam Map<String, Object> params,HttpServletResponse response){
        deliveryPlanService.exportDynaPlanReplyData(params, response);
    }

    /**
     * 批量导入回复送货计划
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importDynaPlanReplyData")
    @RequiresPermissions("dm:deliveryPlan:deliveryPlanImport")
    public R importDynaPlanReplyData(@RequestParam("file") MultipartFile file) throws Exception{
        ImportParams params = new ImportParams();
        params.setNeedVerify(false);
        ExcelImportResult<Object> objectExcelImportResult = ExcelImportUtil.importExcelMore(file.getInputStream(), Map.class, params);
        if (objectExcelImportResult.isVerifyFail()) {
            Map<String,Object> map = new HashMap<>();
            map.put("errorFileName","送货计划交期回复导入失败数据.xlsx");
            objectExcelImportResult.setMap(map);
            //导出失败的excel 必须结合前端d-excel-import标签使用
            this.downLoadFailExcel(objectExcelImportResult);
            return R.ok();
        } else {
            if (CollectionUtils.isNotEmpty(objectExcelImportResult.getList())) {
                List<AfterImportErrorPlanVO> afterImportExportErrorPlanList = deliveryPlanService.importDynaPlanReplyData(objectExcelImportResult.getList());
                //afterImportExportErrorPlanList - 导入送货计划失败错误信息数据集合
                //afterImportExportErrorPlanList不为空则下载文件
                if(CollectionUtil.isNotEmpty(afterImportExportErrorPlanList)){
                    exportExcel(afterImportExportErrorPlanList,"导入失败送货计划数据","导入失败送货计划数据", AfterImportErrorPlanVO.class,"导入失败送货计划数据.xlsx");
                }
            }
            return R.ok();
        }
    }

    /**
     * 送货计划导入
     * 2021-4-10
     * meng
     */
    @SysLog("送货计划导入")
    @ApiOperation("送货计划导入")
    @PostMapping("/deliveryPlanImport")
    @RequiresPermissions("dm:deliveryPlan:deliveryPlanImport")
    public R deliveryPlanImport(@RequestParam("file") MultipartFile file){
        ExcelImportResult<DeliveryPlanImportVO> excelImportResult = this.importExcel(file, DeliveryPlanImportVO.class,deliveryPlanImportExcelVerifyHandler);
        if (CollectionUtils.isNotEmpty(excelImportResult.getList())) {
            deliveryPlanService.importNew(excelImportResult.getList());
        }
        if (excelImportResult.isVerifyFail()) {
            //导出失败的excel 必须结合前端d-excel-import标签使用
            this.downLoadFailExcel(excelImportResult);
            return R.ok();
        } else {
            return R.ok();
        }
    }

    /**
     * 下载送货计划导入模板
     * meng
     * 2021-4-10
     * @param params
     * @return
     */
    @SysLog("下载送货计划导入模板")
    @ApiOperation("下载送货计划导入模板")
    @GetMapping("/importModel")
    @RequiresPermissions("dm:deliveryPlan:importModel")
    public void importModel(@RequestParam Map<String, Object> params){

        List<DeliveryPlanImportVO> list = deliveryPlanService.importModel(params);

        exportExcel(list,"送货计划","送货计划", DeliveryPlanImportVO.class,"送货计划.xlsx");
    }

    @GetMapping("/findDeliveryPlan")
    public R findDeliveryPlan(@RequestParam Map<String,Object> params){
        PageUtils page = deliveryPlanService.findDeliveryPlan(params);
        return R.ok().put("page",page);
    }

    @GetMapping("/findDeliveryVendorPlan")
    public R findDeliveryVendorPlan(@RequestParam Map<String,Object> params){
        PageUtils page = deliveryPlanService.findDeliveryVendorPlan(params);
        return R.ok().put("page",page);
    }

    /**
     * 根据最新的计划明细更改原计划明细行已匹配数量
     * @param deliveryPlanItemEntity
     * @return
     */
    @PostMapping("/updatePlanItemMatchNumByNowPlanItem")
    public R updatePlanItemMatchNumByNowPlanItem(@RequestBody DeliveryPlanItemEntity deliveryPlanItemEntity){
        Long id = deliveryPlanItemService.updatePlanItemMatchNumByNowPlanItem(deliveryPlanItemEntity);
        return R.ok(id);
    }

    /**
     * 根据id关闭计划明细行
     * @param id
     * @return
     */
    @GetMapping("/closePlanItemById/{id}")
    public R closePlanItemById(@PathVariable("id") Long id){
        return R.ok(deliveryPlanItemService.closePlanItemById(id));
    }

    @PostMapping("/closePlanItemByIds")
    public R closePlanItemByIds(@RequestBody List<Long> ids){
        return R.ok(deliveryPlanItemService.closePlanItemByIds(ids));
    }

    @PostMapping("/batchConfirmPlanByVendor")
    public R batchConfirmPlanByVendor(@RequestBody List<DeliveryPlanEntity> list){
        return R.ok(deliveryPlanService.batchConfirmPlanByVendor(list));
    }
}
